#ifndef AC_DATA_PARSER_H
#define AC_DATA_PARSER_H

#include <stdint.h>
#include <stdio.h>
#include <string.h>

#ifdef __cplusplus
extern "C" {
#endif

// 空调数据结构定义
typedef struct {
    uint16_t temperature;           // 温度 (2B)
    uint16_t set_temperature;       // 设定温度 (2B)
    uint8_t  mode;                  // 模式 (1B)
    uint8_t  week_day;              // 星期 (1B)
    uint8_t  week;                  // 周 (1B)
    uint8_t  hour;                  // 时 (1B)
    uint8_t  minute;                // 分 (1B)
    uint8_t  power_switch;          // 开关 (1B)
    uint8_t  high_wind;             // 高风 (1B)
    uint8_t  medium_wind;           // 中风 (1B)
    uint8_t  low_wind;              // 低风 (1B)
    uint8_t  cold_valve;            // 冷阀 (1B)
    uint8_t  hot_valve;             // 热阀 (1B)
    uint16_t humidity;              // 湿度 (2B)
    uint8_t  wind_speed_auto_flag;  // 风速自动标志 (1B)
    uint8_t  gid;                   // GID (1B)
    uint8_t  oid;                   // OID (1B)
    uint8_t  iid;                   // IID (1B)
    uint8_t  lock;                  // LOCK (1B)
    uint8_t  onoff_pri;             // 21-onoff pri. (1B)
    uint8_t  set_temp_pri;          // 22-set temp pri. (1B)
    uint8_t  set_mode_pri;          // 23-set mode pri. (1B)
    uint8_t  set_wspeed_pri;        // 24-set wspeed pri. (1B)
} ac_data_t;

// 空调模式枚举
typedef enum {
    AC_MODE_AUTO = 0,
    AC_MODE_COOL = 1,
    AC_MODE_HEAT = 2,
    AC_MODE_FAN = 3,
    AC_MODE_DRY = 4
} ac_mode_e;

// 函数声明
int parse_ac_data(const uint8_t *data, uint16_t data_len, ac_data_t *ac_data);
void print_ac_data(const ac_data_t *ac_data);
const char* get_ac_mode_string(uint8_t mode);
const char* get_weekday_string(uint8_t weekday);

#ifdef __cplusplus
}
#endif

#endif // AC_DATA_PARSER_H
