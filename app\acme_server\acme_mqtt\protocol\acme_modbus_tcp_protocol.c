#include "pthread.h"
#include <nng/nng.h>
#include <jansson.h>
#include <memory.h>
#include <signal.h>
#include <stdbool.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "neuron.h"


#include "acme_modbus_tcp_protocol.h"





/*
* 功能：modbus tcp tag 新点位上报组包
*/
json_t * acme_modbus_tcp_new_tag_encode()
{

}

/*
* 功能：modbus tcp tag 点位状态上报组包
*/
json_t * acme_modbus_tcp_tag_sync_encode()
{

}






