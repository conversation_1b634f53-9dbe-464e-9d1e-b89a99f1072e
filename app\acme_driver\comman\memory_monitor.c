/**
 * NEURON IIoT System for Industry 4.0
 * Copyright (C) 2020-2024 EMQ Technologies Co., Ltd All rights reserved.
 *
 * 内存监控和泄漏检测工具实现
 **/

#include "memory_monitor.h"
#include <pthread.h>

// 全局内存监控器实例
memory_monitor_t g_mem_monitor = {0};
static pthread_mutex_t monitor_mutex = PTHREAD_MUTEX_INITIALIZER;

/**
 * 初始化内存监控器
 * @param log_file_path 日志文件路径，如果为NULL则输出到stdout
 * @return 0成功，-1失败
 */
int memory_monitor_init(const char *log_file_path)
{
    pthread_mutex_lock(&monitor_mutex);
    
    memset(&g_mem_monitor, 0, sizeof(g_mem_monitor));
    g_mem_monitor.enabled = MEM_MONITOR_ENABLED;
    g_mem_monitor.stats.last_check = time(NULL);
    g_mem_monitor.stats.last_rss = get_process_memory_usage();
    
    if (log_file_path != NULL) {
        g_mem_monitor.log_file = fopen(log_file_path, "a");
        if (g_mem_monitor.log_file == NULL) {
            printf("[MEM_MONITOR] Warning: Failed to open log file %s, using stdout\n", log_file_path);
            g_mem_monitor.log_file = stdout;
        }
    } else {
        g_mem_monitor.log_file = stdout;
    }
    
    // 记录初始状态
    fprintf(g_mem_monitor.log_file, "[MEM_MONITOR] %ld: Memory monitor initialized, initial RSS: %lu KB\n", 
            time(NULL), g_mem_monitor.stats.last_rss);
    fflush(g_mem_monitor.log_file);
    
    pthread_mutex_unlock(&monitor_mutex);
    return 0;
}

/**
 * 清理内存监控器
 */
void memory_monitor_cleanup(void)
{
    pthread_mutex_lock(&monitor_mutex);
    
    if (g_mem_monitor.log_file != NULL && g_mem_monitor.log_file != stdout) {
        fprintf(g_mem_monitor.log_file, "[MEM_MONITOR] %ld: Memory monitor cleanup, final stats:\n", time(NULL));
        memory_monitor_log_stats("CLEANUP");
        fclose(g_mem_monitor.log_file);
    }
    
    memset(&g_mem_monitor, 0, sizeof(g_mem_monitor));
    pthread_mutex_unlock(&monitor_mutex);
}

/**
 * 获取进程内存使用量(RSS)
 * @return 内存使用量(KB)，失败返回0
 */
uint64_t get_process_memory_usage(void)
{
    FILE *file = fopen("/proc/self/status", "r");
    if (file == NULL) {
        return 0;
    }
    
    char line[256];
    uint64_t rss_kb = 0;
    
    while (fgets(line, sizeof(line), file)) {
        if (strncmp(line, "VmRSS:", 6) == 0) {
            sscanf(line, "VmRSS: %lu kB", &rss_kb);
            break;
        }
    }
    
    fclose(file);
    return rss_kb;
}

/**
 * 检查内存使用情况
 * @param context 上下文信息，用于标识检查点
 */
void memory_monitor_check(const char *context)
{
    if (!g_mem_monitor.enabled) {
        return;
    }
    
    pthread_mutex_lock(&monitor_mutex);
    
    time_t now = time(NULL);
    uint64_t current_rss = get_process_memory_usage();
    
    // 计算内存变化
    int64_t rss_delta = (int64_t)current_rss - (int64_t)g_mem_monitor.stats.last_rss;
    
    // 如果内存增长超过阈值或者达到检查间隔，记录日志
    if (abs(rss_delta) > MEM_MONITOR_LOG_THRESHOLD || 
        (now - g_mem_monitor.stats.last_check) >= MEM_MONITOR_INTERVAL) {
        
        fprintf(g_mem_monitor.log_file, 
                "[MEM_MONITOR] %ld: %s - RSS: %lu KB (delta: %+ld KB)\n",
                now, context ? context : "CHECK", current_rss, rss_delta);
        fflush(g_mem_monitor.log_file);
        
        g_mem_monitor.stats.last_check = now;
    }
    
    // 更新统计信息
    g_mem_monitor.stats.current_usage = current_rss;
    if (current_rss > g_mem_monitor.stats.peak_usage) {
        g_mem_monitor.stats.peak_usage = current_rss;
    }
    g_mem_monitor.stats.last_rss = current_rss;
    
    pthread_mutex_unlock(&monitor_mutex);
}

/**
 * 记录详细的内存统计信息
 * @param context 上下文信息
 */
void memory_monitor_log_stats(const char *context)
{
    if (!g_mem_monitor.enabled || g_mem_monitor.log_file == NULL) {
        return;
    }
    
    pthread_mutex_lock(&monitor_mutex);
    
    fprintf(g_mem_monitor.log_file, 
            "[MEM_STATS] %ld: %s\n"
            "  Current RSS: %lu KB\n"
            "  Peak RSS: %lu KB\n"
            "  Total Alloc: %lu bytes\n"
            "  Total Free: %lu bytes\n"
            "  Alloc Count: %u\n"
            "  Free Count: %u\n"
            "  Potential Leak: %ld bytes\n",
            time(NULL), context ? context : "STATS",
            g_mem_monitor.stats.current_usage,
            g_mem_monitor.stats.peak_usage,
            g_mem_monitor.stats.total_alloc,
            g_mem_monitor.stats.total_free,
            g_mem_monitor.stats.alloc_count,
            g_mem_monitor.stats.free_count,
            (int64_t)(g_mem_monitor.stats.total_alloc - g_mem_monitor.stats.total_free));
    
    fflush(g_mem_monitor.log_file);
    pthread_mutex_unlock(&monitor_mutex);
}

#ifdef DEBUG_MEMORY
/**
 * 监控版本的malloc
 */
void* monitored_malloc(size_t size, const char *file, int line)
{
    void *ptr = malloc(size);
    if (ptr != NULL) {
        pthread_mutex_lock(&monitor_mutex);
        g_mem_monitor.stats.total_alloc += size;
        g_mem_monitor.stats.alloc_count++;
        pthread_mutex_unlock(&monitor_mutex);
        
        fprintf(g_mem_monitor.log_file, 
                "[MEM_ALLOC] %ld: malloc(%zu) = %p at %s:%d\n",
                time(NULL), size, ptr, file, line);
    }
    return ptr;
}

/**
 * 监控版本的free
 */
void monitored_free(void *ptr, const char *file, int line)
{
    if (ptr != NULL) {
        pthread_mutex_lock(&monitor_mutex);
        g_mem_monitor.stats.free_count++;
        pthread_mutex_unlock(&monitor_mutex);
        
        fprintf(g_mem_monitor.log_file, 
                "[MEM_FREE] %ld: free(%p) at %s:%d\n",
                time(NULL), ptr, file, line);
    }
    free(ptr);
}

/**
 * 监控版本的calloc
 */
void* monitored_calloc(size_t count, size_t size, const char *file, int line)
{
    void *ptr = calloc(count, size);
    if (ptr != NULL) {
        size_t total_size = count * size;
        pthread_mutex_lock(&monitor_mutex);
        g_mem_monitor.stats.total_alloc += total_size;
        g_mem_monitor.stats.alloc_count++;
        pthread_mutex_unlock(&monitor_mutex);
        
        fprintf(g_mem_monitor.log_file, 
                "[MEM_ALLOC] %ld: calloc(%zu, %zu) = %p at %s:%d\n",
                time(NULL), count, size, ptr, file, line);
    }
    return ptr;
}
#endif
