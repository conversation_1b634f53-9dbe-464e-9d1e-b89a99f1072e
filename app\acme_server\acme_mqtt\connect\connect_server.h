#ifndef ACME_CONNECT_SERVER_HHHH_
#define ACME_CONNECT_SERVER_HHHH_

#include "plugin.h"
#include "utils/log.h"


// gw request register
typedef struct{
    char uid[22];  //uid
    char mName[50];
    char appkey[36];
    char g_projectID[32];  //项目ID
    int pointNum;
    char gw_fw_ver[36];
    char *p_veriyCode; //验证吗
    char registerToken[22];
}gw_register_reg_t;

// gw request register response
typedef struct{
    bool success;           //成功 or 失败
    int code;               //http 响应码
    //char *projectId;   //项目ID
    char *username; //MQTT 登录用户名
    char *password;   //MQTT 登录密码
    char *mqttAddr; //MQTT 地址
    char *time;          //系统时间
    char *msg;
}gw_register_resp_t;



//服务器连接线程
void * acme_server_connect_thread(void *arg);


#endif