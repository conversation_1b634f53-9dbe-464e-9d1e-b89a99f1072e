{{- if .Values.service.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "neuron.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "neuron.labels" . | nindent 4 }}
  {{- if eq .Values.service.type "LoadBalancer" }}
  {{- with .Values.service.annotations }}
  annotations:
  {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- end }}
spec:
  type: {{ .Values.service.type }}
  {{- if eq .Values.service.type "LoadBalancer" }}
  {{- if .Values.service.loadBalancerIP }}
  loadBalancerIP: {{ .Values.service.loadBalancerIP }}
  {{- end }}
  {{- if .Values.service.loadBalancerSourceRanges }}
  loadBalancerSourceRanges: {{- toYaml .Values.service.loadBalancerSourceRanges | nindent 4 }}
  {{- end }}
  {{- end }}
  ports:
  - name: {{ .Values.service.ports.web.name }}
    port:  {{ .Values.service.ports.web.port | default 7000 }}
    protocol: TCP
    targetPort:  web
    {{- if and (or (eq .Values.service.type "NodePort") (eq .Values.service.type "LoadBalancer")) (not (empty .Values.service.nodePorts.web)) }}
    nodePort: {{ .Values.service.nodePorts.web }}
    {{- else if eq .Values.service.type "ClusterIP" }}
    nodePort: null
    {{- end }}
  - name: {{ .Values.service.ports.api.name }}
    port: {{ .Values.service.ports.api.port | default 7001 }}
    protocol: TCP
    targetPort: api
    {{- if and (or (eq .Values.service.type "NodePort") (eq .Values.service.type "LoadBalancer")) (not (empty .Values.service.nodePorts.api)) }}
    nodePort: {{ .Values.service.nodePorts.api }}
    {{- else if eq .Values.service.type "ClusterIP" }}
    nodePort: null
    {{- end }}
  selector:
    {{- include "neuron.selectorLabels" . | nindent 4 }}
{{- end }}