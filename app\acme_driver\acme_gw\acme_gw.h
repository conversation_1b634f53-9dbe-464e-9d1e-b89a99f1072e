/**
 * NEURON IIoT System for Industry 4.0
 * Copyright (C) 2020-2024 EMQ Technologies Co., Ltd All rights reserved.
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 3 of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with this program; if not, write to the Free Software Foundation,
 * Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.
 **/

#ifndef NEURON_PLUGIN_ACME_GW_H
#define NEURON_PLUGIN_ACME_GW_H

#ifdef __cplusplus
extern "C" {
#endif

#include "neuron.h"
#include "my_ringbuffer.h"
#include "app_wavemesh.h"
#include "acme_lora_driver.h"
#include "lora_device_manager.h"
#include "acme_node.h"

#define PACK_LEN_MAX    512
#define BUFF_CASH_LEN   2048



typedef struct acme_gw
{
    uint8_t _pos;
    uint8_t _state;
    char lora_raw_data[PACK_LEN_MAX];
}tty_pack_sta_t;





struct neu_plugin {
    neu_plugin_common_t common;

    //TODO: 此处添加 网关驱动相关的定义 如硬件型号配置等参数
    //...

    //串口fd、串口配置参数
    neu_conn_t *    lora_tty_conn;
    neu_node_link_state_e tty_sta;
    neu_events_t *   tty_events ;
    neu_event_io_t *tty_recv_io;
    RingBuffer *    pMeshRcv;   
    cfgBlock     *   wm_cfg;
    tty_pack_sta_t * tty_pack_sta;

    neu_event_timer_t * pair_timer;     //网关配对定时器
    int pair_mode;                 //配对模式0:(0,0,0)  1:(1:3:3)  2:(3,4,31)

    lora_mesh_cfg        *mesh_cfg;       //网关mesh 参数信息
    
    //子设备集合 （用容器还是 哈希表）
    neu_subDevice_manager_t *  subDevice_manager;
    int                         pair_mid;        //正在配对设备
    


};



#ifdef __cplusplus
}
#endif

#endif
