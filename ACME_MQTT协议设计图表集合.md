# ACME_MQTT 协议设计图表集合

## 1. ACME_MQTT新协议实现流程图

```mermaid
sequenceDiagram
    participant Cloud as 云端平台
    participant M<PERSON><PERSON> as MQTT Broker
    participant G<PERSON> as ACME网关
    participant Device as FCM设备
    
    Note over Cloud,Device: 新协议数据读取流程
    
    Cloud->>MQTT: Publish读取请求
    Note right of Cloud: /acme/SPT_GW_001/FCM_001/read/request<br/>{"version":"1.0","message_type":"read_request",...}
    
    MQTT->>GW: 转发读取请求
    GW->>GW: 解析协议消息
    Note right of GW: acme_parse_protocol_message()
    
    GW->>GW: 处理读取请求
    Note right of GW: handle_read_request()
    
    GW->>Device: 读取设备数据
    Note right of Device: NEU_REQ_READ_GROUP
    
    Device-->>GW: 返回数据
    Note left of Device: 点位数据: ONOFF, STEMP, RTEMP
    
    GW->>GW: 构造响应消息
    Note right of GW: acme_encode_protocol_message()
    
    GW->>MQTT: Publish读取响应
    Note right of GW: /acme/SPT_GW_001/FCM_001/read/response<br/>{"result":"success","tags":[...]}
    
    MQTT->>Cloud: 转发读取响应
    
    Note over Cloud,Device: 数据写入流程
    
    Cloud->>MQTT: Publish写入请求
    Note right of Cloud: /acme/SPT_GW_001/FCM_001/write/request<br/>{"tags":[{"name":"ONOFF","value":1}]}
    
    MQTT->>GW: 转发写入请求
    GW->>GW: 处理写入请求
    Note right of GW: handle_write_request()
    
    GW->>Device: 写入设备数据
    Device-->>GW: 写入结果
    
    GW->>MQTT: Publish写入响应
    Note right of GW: /acme/SPT_GW_001/FCM_001/write/response<br/>{"result":"success"}
    
    MQTT->>Cloud: 转发写入响应
    
    Note over Cloud,Device: 事件通知流程
    
    Device->>GW: 设备状态变化
    Note right of Device: 温度超过阈值
    
    GW->>GW: 检测到事件
    Note right of GW: 触发事件处理逻辑
    
    GW->>MQTT: Publish事件通知
    Note right of GW: /acme/SPT_GW_001/FCM_001/event/notify<br/>{"event_type":"alarm","severity":"warning"}
    
    MQTT->>Cloud: 转发事件通知
    
    Cloud->>MQTT: Publish事件确认
    Note right of Cloud: /acme/SPT_GW_001/FCM_001/event/ack<br/>{"result":"acknowledged"}
    
    MQTT->>GW: 转发事件确认
    GW->>GW: 处理事件确认
    Note right of GW: handle_event_ack()
```

## 2. ACME_MQTT协议架构对比图

```mermaid
graph TB
    subgraph "原有协议架构 (冗余复杂)"
        subgraph "主题结构混乱"
            O1["/neuron/gw001/device001/temp/data"]
            O2["/neuron/gw001/device001/humidity/data"]
            O3["/neuron/gw001/device001/control/onoff"]
            O4["/neuron/gw001/device001/control/settemp"]
            O5["/neuron/gw001/device001/alarm/high"]
            O6["/neuron/gw001/device001/status/online"]
        end
        
        subgraph "业务耦合严重"
            OB1[温度业务逻辑]
            OB2[湿度业务逻辑]
            OB3[控制业务逻辑]
            OB4[告警业务逻辑]
        end
    end
    
    subgraph "新协议架构 (简洁统一)"
        subgraph "统一主题结构"
            N1["/acme/SPT_GW_001/FCM_001/read/request"]
            N2["/acme/SPT_GW_001/FCM_001/read/response"]
            N3["/acme/SPT_GW_001/FCM_001/write/request"]
            N4["/acme/SPT_GW_001/FCM_001/write/response"]
            N5["/acme/SPT_GW_001/FCM_001/event/notify"]
            N6["/acme/SPT_GW_001/FCM_001/event/ack"]
        end
        
        subgraph "业务解耦设计"
            NB1[通用读取处理器]
            NB2[通用写入处理器]
            NB3[通用事件处理器]
            NB4[协议版本管理器]
        end
        
        subgraph "标准化消息格式"
            NM1[统一JSON结构]
            NM2[标准错误码]
            NM3[版本协商机制]
            NM4[请求响应匹配]
        end
    end
    
    subgraph "协议优势对比"
        subgraph "原有问题"
            P1[❌ 主题数量庞大]
            P2[❌ 业务耦合严重]
            P3[❌ 扩展困难]
            P4[❌ 维护复杂]
        end
        
        subgraph "新协议优势"
            A1[✅ 主题结构简洁]
            A2[✅ 业务逻辑解耦]
            A3[✅ 易于扩展]
            A4[✅ 维护简单]
        end
    end
    
    %% 连接关系
    O1 --> OB1
    O2 --> OB2
    O3 --> OB3
    O4 --> OB3
    O5 --> OB4
    O6 --> OB4
    
    N1 --> NB1
    N2 --> NB1
    N3 --> NB2
    N4 --> NB2
    N5 --> NB3
    N6 --> NB3
    
    NB1 --> NM1
    NB2 --> NM1
    NB3 --> NM1
    NB4 --> NM3
    
    %% 样式
    classDef old fill:#ffebee,stroke:#f44336
    classDef new fill:#e8f5e8,stroke:#4caf50
    classDef problem fill:#fff3e0,stroke:#ff9800
    classDef advantage fill:#e3f2fd,stroke:#2196f3
    
    class O1,O2,O3,O4,O5,O6,OB1,OB2,OB3,OB4 old
    class N1,N2,N3,N4,N5,N6,NB1,NB2,NB3,NB4,NM1,NM2,NM3,NM4 new
    class P1,P2,P3,P4 problem
    class A1,A2,A3,A4 advantage
```

## 3. 协议主题结构图

```mermaid
graph LR
    subgraph "ACME协议主题结构"
        subgraph "基础格式"
            BASE["/acme/{gateway_id}/{device_id}/{message_type}"]
        end
        
        subgraph "读取主题"
            R1["/acme/SPT_GW_001/FCM_001/read/request"]
            R2["/acme/SPT_GW_001/FCM_001/read/response"]
        end
        
        subgraph "写入主题"
            W1["/acme/SPT_GW_001/FCM_001/write/request"]
            W2["/acme/SPT_GW_001/FCM_001/write/response"]
        end
        
        subgraph "事件主题"
            E1["/acme/SPT_GW_001/FCM_001/event/notify"]
            E2["/acme/SPT_GW_001/FCM_001/event/ack"]
        end
        
        subgraph "响应主题"
            S1["/acme/SPT_GW_001/FCM_001/response/{request_id}"]
        end
        
        subgraph "广播主题"
            B1["/acme/SPT_GW_001/ALL/read/request"]
            B2["/acme/SPT_GW_001/ALL/write/request"]
        end
    end
    
    BASE --> R1
    BASE --> R2
    BASE --> W1
    BASE --> W2
    BASE --> E1
    BASE --> E2
    BASE --> S1
    BASE --> B1
    BASE --> B2
    
    %% 样式
    classDef base fill:#e3f2fd
    classDef read fill:#e8f5e8
    classDef write fill:#fff3e0
    classDef event fill:#f3e5f5
    classDef response fill:#fce4ec
    classDef broadcast fill:#e1f5fe
    
    class BASE base
    class R1,R2 read
    class W1,W2 write
    class E1,E2 event
    class S1 response
    class B1,B2 broadcast
```

## 4. 消息处理架构图

```mermaid
graph TB
    subgraph "ACME_MQTT消息处理架构"
        subgraph "消息接收层"
            MR1[MQTT消息接收]
            MR2[主题解析器]
            MR3[消息路由器]
        end
        
        subgraph "协议处理层"
            PP1[协议消息解析器]
            PP2[版本兼容性检查]
            PP3[请求ID管理器]
            PP4[错误码处理器]
        end
        
        subgraph "业务处理层"
            BP1[读取请求处理器]
            BP2[写入请求处理器]
            BP3[事件通知处理器]
            BP4[响应生成器]
        end
        
        subgraph "数据转换层"
            DT1[JSON编码器]
            DT2[JSON解码器]
            DT3[数据类型转换器]
            DT4[标签映射器]
        end
        
        subgraph "Neuron接口层"
            NI1[NEU_REQ_READ_GROUP]
            NI2[NEU_REQ_WRITE]
            NI3[NEU_RESP_READ_GROUP]
            NI4[设备状态管理]
        end
    end
    
    %% 数据流连接
    MR1 --> MR2
    MR2 --> MR3
    MR3 --> PP1
    PP1 --> PP2
    PP2 --> PP3
    PP3 --> BP1
    PP3 --> BP2
    PP3 --> BP3
    
    BP1 --> DT2
    BP2 --> DT2
    BP3 --> DT1
    
    DT2 --> DT3
    DT3 --> DT4
    DT4 --> NI1
    DT4 --> NI2
    
    NI3 --> DT3
    NI4 --> BP3
    
    BP4 --> DT1
    DT1 --> MR1
    
    PP4 --> BP4
    
    %% 样式
    classDef receive fill:#e3f2fd
    classDef protocol fill:#e8f5e8
    classDef business fill:#fff3e0
    classDef transform fill:#f3e5f5
    classDef neuron fill:#fce4ec
    
    class MR1,MR2,MR3 receive
    class PP1,PP2,PP3,PP4 protocol
    class BP1,BP2,BP3,BP4 business
    class DT1,DT2,DT3,DT4 transform
    class NI1,NI2,NI3,NI4 neuron
```

## 使用说明

### 导出为图片的步骤：

1. **在线导出**：
   - 访问 https://mermaid.live/
   - 复制上述代码到编辑器
   - 点击 "Actions" → "Download PNG"

2. **VS Code导出**：
   - 安装 "Mermaid Preview" 插件
   - 新建 .md 文件，粘贴代码
   - 右键选择导出图片

3. **命令行导出**：
   ```bash
   # 安装工具
   npm install -g @mermaid-js/mermaid-cli
   
   # 导出图片
   mmdc -i protocol_flow.mmd -o protocol_flow.png -w 1920 -H 1080
   ```

### 建议的图片尺寸：
- **流程图**: 1920x1080 (高清)
- **架构对比图**: 1600x1200 (方形)
- **主题结构图**: 1400x800 (宽屏)
- **消息处理图**: 1600x1000 (标准)
