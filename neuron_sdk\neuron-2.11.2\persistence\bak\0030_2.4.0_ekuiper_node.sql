/**
 * NEURON IIoT System for Industry 4.0
 * Copyright (C) 2020-2023 EMQ Technologies Co., Ltd All rights reserved.
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 3 of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with this program; if not, write to the Free Software Foundation,
 * Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.
 **/
BEGIN TRANSACTION;

-- from neuron version 2.4.0, eKuiper plugin is no more singleton
-- remove default data-stream-processing node if not in use
DELETE FROM
  nodes
WHERE
  name = 'data-stream-processing'
  AND name NOT in (
    SELECT
      node_name
    FROM
      settings
  )
  AND name NOT in (
    SELECT
      app_name
    FROM
      subscriptions
  );

-- fix data-stream-processing node plugin name
UPDATE
  nodes
SET
  plugin_name = 'eKuiper'
where
  name = 'data-stream-processing';

COMMIT;
