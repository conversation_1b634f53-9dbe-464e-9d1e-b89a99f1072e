/**
 * NEURON IIoT System for Industry 4.0
 * Copyright (C) 2020-2022 EMQ Technologies Co., Ltd All rights reserved.
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 3 of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with this program; if not, write to the Free Software Foundation,
 * Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.
 **/

/*
 * DO NOT EDIT THIS FILE MANUALLY!
 * It was automatically generated by `json-autotype`.
 */

#include <stdlib.h>
#include <string.h>

#include "json/json.h"

#include "utils/log.h"

#include "neu_json_otel.h"

void neu_json_decode_otel_conf_req_free(neu_json_otel_conf_req_t *req)
{
    if (req->action != NULL) {
        free(req->action);
    }
    if (req->collector_url != NULL) {
        free(req->collector_url);
    }
    if (req->service_name != NULL) {
        free(req->service_name);
    }
    if (req != NULL) {
        free(req);
    }
}
int neu_json_decode_otel_conf_req(char *buf, neu_json_otel_conf_req_t **result)
{
    int   ret      = 0;
    void *json_obj = neu_json_decode_new(buf);

    neu_json_otel_conf_req_t *req = calloc(1, sizeof(neu_json_otel_conf_req_t));
    if (req == NULL) {
        return -1;
    }

    neu_json_elem_t req_elems[] = {
        {
            .name = "action",
            .t    = NEU_JSON_STR,
        },
        {
            .name      = "collector_url",
            .t         = NEU_JSON_STR,
            .attribute = NEU_JSON_ATTRIBUTE_OPTIONAL,
        },
        {
            .name      = "control",
            .t         = NEU_JSON_BOOL,
            .attribute = NEU_JSON_ATTRIBUTE_OPTIONAL,
        },
        {
            .name      = "data",
            .t         = NEU_JSON_BOOL,
            .attribute = NEU_JSON_ATTRIBUTE_OPTIONAL,
        },
        {
            .name      = "data_sample_rate",
            .t         = NEU_JSON_DOUBLE,
            .attribute = NEU_JSON_ATTRIBUTE_OPTIONAL,
        },
        {
            .name      = "service_name",
            .t         = NEU_JSON_STR,
            .attribute = NEU_JSON_ATTRIBUTE_OPTIONAL,
        },
    };

    ret = neu_json_decode_by_json(json_obj, NEU_JSON_ELEM_SIZE(req_elems),
                                  req_elems);
    if (ret != 0) {
        free(req);
        if (json_obj != NULL) {
            neu_json_decode_free(json_obj);
        }
        return -1;
    }

    req->action           = req_elems[0].v.val_str;
    req->collector_url    = req_elems[1].v.val_str;
    req->control_flag     = req_elems[2].v.val_bool;
    req->data_flag        = req_elems[3].v.val_bool;
    req->data_sample_rate = req_elems[4].v.val_double;
    req->service_name     = req_elems[5].v.val_str;
    *result               = req;

    if (json_obj != NULL) {
        neu_json_decode_free(json_obj);
    }
    return ret;
}

int neu_json_encode_otel_conf_req(void *json_object, void *param)
{
    int                       ret          = 0;
    neu_json_otel_conf_req_t *req          = (neu_json_otel_conf_req_t *) param;
    neu_json_elem_t           resp_elems[] = { {
                                         .name      = "action",
                                         .t         = NEU_JSON_STR,
                                         .v.val_str = req->action,
                                     },
                                     {
                                         .name      = "collector_url",
                                         .t         = NEU_JSON_STR,
                                         .v.val_str = req->collector_url,
                                     },
                                     {
                                         .name       = "control",
                                         .t          = NEU_JSON_BOOL,
                                         .v.val_bool = req->control_flag,
                                     },
                                     {
                                         .name       = "data",
                                         .t          = NEU_JSON_BOOL,
                                         .v.val_bool = req->data_flag,
                                     },
                                     {
                                         .name = "data_sample_rate",
                                         .t    = NEU_JSON_DOUBLE,
                                         .v.val_double = req->data_sample_rate,
                                     },
                                     {
                                         .name      = "service_name",
                                         .t         = NEU_JSON_STR,
                                         .v.val_str = req->service_name,
                                     } };
    ret = neu_json_encode_field(json_object, resp_elems,
                                NEU_JSON_ELEM_SIZE(resp_elems));

    return ret;
}