(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-67de72a8"],{"361a":function(e,t,n){"use strict";n("3c39")},"3c39":function(e,t,n){},"820e":function(e,t,n){"use strict";var c=n("23e7"),a=n("c65b"),o=n("59ed"),r=n("f069"),u=n("e667"),l=n("2266");c({target:"Promise",stat:!0},{allSettled:function(e){var t=this,n=r.f(t),c=n.resolve,i=n.reject,s=u((function(){var n=o(t.resolve),r=[],u=0,i=1;l(e,(function(e){var o=u++,l=!1;i++,a(n,t,e).then((function(e){l||(l=!0,r[o]={status:"fulfilled",value:e},--i||c(r))}),(function(e){l||(l=!0,r[o]={status:"rejected",reason:e},--i||c(r))}))})),--i||c(r)}));return s.error&&i(s.value),n.promise}})},dd92:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var c=n("e423"),a=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"driver",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return c["a"].get("/metrics?category=".concat(e),{params:t})}},ef4c:function(e,t,n){"use strict";n.r(t);n("a9e3"),n("d3b7"),n("820e"),n("3ca3"),n("ddb0"),n("d81d"),n("ac1f"),n("466d"),n("1276"),n("b680");var c=n("7a23"),a=n("3fd4"),o=n("6a4f"),r=n("dd92"),u=(n("4ec9"),n("99af"),n("5a0c"),n("55b6")),l=function(e,t){var n=new Map([["day",u["default"].global.t("common.day",t)],["hour",u["default"].global.t("common.hour",t)],["minute",u["default"].global.t("common.minute",t)],["second",u["default"].global.t("common.second",t)]]);return n.get(e)||""},i=function(e){if(!e)return"0 ".concat(u["default"].global.t("common.second",0));var t="";if(e<60)t="".concat(e," ").concat(l("second",e));else if(e>=60&&e<3600){var n=Math.floor(e/60),c=Math.floor(e%60);t="".concat(n," ").concat(l("minute",n)," ").concat(c," ").concat(l("second",e))}else{var a=Math.floor(e/3600),o=Math.floor(Math.floor(e%3600)/60),r=Math.floor(Math.floor(e%3600)%60),i=l("minute",o),s=l("second",r);if(a>0&&a<24)t="".concat(a," ").concat(l("hour",a)," ").concat(o," ").concat(i," ").concat(r," ").concat(s);else{var d=Math.floor(a/24),m=a-24*d;t="".concat(d," ").concat(l("day",d)," ").concat(m," ").concat(l("hour",m)," ").concat(o," ").concat(i," ").concat(r," ").concat(s)}}return t},s=n("73ec"),d=n("eb58"),m=n("a007"),b={class:"card-hd-with-btn"},f={class:"card-title"},j={class:"progress-text"},O=Object(c["defineComponent"])({setup:function(e){Object(c["useCssVars"])((function(e){return{"11491c95":Object(c["unref"])(y)}}));var t,n=Object(d["a"])(),u=n.currentLang,l=Object(c["ref"])(!1),O=Object(c["ref"])({version:"",build_date:""}),v=Object(c["ref"])(""),h=Object(c["reactive"])({systemRunningTime:"",systemStatus:"",memPercent:0,memTotalBytes:"",memUsedBytes:"",startupTimeMatchReg:/(uptime_seconds=?)(\s*\d*)(?=\n)/g,debugFilesMatchReg:/(core_dumped=?)(\s*\d*)(?=\n)/g,memTotalBytesReg:/(mem_total_bytes=?)(\s*\d*)(?=\n)/g,memUsedBytesReg:/(mem_used_bytes=?)(\s*\d*)(?=\n)/g}),g=Object(c["computed"])((function(){return i(Number(h.systemRunningTime))})),p=Object(c["computed"])((function(){return"0"===h.systemStatus?"common.normal":"common.exceptions"})),y=Object(c["computed"])((function(){return"zh"===u.value?"90px":"120px"}));Promise&&!Promise.allSettled&&(Promise.allSettled=function(e){return Promise.all(e.map((function(e){return e.then((function(e){return{state:"fulfilled",value:e}})).catch((function(e){return{state:"rejected",reason:e}}))})))});var w=function(){return new Promise((function(e,t){Object(r["a"])(m["c"].GLOBAL).then((function(t){var n=t.data,c=n.match(h.startupTimeMatchReg),a=n.match(h.debugFilesMatchReg),o=n.match(h.memUsedBytesReg),r=n.match(h.memTotalBytesReg);h.systemRunningTime=c?c[0].split(" ")[1]:"",h.systemStatus=a?a[0].split(" ")[1]:"";var u=Number(o?o[0].split(" ")[1]:"0"),l=Number(r?r[0].split(" ")[1]:"0");h.memPercent=Number(u&&l?(u/l*100).toFixed(2):"0"),h.memUsedBytes=Object(s["h"])(u,"Byte"),h.memTotalBytes=Object(s["h"])(l,"Byte"),x(),e(t)})).catch((function(e){t(e)}))}))},x=function(){t&&window.clearInterval(t),t=window.setInterval((function(){w()}),5e3)},N=function(){try{l.value=!0,Promise.allSettled([Object(o["d"])(),Object(o["b"])(),w()]).then((function(e){var t,n,c=(null===(t=e[0])||void 0===t?void 0:t.value)||{version:"",build_date:""},a=c.data,o=(null===(n=e[1])||void 0===n?void 0:n.value)||{},r=o.data;O.value=a,v.value=(null===r||void 0===r?void 0:r.token)||""})).finally((function(){l.value=!1}))}catch(e){console.log("error",e)}};return N(),Object(c["onUnmounted"])((function(){t&&window.clearInterval(t)})),function(e,t){var n=Object(c["resolveComponent"])("emqx-descriptions-item"),o=Object(c["resolveComponent"])("emqx-descriptions"),r=Object(c["resolveComponent"])("emqx-card"),u=Object(c["resolveDirective"])("emqx-loading");return Object(c["withDirectives"])((Object(c["openBlock"])(),Object(c["createBlock"])(r,{class:"about"},{default:Object(c["withCtx"])((function(){return[Object(c["createElementVNode"])("div",b,[Object(c["createElementVNode"])("h3",f,Object(c["toDisplayString"])(e.$t("common.about")),1)]),Object(c["createElementVNode"])("div",null,[Object(c["createVNode"])(o,{column:1},{default:Object(c["withCtx"])((function(){return[Object(c["createVNode"])(n,{label:e.$t("admin.version")},{default:Object(c["withCtx"])((function(){return[Object(c["createTextVNode"])(Object(c["toDisplayString"])(O.value.version),1)]})),_:1},8,["label"]),Object(c["createVNode"])(n,{label:e.$t("admin.systemStatus")},{default:Object(c["withCtx"])((function(){return[Object(c["createTextVNode"])(Object(c["toDisplayString"])(e.$t("".concat(Object(c["unref"])(p)))),1)]})),_:1},8,["label"]),Object(c["createVNode"])(n,{label:e.$t("admin.systemRunningTime")},{default:Object(c["withCtx"])((function(){return[Object(c["createTextVNode"])(Object(c["toDisplayString"])(Object(c["unref"])(g)),1)]})),_:1},8,["label"]),Object(c["createVNode"])(n,{label:e.$t("admin.memoryUsage")},{default:Object(c["withCtx"])((function(){return[Object(c["createVNode"])(Object(c["unref"])(a["ElProgress"]),{"stroke-width":14,percentage:Object(c["unref"])(h).memPercent,status:"success",class:"progress-bar"},{default:Object(c["withCtx"])((function(){return[Object(c["createElementVNode"])("span",j,Object(c["toDisplayString"])(Object(c["unref"])(h).memUsedBytes)+" / "+Object(c["toDisplayString"])(Object(c["unref"])(h).memTotalBytes),1)]})),_:1},8,["percentage"])]})),_:1},8,["label"]),Object(c["createVNode"])(n,{label:e.$t("admin.hwToken")},{default:Object(c["withCtx"])((function(){return[Object(c["createTextVNode"])(Object(c["toDisplayString"])(v.value),1)]})),_:1},8,["label"]),Object(c["createVNode"])(n,{label:e.$t("admin.builtDate")},{default:Object(c["withCtx"])((function(){return[Object(c["createTextVNode"])(Object(c["toDisplayString"])(O.value.build_date),1)]})),_:1},8,["label"])]})),_:1})])]})),_:1},512)),[[u,l.value]])}}}),v=(n("361a"),n("6b0d")),h=n.n(v);const g=h()(O,[["__scopeId","data-v-9338e124"]]);t["default"]=g}}]);