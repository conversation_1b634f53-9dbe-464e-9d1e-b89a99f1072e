次文件夹主要存放 Neuron SDK源码、dashboard web 前端。

Neuron SDK 源码 在目录neuron-2.11.2，在build目录下编译，生成可执行文件neuron 和 libneuron-base.so 动态库、以及相关的配置文件config、plugins等，拷贝到SPT设备上。

dist 为 dashboard web 前端，直接拷贝到 SPT设备neuron 执行目录下即可。

移植步骤：
/******************* 移植 neuron 到 设备上 ******************************************/
1、拷贝web 前端:neuron-dashboard 网页看板 直接解压neuron-dashboard.zip 	得到 dist 目录，拷贝到 开发版本 neuron 执行程序目录就可以。
2、拷贝neuron-2.11.2/build下的 libneuron-base.so、neuron、plugins、simulator、config、persistence 文件和文件夹到 板上 /opt/neuron 目录下
3、拷贝依赖库和bin(可选)*************** 服务器 /opt/externs/libs/arm-linux-gnueabihf/下的bin和lib 到板上 /opt/neuron 目录下
4、板上 /opt/neuron 目录给权限: chmod -R +x /opt/neuron
5、设置环境变量:export PATH=$PATH:/opt/neuron/bin
			export LD_LIBRARY_PATH=/opt/neuron/lib:$LD_LIBRARY_PATH
6、运行neuron 软件 ./neuron --log --log_level DEBUG &
				或者 ./neuron -d          (守护进程方式运行)



开发板 neuron 目录：/opt/neuron
root@myd-yr3506:/opt/neuron# ls
bin  config  dist  lib  libneuron-base.so  logs  neuron  persistence  plugins  simulator  tests
root@myd-yr3506:/opt/neuron# 


bin 目录 是 交叉编译移植库***************服务器目录的 /opt/externs/libs/arm-linux-gnueabihf/bin
lib 目录是 交叉编译移植库的目录/opt/externs/libs/arm-linux-gnueabihf/lib  (只需要拷贝so 动态库即可，.a静态库已经链接到可执行程序 neuron 内 所以不用拷贝)
config  logs  persistence  plugins  simulator  tests 直接拷贝 SDK 编译后的build 生成的文件夹即可
/************************************************************************************************************/
