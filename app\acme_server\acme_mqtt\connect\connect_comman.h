#ifndef ACME_CONNECT_COMMAN_HHHH_
#define ACME_CONNECT_COMMAN_HHHH_

#include "plugin.h"
#include "utils/log.h"



typedef struct
{
    neu_plugin_t *plugin;           //插件句柄
    const neu_plugin_module_t *module;    //插件基本信息
    void * mqtt_setting;             //MQTT 配置信息 (json 格式)
    bool   mqtt_start;              //是否启动MQTT连接
    pthread_mutex_t mutex;          //锁
}acme_plugin_t;


int acme_print_json(void *json_value);      //打印json 内容

acme_plugin_t * acme_process_init(neu_plugin_t *plugin);       //初始化
void * get_node_setting_resp(neu_plugin_t *plugin, neu_resp_get_node_setting_t *setting);   //获取适配器MQTT 配置参数
int get_node_setting_req(neu_plugin_t *plugin);     //请求获取适配器 MQTT 参数

int acme_mqtt_setting_set(void *new_setting_json); //ACME对象 MQTT 配置参数设置
void * acme_mqtt_setting_get();                     //ACME对象 MQTT 配置参数获取

int acme_mqtt_setting_modify(char *username, char *password, char *mqttAddr, int post, char *time);       //修改 MQTT 登录参数

int acme_mqtt_start_req();
int acme_mqtt_stop_req();
bool acme_mqtt_get_status();

#endif
