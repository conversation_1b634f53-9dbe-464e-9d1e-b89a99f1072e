# ACME_MQTT 物模型规范 v1.0

## 📋 物模型概述

### 设计理念
基于 **ACME_MQTT 云端对接协议规范 v1.0**，定义标准化的物模型规范，实现设备能力的统一描述和管理。

### 核心特性
- **标准化描述**: 统一的设备能力模型
- **类型化定义**: 强类型的属性、服务、事件定义
- **层次化管理**: 支持设备分类和继承
- **动态扩展**: 支持自定义属性和服务
- **版本管理**: 支持物模型版本演进

## 🏗️ 物模型架构

### 物模型组成
```
物模型 (Thing Model)
├── 基础信息 (Basic Info)
├── 属性 (Properties)
├── 服务 (Services)
├── 事件 (Events)
└── 扩展信息 (Extensions)
```

### 数据类型系统
```
基础类型:
- int32: 32位整数
- float: 单精度浮点数
- double: 双精度浮点数
- bool: 布尔值
- string: 字符串
- enum: 枚举类型
- array: 数组类型
- struct: 结构体类型
```

## 📊 物模型定义结构

### 完整物模型JSON结构
```json
{
    "thing_model": {
        "version": "1.0",
        "model_id": "acme.device.fcm.v1",
        "model_name": "ACME FCM空调控制器",
        "device_type": "FCM",
        "manufacturer": "ACME",
        "description": "ACME FCM系列空调控制设备物模型",
        "created_time": "2024-01-01T00:00:00Z",
        "updated_time": "2024-01-01T00:00:00Z",
        
        "properties": { ... },
        "services": { ... },
        "events": { ... },
        "extensions": { ... }
    }
}
```

### 基础信息字段说明
| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| version | string | ✅ | 物模型版本号 |
| model_id | string | ✅ | 物模型唯一标识 |
| model_name | string | ✅ | 物模型名称 |
| device_type | string | ✅ | 设备类型标识 |
| manufacturer | string | ✅ | 设备厂商 |
| description | string | ❌ | 物模型描述 |
| created_time | string | ✅ | 创建时间(ISO8601) |
| updated_time | string | ✅ | 更新时间(ISO8601) |

## 🔧 属性定义规范

### 属性结构定义
```json
{
    "properties": {
        "property_identifier": {
            "name": "属性名称",
            "description": "属性描述",
            "data_type": "int32|float|bool|string|enum|array|struct",
            "access_mode": "r|w|rw",
            "required": true,
            "default_value": null,
            "unit": "单位",
            "range": {
                "min": 0,
                "max": 100,
                "step": 1
            },
            "enum_values": [
                {"value": 0, "name": "关闭"},
                {"value": 1, "name": "开启"}
            ],
            "tags": ["real-time", "alarm"]
        }
    }
}
```

### 属性字段说明
| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| name | string | ✅ | 属性显示名称 |
| description | string | ❌ | 属性详细描述 |
| data_type | string | ✅ | 数据类型 |
| access_mode | string | ✅ | 访问模式: r(只读)/w(只写)/rw(读写) |
| required | boolean | ❌ | 是否必需属性，默认false |
| default_value | any | ❌ | 默认值 |
| unit | string | ❌ | 数据单位 |
| range | object | ❌ | 数值范围(适用于数值类型) |
| enum_values | array | ❌ | 枚举值定义(适用于enum类型) |
| tags | array | ❌ | 属性标签 |

## ⚙️ 服务定义规范

### 服务结构定义
```json
{
    "services": {
        "service_identifier": {
            "name": "服务名称",
            "description": "服务描述",
            "call_type": "sync|async",
            "input_params": {
                "param1": {
                    "name": "参数名称",
                    "data_type": "int32",
                    "required": true,
                    "description": "参数描述"
                }
            },
            "output_params": {
                "result": {
                    "name": "执行结果",
                    "data_type": "bool",
                    "description": "服务执行结果"
                }
            }
        }
    }
}
```

### 服务字段说明
| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| name | string | ✅ | 服务显示名称 |
| description | string | ❌ | 服务详细描述 |
| call_type | string | ✅ | 调用类型: sync(同步)/async(异步) |
| input_params | object | ❌ | 输入参数定义 |
| output_params | object | ❌ | 输出参数定义 |

## 📢 事件定义规范

### 事件结构定义
```json
{
    "events": {
        "event_identifier": {
            "name": "事件名称",
            "description": "事件描述",
            "event_type": "info|warning|error|critical",
            "output_params": {
                "alarm_code": {
                    "name": "告警代码",
                    "data_type": "int32",
                    "description": "具体告警代码"
                },
                "alarm_message": {
                    "name": "告警信息",
                    "data_type": "string",
                    "description": "告警详细信息"
                }
            }
        }
    }
}
```

### 事件字段说明
| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| name | string | ✅ | 事件显示名称 |
| description | string | ❌ | 事件详细描述 |
| event_type | string | ✅ | 事件类型: info/warning/error/critical |
| output_params | object | ❌ | 事件输出参数 |

## 🔌 扩展信息规范

### 扩展结构定义
```json
{
    "extensions": {
        "protocol_config": {
            "lora_config": {
                "device_eui": "string",
                "app_eui": "string",
                "frequency": "number"
            }
        },
        "ui_config": {
            "display_order": ["ONOFF", "STEMP", "RTEMP"],
            "group_layout": {
                "basic": ["ONOFF", "SMODE"],
                "temperature": ["STEMP", "RTEMP"]
            }
        },
        "business_config": {
            "alarm_thresholds": {
                "RTEMP": {"min": 16, "max": 30}
            }
        }
    }
}
```

## 📱 设备类型物模型定义

### FCM空调控制器物模型
```json
{
    "thing_model": {
        "version": "1.0",
        "model_id": "acme.device.fcm.v1",
        "model_name": "ACME FCM空调控制器",
        "device_type": "FCM",
        "manufacturer": "ACME",
        "description": "ACME FCM系列空调控制设备，支持温度控制、模式切换等功能",
        "created_time": "2024-01-01T00:00:00Z",
        "updated_time": "2024-01-01T00:00:00Z",

        "properties": {
            "ONOFF": {
                "name": "开关状态",
                "description": "空调开关控制",
                "data_type": "enum",
                "access_mode": "rw",
                "required": true,
                "default_value": 0,
                "enum_values": [
                    {"value": 0, "name": "关闭"},
                    {"value": 1, "name": "开启"}
                ],
                "tags": ["control", "real-time"]
            },
            "STEMP": {
                "name": "设定温度",
                "description": "空调设定温度值",
                "data_type": "float",
                "access_mode": "rw",
                "required": true,
                "default_value": 25.0,
                "unit": "°C",
                "range": {
                    "min": 16.0,
                    "max": 30.0,
                    "step": 0.5
                },
                "tags": ["control", "temperature"]
            },
            "RTEMP": {
                "name": "实际温度",
                "description": "当前环境温度",
                "data_type": "float",
                "access_mode": "r",
                "required": true,
                "unit": "°C",
                "range": {
                    "min": -20.0,
                    "max": 60.0,
                    "step": 0.1
                },
                "tags": ["sensor", "real-time", "alarm"]
            },
            "SMODE": {
                "name": "运行模式",
                "description": "空调运行模式",
                "data_type": "enum",
                "access_mode": "rw",
                "required": true,
                "default_value": 0,
                "enum_values": [
                    {"value": 0, "name": "自动"},
                    {"value": 1, "name": "制冷"},
                    {"value": 2, "name": "制热"},
                    {"value": 3, "name": "送风"},
                    {"value": 4, "name": "除湿"}
                ],
                "tags": ["control", "mode"]
            },
            "SFAN": {
                "name": "风速设定",
                "description": "空调风速档位",
                "data_type": "enum",
                "access_mode": "rw",
                "required": false,
                "default_value": 0,
                "enum_values": [
                    {"value": 0, "name": "自动"},
                    {"value": 1, "name": "低速"},
                    {"value": 2, "name": "中速"},
                    {"value": 3, "name": "高速"}
                ],
                "tags": ["control", "fan"]
            },
            "STATUS": {
                "name": "设备状态",
                "description": "设备运行状态",
                "data_type": "enum",
                "access_mode": "r",
                "required": true,
                "enum_values": [
                    {"value": 0, "name": "离线"},
                    {"value": 1, "name": "在线"},
                    {"value": 2, "name": "故障"},
                    {"value": 3, "name": "维护"}
                ],
                "tags": ["status", "real-time"]
            }
        },

        "services": {
            "set_temperature": {
                "name": "设置温度",
                "description": "设置空调目标温度",
                "call_type": "sync",
                "input_params": {
                    "temperature": {
                        "name": "目标温度",
                        "data_type": "float",
                        "required": true,
                        "description": "要设置的温度值",
                        "unit": "°C",
                        "range": {"min": 16.0, "max": 30.0}
                    }
                },
                "output_params": {
                    "result": {
                        "name": "执行结果",
                        "data_type": "bool",
                        "description": "设置是否成功"
                    },
                    "actual_temperature": {
                        "name": "实际设定值",
                        "data_type": "float",
                        "description": "实际设定的温度值",
                        "unit": "°C"
                    }
                }
            },
            "switch_mode": {
                "name": "切换模式",
                "description": "切换空调运行模式",
                "call_type": "sync",
                "input_params": {
                    "mode": {
                        "name": "运行模式",
                        "data_type": "enum",
                        "required": true,
                        "description": "要切换的运行模式",
                        "enum_values": [
                            {"value": 0, "name": "自动"},
                            {"value": 1, "name": "制冷"},
                            {"value": 2, "name": "制热"},
                            {"value": 3, "name": "送风"},
                            {"value": 4, "name": "除湿"}
                        ]
                    }
                },
                "output_params": {
                    "result": {
                        "name": "执行结果",
                        "data_type": "bool",
                        "description": "切换是否成功"
                    }
                }
            },
            "power_control": {
                "name": "电源控制",
                "description": "控制空调开关",
                "call_type": "sync",
                "input_params": {
                    "power": {
                        "name": "电源状态",
                        "data_type": "bool",
                        "required": true,
                        "description": "true=开启, false=关闭"
                    }
                },
                "output_params": {
                    "result": {
                        "name": "执行结果",
                        "data_type": "bool",
                        "description": "控制是否成功"
                    }
                }
            }
        },

        "events": {
            "temperature_alarm": {
                "name": "温度告警",
                "description": "温度超出正常范围告警",
                "event_type": "warning",
                "output_params": {
                    "current_temperature": {
                        "name": "当前温度",
                        "data_type": "float",
                        "description": "触发告警时的温度值",
                        "unit": "°C"
                    },
                    "threshold_type": {
                        "name": "阈值类型",
                        "data_type": "enum",
                        "description": "超出的阈值类型",
                        "enum_values": [
                            {"value": 1, "name": "超高温"},
                            {"value": 2, "name": "超低温"}
                        ]
                    },
                    "threshold_value": {
                        "name": "阈值",
                        "data_type": "float",
                        "description": "告警阈值",
                        "unit": "°C"
                    }
                }
            },
            "device_offline": {
                "name": "设备离线",
                "description": "设备通信中断",
                "event_type": "error",
                "output_params": {
                    "offline_time": {
                        "name": "离线时间",
                        "data_type": "string",
                        "description": "设备离线的时间戳"
                    },
                    "last_online_time": {
                        "name": "最后在线时间",
                        "data_type": "string",
                        "description": "设备最后在线的时间戳"
                    }
                }
            },
            "device_fault": {
                "name": "设备故障",
                "description": "设备硬件或软件故障",
                "event_type": "critical",
                "output_params": {
                    "fault_code": {
                        "name": "故障代码",
                        "data_type": "int32",
                        "description": "具体故障代码"
                    },
                    "fault_message": {
                        "name": "故障信息",
                        "data_type": "string",
                        "description": "故障详细描述"
                    },
                    "fault_level": {
                        "name": "故障等级",
                        "data_type": "enum",
                        "description": "故障严重程度",
                        "enum_values": [
                            {"value": 1, "name": "轻微"},
                            {"value": 2, "name": "一般"},
                            {"value": 3, "name": "严重"},
                            {"value": 4, "name": "致命"}
                        ]
                    }
                }
            }
        },

        "extensions": {
            "protocol_config": {
                "lora_config": {
                    "device_class": "A",
                    "data_rate": "SF7BW125",
                    "tx_power": 14,
                    "adr_enabled": true
                },
                "communication": {
                    "heartbeat_interval": 300,
                    "data_report_interval": 60,
                    "retry_count": 3,
                    "timeout": 30
                }
            },
            "ui_config": {
                "display_order": ["ONOFF", "STEMP", "RTEMP", "SMODE", "SFAN", "STATUS"],
                "group_layout": {
                    "basic_control": {
                        "name": "基础控制",
                        "properties": ["ONOFF", "SMODE"]
                    },
                    "temperature_control": {
                        "name": "温度控制",
                        "properties": ["STEMP", "RTEMP"]
                    },
                    "advanced_control": {
                        "name": "高级控制",
                        "properties": ["SFAN"]
                    },
                    "status_monitor": {
                        "name": "状态监控",
                        "properties": ["STATUS"]
                    }
                },
                "dashboard_config": {
                    "primary_display": ["ONOFF", "RTEMP"],
                    "chart_properties": ["RTEMP", "STEMP"],
                    "alarm_properties": ["RTEMP", "STATUS"]
                }
            },
            "business_config": {
                "alarm_thresholds": {
                    "RTEMP": {
                        "high_alarm": 35.0,
                        "low_alarm": 10.0,
                        "high_warning": 32.0,
                        "low_warning": 12.0
                    }
                },
                "control_constraints": {
                    "STEMP": {
                        "summer_range": {"min": 22.0, "max": 28.0},
                        "winter_range": {"min": 18.0, "max": 26.0}
                    }
                },
                "energy_management": {
                    "power_saving_mode": true,
                    "auto_shutdown_delay": 1800,
                    "eco_temperature_offset": 2.0
                }
            }
        }
    }
}
```

### ECM环境监测器物模型
```json
{
    "thing_model": {
        "version": "1.0",
        "model_id": "acme.device.ecm.v1",
        "model_name": "ACME ECM环境监测器",
        "device_type": "ECM",
        "manufacturer": "ACME",
        "description": "ACME ECM系列环境监测设备，支持温湿度、CO2、PM2.5等环境参数监测",

        "properties": {
            "TEMP": {
                "name": "环境温度",
                "description": "当前环境温度",
                "data_type": "float",
                "access_mode": "r",
                "required": true,
                "unit": "°C",
                "range": {"min": -40.0, "max": 80.0, "step": 0.1},
                "tags": ["sensor", "real-time"]
            },
            "HUMI": {
                "name": "环境湿度",
                "description": "当前环境相对湿度",
                "data_type": "float",
                "access_mode": "r",
                "required": true,
                "unit": "%RH",
                "range": {"min": 0.0, "max": 100.0, "step": 0.1},
                "tags": ["sensor", "real-time"]
            },
            "CO2": {
                "name": "二氧化碳浓度",
                "description": "空气中CO2浓度",
                "data_type": "int32",
                "access_mode": "r",
                "required": false,
                "unit": "ppm",
                "range": {"min": 0, "max": 5000, "step": 1},
                "tags": ["sensor", "air-quality"]
            },
            "PM25": {
                "name": "PM2.5浓度",
                "description": "空气中PM2.5颗粒物浓度",
                "data_type": "int32",
                "access_mode": "r",
                "required": false,
                "unit": "μg/m³",
                "range": {"min": 0, "max": 500, "step": 1},
                "tags": ["sensor", "air-quality"]
            },
            "LIGHT": {
                "name": "光照强度",
                "description": "环境光照强度",
                "data_type": "int32",
                "access_mode": "r",
                "required": false,
                "unit": "lux",
                "range": {"min": 0, "max": 100000, "step": 1},
                "tags": ["sensor", "light"]
            }
        },

        "services": {
            "calibrate_sensor": {
                "name": "传感器校准",
                "description": "校准指定传感器",
                "call_type": "async",
                "input_params": {
                    "sensor_type": {
                        "name": "传感器类型",
                        "data_type": "enum",
                        "required": true,
                        "enum_values": [
                            {"value": 1, "name": "温度传感器"},
                            {"value": 2, "name": "湿度传感器"},
                            {"value": 3, "name": "CO2传感器"},
                            {"value": 4, "name": "PM2.5传感器"}
                        ]
                    }
                }
            }
        },

        "events": {
            "air_quality_alarm": {
                "name": "空气质量告警",
                "description": "空气质量指标超标",
                "event_type": "warning",
                "output_params": {
                    "parameter": {
                        "name": "超标参数",
                        "data_type": "string",
                        "description": "超标的环境参数名称"
                    },
                    "current_value": {
                        "name": "当前值",
                        "data_type": "float",
                        "description": "当前参数值"
                    },
                    "threshold": {
                        "name": "告警阈值",
                        "data_type": "float",
                        "description": "设定的告警阈值"
                    }
                }
            }
        }
    }
}
```

## 🔗 物模型与MQTT协议映射

### 属性读取映射
**MQTT主题**: `/acme/{gateway_id}/{device_id}/read/request`

**物模型映射**:
```json
{
    "version": "1.0",
    "timestamp": 1703123456789,
    "request_id": "read_req_001",
    "message_type": "read_request",
    "source": "cloud",
    "target": "FCM_001",
    "data": {
        "properties": ["ONOFF", "STEMP", "RTEMP", "SMODE"],
        "thing_model_id": "acme.device.fcm.v1"
    }
}
```

**响应映射**:
```json
{
    "version": "1.0",
    "timestamp": 1703123456790,
    "request_id": "read_req_001",
    "message_type": "read_response",
    "source": "gateway",
    "target": "cloud",
    "data": {
        "result": "success",
        "thing_model_id": "acme.device.fcm.v1",
        "properties": [
            {
                "identifier": "ONOFF",
                "value": 1,
                "data_type": "enum",
                "timestamp": 1703123456789,
                "quality": "good"
            },
            {
                "identifier": "STEMP",
                "value": 25.5,
                "data_type": "float",
                "unit": "°C",
                "timestamp": 1703123456789,
                "quality": "good"
            }
        ]
    }
}
```

### 服务调用映射
**MQTT主题**: `/acme/{gateway_id}/{device_id}/write/request`

**服务调用**:
```json
{
    "version": "1.0",
    "timestamp": 1703123456789,
    "request_id": "service_req_001",
    "message_type": "service_request",
    "source": "cloud",
    "target": "FCM_001",
    "data": {
        "thing_model_id": "acme.device.fcm.v1",
        "service": {
            "identifier": "set_temperature",
            "input_params": {
                "temperature": 26.0
            }
        }
    }
}
```

### 事件上报映射
**MQTT主题**: `/acme/{gateway_id}/{device_id}/event/notify`

**事件上报**:
```json
{
    "version": "1.0",
    "timestamp": 1703123456789,
    "request_id": "event_001",
    "message_type": "event_notify",
    "source": "gateway",
    "target": "cloud",
    "data": {
        "thing_model_id": "acme.device.fcm.v1",
        "event": {
            "identifier": "temperature_alarm",
            "event_type": "warning",
            "output_params": {
                "current_temperature": 35.5,
                "threshold_type": 1,
                "threshold_value": 30.0
            },
            "timestamp": 1703123456789
        }
    }
}
```

## 📋 物模型管理规范

### 物模型版本管理
```json
{
    "version_info": {
        "current_version": "1.0",
        "supported_versions": ["1.0"],
        "compatibility_matrix": {
            "1.0": {
                "backward_compatible": [],
                "forward_compatible": ["1.1"]
            }
        },
        "migration_rules": {
            "from_1.0_to_1.1": {
                "property_mappings": {},
                "service_mappings": {},
                "event_mappings": {}
            }
        }
    }
}
```

### 物模型注册流程
1. **模型定义**: 按照规范定义物模型JSON
2. **模型验证**: 使用JSON Schema验证模型格式
3. **模型注册**: 向物模型管理器注册新模型
4. **版本管理**: 管理模型版本和兼容性
5. **模型发布**: 发布到设备和云端平台

### 物模型发现机制
**MQTT主题**: `/acme/{gateway_id}/{device_id}/model/discover`

**发现请求**:
```json
{
    "version": "1.0",
    "timestamp": 1703123456789,
    "request_id": "discover_req_001",
    "message_type": "model_discover_request",
    "source": "cloud",
    "target": "FCM_001",
    "data": {
        "discover_type": "full|properties|services|events"
    }
}
```

**发现响应**:
```json
{
    "version": "1.0",
    "timestamp": 1703123456790,
    "request_id": "discover_req_001",
    "message_type": "model_discover_response",
    "source": "gateway",
    "target": "cloud",
    "data": {
        "result": "success",
        "thing_model_id": "acme.device.fcm.v1",
        "model_version": "1.0",
        "supported_features": {
            "properties": ["ONOFF", "STEMP", "RTEMP", "SMODE"],
            "services": ["set_temperature", "switch_mode", "power_control"],
            "events": ["temperature_alarm", "device_offline", "device_fault"]
        },
        "model_url": "https://models.acme.com/fcm/v1.0/model.json"
    }
}
```

## ✅ 物模型验证规范

### JSON Schema定义
```json
{
    "$schema": "http://json-schema.org/draft-07/schema#",
    "title": "ACME Thing Model Schema",
    "type": "object",
    "required": ["thing_model"],
    "properties": {
        "thing_model": {
            "type": "object",
            "required": ["version", "model_id", "model_name", "device_type", "manufacturer"],
            "properties": {
                "version": {
                    "type": "string",
                    "pattern": "^\\d+\\.\\d+$"
                },
                "model_id": {
                    "type": "string",
                    "pattern": "^[a-z0-9._-]+$"
                },
                "model_name": {
                    "type": "string",
                    "minLength": 1,
                    "maxLength": 100
                },
                "device_type": {
                    "type": "string",
                    "enum": ["FCM", "ECM", "IGW", "BEC"]
                },
                "manufacturer": {
                    "type": "string",
                    "minLength": 1
                },
                "properties": {
                    "type": "object",
                    "patternProperties": {
                        "^[A-Z][A-Z0-9_]*$": {
                            "$ref": "#/definitions/property"
                        }
                    }
                },
                "services": {
                    "type": "object",
                    "patternProperties": {
                        "^[a-z][a-z0-9_]*$": {
                            "$ref": "#/definitions/service"
                        }
                    }
                },
                "events": {
                    "type": "object",
                    "patternProperties": {
                        "^[a-z][a-z0-9_]*$": {
                            "$ref": "#/definitions/event"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "property": {
            "type": "object",
            "required": ["name", "data_type", "access_mode"],
            "properties": {
                "name": {"type": "string"},
                "description": {"type": "string"},
                "data_type": {
                    "type": "string",
                    "enum": ["int32", "float", "double", "bool", "string", "enum", "array", "struct"]
                },
                "access_mode": {
                    "type": "string",
                    "enum": ["r", "w", "rw"]
                },
                "required": {"type": "boolean"},
                "unit": {"type": "string"},
                "range": {
                    "type": "object",
                    "properties": {
                        "min": {"type": "number"},
                        "max": {"type": "number"},
                        "step": {"type": "number"}
                    }
                },
                "enum_values": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "required": ["value", "name"],
                        "properties": {
                            "value": {"type": ["number", "string", "boolean"]},
                            "name": {"type": "string"}
                        }
                    }
                }
            }
        },
        "service": {
            "type": "object",
            "required": ["name", "call_type"],
            "properties": {
                "name": {"type": "string"},
                "description": {"type": "string"},
                "call_type": {
                    "type": "string",
                    "enum": ["sync", "async"]
                },
                "input_params": {
                    "type": "object",
                    "patternProperties": {
                        "^[a-z][a-z0-9_]*$": {
                            "$ref": "#/definitions/parameter"
                        }
                    }
                },
                "output_params": {
                    "type": "object",
                    "patternProperties": {
                        "^[a-z][a-z0-9_]*$": {
                            "$ref": "#/definitions/parameter"
                        }
                    }
                }
            }
        },
        "event": {
            "type": "object",
            "required": ["name", "event_type"],
            "properties": {
                "name": {"type": "string"},
                "description": {"type": "string"},
                "event_type": {
                    "type": "string",
                    "enum": ["info", "warning", "error", "critical"]
                },
                "output_params": {
                    "type": "object",
                    "patternProperties": {
                        "^[a-z][a-z0-9_]*$": {
                            "$ref": "#/definitions/parameter"
                        }
                    }
                }
            }
        },
        "parameter": {
            "type": "object",
            "required": ["name", "data_type"],
            "properties": {
                "name": {"type": "string"},
                "data_type": {"type": "string"},
                "required": {"type": "boolean"},
                "description": {"type": "string"}
            }
        }
    }
}
```

### 验证规则
1. **命名规范**: 属性使用大写字母和下划线，服务和事件使用小写字母和下划线
2. **数据类型**: 必须使用预定义的数据类型
3. **访问模式**: 属性必须指定正确的访问模式
4. **枚举值**: 枚举类型必须定义所有可能的值
5. **范围约束**: 数值类型应定义合理的取值范围
6. **必填字段**: 核心字段必须填写完整

## 🚀 实施指南

### 阶段1: 基础物模型实现 (2周)
1. **定义核心设备物模型**: FCM、ECM基础物模型
2. **实现物模型解析器**: JSON解析和验证
3. **集成MQTT协议映射**: 基础读写操作映射
4. **开发验证工具**: 物模型格式验证

### 阶段2: 高级功能实现 (3周)
1. **服务调用机制**: 实现服务调用和响应
2. **事件上报机制**: 实现事件监听和上报
3. **物模型发现**: 动态物模型发现和注册
4. **版本管理**: 物模型版本控制和兼容性

### 阶段3: 优化和扩展 (2周)
1. **性能优化**: 物模型解析和处理性能优化
2. **扩展设备支持**: 支持更多设备类型
3. **管理工具**: 物模型管理和配置工具
4. **文档完善**: 开发者文档和示例

### 技术要点
1. **物模型缓存**: 缓存已解析的物模型，提高性能
2. **动态加载**: 支持运行时动态加载新的物模型
3. **错误处理**: 完善的物模型验证和错误提示
4. **兼容性**: 保持向后兼容，支持平滑升级

## 📊 效果评估

### 预期收益
1. **标准化程度**: 设备接入标准化程度提升90%
2. **开发效率**: 新设备接入开发效率提升60%
3. **维护成本**: 系统维护成本降低40%
4. **扩展能力**: 支持快速扩展新设备类型

### 成功指标
- 物模型定义覆盖率 > 95%
- 协议映射准确率 > 99%
- 设备接入成功率 > 98%
- 系统响应时间 < 100ms

## 🔐 安全认证规范

### 设备身份认证

#### 设备证书管理
```json
{
    "device_certificate": {
        "device_id": "FCM_001",
        "device_eui": "1234567890ABCDEF",
        "certificate_type": "X.509|PSK|JWT",
        "certificate_data": "-----BEGIN CERTIFICATE-----...",
        "private_key": "-----BEGIN PRIVATE KEY-----...",
        "ca_certificate": "-----BEGIN CERTIFICATE-----...",
        "validity_period": {
            "not_before": "2024-01-01T00:00:00Z",
            "not_after": "2025-01-01T00:00:00Z"
        },
        "permissions": {
            "properties": {
                "read": ["ONOFF", "STEMP", "RTEMP"],
                "write": ["ONOFF", "STEMP"]
            },
            "services": ["set_temperature", "power_control"],
            "events": ["temperature_alarm", "device_offline"]
        }
    }
}
```

#### 认证消息格式
```json
{
    "version": "1.0",
    "timestamp": 1703123456789,
    "request_id": "auth_req_001",
    "message_type": "auth_request",
    "source": "device",
    "target": "cloud",
    "data": {
        "auth_type": "certificate|token|signature",
        "device_id": "FCM_001",
        "device_eui": "1234567890ABCDEF",
        "thing_model_id": "acme.device.fcm.v1",
        "credentials": {
            "certificate": "-----BEGIN CERTIFICATE-----...",
            "signature": "base64_encoded_signature",
            "timestamp": 1703123456789,
            "nonce": "random_nonce_value"
        }
    }
}
```

### 消息签名验证

#### 签名算法支持
- **RSA-SHA256**: 适用于高安全要求场景
- **ECDSA-SHA256**: 适用于资源受限设备
- **HMAC-SHA256**: 适用于对称密钥场景

#### 签名消息结构
```json
{
    "version": "1.0",
    "timestamp": 1703123456789,
    "request_id": "signed_req_001",
    "message_type": "read_request",
    "source": "cloud",
    "target": "FCM_001",
    "security": {
        "signature_algorithm": "RSA-SHA256",
        "signature": "base64_encoded_signature",
        "certificate_fingerprint": "sha256_fingerprint",
        "signing_time": 1703123456789
    },
    "data": {
        "properties": ["ONOFF", "STEMP", "RTEMP"]
    }
}
```

### 访问控制规范

#### 权限模型定义
```json
{
    "access_control": {
        "device_id": "FCM_001",
        "thing_model_id": "acme.device.fcm.v1",
        "permissions": {
            "properties": {
                "ONOFF": {
                    "read": true,
                    "write": true,
                    "conditions": {
                        "time_range": ["08:00", "22:00"],
                        "user_roles": ["admin", "operator"]
                    }
                },
                "STEMP": {
                    "read": true,
                    "write": true,
                    "constraints": {
                        "min_value": 16.0,
                        "max_value": 30.0,
                        "rate_limit": "10/minute"
                    }
                },
                "RTEMP": {
                    "read": true,
                    "write": false
                }
            },
            "services": {
                "set_temperature": {
                    "allowed": true,
                    "rate_limit": "5/minute",
                    "user_roles": ["admin", "operator"]
                },
                "power_control": {
                    "allowed": true,
                    "conditions": {
                        "emergency_mode": false
                    }
                }
            },
            "events": {
                "temperature_alarm": {
                    "subscribe": true,
                    "user_roles": ["admin", "operator", "viewer"]
                }
            }
        }
    }
}
```

### 加密传输规范

#### TLS配置要求
```json
{
    "tls_config": {
        "min_version": "TLSv1.2",
        "cipher_suites": [
            "TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384",
            "TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256",
            "TLS_RSA_WITH_AES_256_GCM_SHA384"
        ],
        "certificate_validation": {
            "verify_peer": true,
            "verify_hostname": true,
            "ca_file": "/etc/ssl/certs/acme-ca.pem",
            "cert_file": "/etc/ssl/certs/device.pem",
            "key_file": "/etc/ssl/private/device.key"
        }
    }
}
```

#### 消息级加密
```json
{
    "version": "1.0",
    "timestamp": 1703123456789,
    "request_id": "encrypted_req_001",
    "message_type": "read_request",
    "source": "cloud",
    "target": "FCM_001",
    "encryption": {
        "algorithm": "AES-256-GCM",
        "key_id": "key_001",
        "iv": "base64_encoded_iv",
        "auth_tag": "base64_encoded_auth_tag"
    },
    "encrypted_data": "base64_encoded_encrypted_payload"
}
```

### 安全事件监控

#### 安全事件类型
```json
{
    "security_events": {
        "authentication_failure": {
            "name": "认证失败",
            "event_type": "warning",
            "output_params": {
                "device_id": {
                    "name": "设备ID",
                    "data_type": "string"
                },
                "failure_reason": {
                    "name": "失败原因",
                    "data_type": "enum",
                    "enum_values": [
                        {"value": 1, "name": "证书过期"},
                        {"value": 2, "name": "签名验证失败"},
                        {"value": 3, "name": "权限不足"}
                    ]
                },
                "source_ip": {
                    "name": "来源IP",
                    "data_type": "string"
                }
            }
        },
        "unauthorized_access": {
            "name": "未授权访问",
            "event_type": "error",
            "output_params": {
                "device_id": {"name": "设备ID", "data_type": "string"},
                "requested_resource": {"name": "请求资源", "data_type": "string"},
                "user_id": {"name": "用户ID", "data_type": "string"}
            }
        },
        "security_breach": {
            "name": "安全违规",
            "event_type": "critical",
            "output_params": {
                "breach_type": {"name": "违规类型", "data_type": "string"},
                "severity_level": {"name": "严重程度", "data_type": "int32"},
                "affected_devices": {"name": "受影响设备", "data_type": "array"}
            }
        }
    }
}
```

### 密钥管理规范

#### 密钥轮换策略
```json
{
    "key_management": {
        "rotation_policy": {
            "device_certificates": {
                "rotation_interval": "365d",
                "advance_notice": "30d",
                "auto_renewal": true
            },
            "signing_keys": {
                "rotation_interval": "90d",
                "overlap_period": "7d"
            },
            "encryption_keys": {
                "rotation_interval": "30d",
                "key_derivation": "PBKDF2"
            }
        },
        "key_distribution": {
            "method": "secure_channel|key_server|manual",
            "verification_required": true,
            "backup_keys": 2
        }
    }
}
```

### 审计日志规范

#### 审计事件记录
```json
{
    "audit_log": {
        "timestamp": 1703123456789,
        "event_id": "audit_001",
        "event_type": "property_read|property_write|service_call|authentication",
        "device_id": "FCM_001",
        "user_id": "user_001",
        "source_ip": "*************",
        "thing_model_id": "acme.device.fcm.v1",
        "resource": {
            "type": "property|service|event",
            "identifier": "STEMP",
            "action": "read|write|call|subscribe"
        },
        "result": {
            "status": "success|failure",
            "error_code": 0,
            "error_message": ""
        },
        "security_context": {
            "authentication_method": "certificate",
            "authorization_level": "admin",
            "encryption_used": true
        }
    }
}
```

## 🔗 Neuron架构映射规范

### Neuron三层架构映射

#### 架构层次关系
```
Neuron架构                    物模型架构
├── Node (模块)     ←→       Device Instance (设备实例)
├── Group (设备组)   ←→       Property Group (属性组)
└── Tag (点位)      ←→       Property (属性)
```

#### 映射配置结构
```json
{
    "neuron_mapping": {
        "device_instance": {
            "device_id": "FCM_001",
            "thing_model_id": "acme.device.fcm.v1",
            "neuron_node": "FCM_001_node",
            "node_type": "acme-lora",
            "connection_config": {
                "device_eui": "1234567890ABCDEF",
                "lora_config": {
                    "frequency": 868000000,
                    "data_rate": "SF7BW125"
                }
            }
        },
        "property_groups": [
            {
                "group_id": "basic_control",
                "group_name": "基础控制组",
                "neuron_group": "default_group",
                "read_interval": 1000,
                "properties": [
                    {
                        "property_id": "ONOFF",
                        "neuron_tag": "ONOFF",
                        "tag_address": "0x01",
                        "tag_type": "BIT",
                        "tag_attribute": "RW"
                    },
                    {
                        "property_id": "SMODE",
                        "neuron_tag": "SMODE",
                        "tag_address": "0x02",
                        "tag_type": "UINT16",
                        "tag_attribute": "RW"
                    }
                ]
            },
            {
                "group_id": "temperature_control",
                "group_name": "温度控制组",
                "neuron_group": "temp_group",
                "read_interval": 2000,
                "properties": [
                    {
                        "property_id": "STEMP",
                        "neuron_tag": "STEMP",
                        "tag_address": "0x10",
                        "tag_type": "FLOAT",
                        "tag_attribute": "RW"
                    },
                    {
                        "property_id": "RTEMP",
                        "neuron_tag": "RTEMP",
                        "tag_address": "0x11",
                        "tag_type": "FLOAT",
                        "tag_attribute": "R"
                    }
                ]
            }
        ]
    }
}
```

### 属性到点位的映射机制

#### 映射表结构定义
```c
// neuron_mapping.h
typedef struct acme_tag_mapping {
    char *property_id;          // 物模型属性ID
    char *neuron_node;          // Neuron节点名
    char *neuron_group;         // Neuron组名
    char *neuron_tag;           // Neuron标签名
    char *tag_address;          // 标签地址
    neu_datatag_type_e tag_type; // 标签类型
    neu_tag_attribute_e tag_attribute; // 标签属性(R/W/RW)

    // 数据转换配置
    double scale_factor;        // 缩放因子
    double offset;              // 偏移量
    bool byte_swap;             // 字节序转换

    struct acme_tag_mapping *next;
} acme_tag_mapping_t;

typedef struct acme_device_mapping {
    char *device_id;            // 设备ID
    char *thing_model_id;       // 物模型ID
    char *neuron_node;          // 对应的Neuron节点

    acme_tag_mapping_t *tag_mappings; // 标签映射链表
    int mapping_count;          // 映射数量
} acme_device_mapping_t;
```

#### 映射管理器实现
```c
// neuron_mapping_manager.c
typedef struct acme_mapping_manager {
    acme_device_mapping_t **device_mappings;
    int device_count;
    int capacity;
} acme_mapping_manager_t;

// 创建映射管理器
acme_mapping_manager_t *acme_mapping_manager_create(void);

// 加载映射配置
int acme_mapping_manager_load_config(acme_mapping_manager_t *manager,
                                    const char *config_json);

// 根据属性ID查找对应的Neuron标签信息
acme_tag_mapping_t *acme_mapping_manager_find_tag(acme_mapping_manager_t *manager,
                                                  const char *device_id,
                                                  const char *property_id);

// 根据Neuron标签查找对应的属性ID
char *acme_mapping_manager_find_property(acme_mapping_manager_t *manager,
                                        const char *device_id,
                                        const char *neuron_node,
                                        const char *neuron_group,
                                        const char *neuron_tag);
```

### 属性操作的完整流程

#### 属性读取流程
```
1. 云端请求: /acme/SPT_GW_001/FCM_001/read/request
   {
     "properties": ["ONOFF", "STEMP", "RTEMP"]
   }

2. 映射查找:
   ONOFF  → FCM_001_node/default_group/ONOFF
   STEMP  → FCM_001_node/temp_group/STEMP
   RTEMP  → FCM_001_node/temp_group/RTEMP

3. Neuron请求:
   - NEU_REQ_READ_GROUP → FCM_001_node/default_group
   - NEU_REQ_READ_GROUP → FCM_001_node/temp_group

4. 数据聚合: 将多个组的数据合并为物模型响应

5. 响应发送: /acme/SPT_GW_001/FCM_001/read/response
```

#### 属性写入流程
```
1. 云端请求: /acme/SPT_GW_001/FCM_001/write/request
   {
     "properties": [
       {"identifier": "ONOFF", "value": 1},
       {"identifier": "STEMP", "value": 25.5}
     ]
   }

2. 映射转换:
   ONOFF=1   → FCM_001_node/default_group/ONOFF = 1
   STEMP=25.5 → FCM_001_node/temp_group/STEMP = 25.5

3. Neuron写入:
   - NEU_REQ_WRITE_TAGS → FCM_001_node/default_group [ONOFF=1]
   - NEU_REQ_WRITE_TAGS → FCM_001_node/temp_group [STEMP=25.5]

4. 结果聚合: 收集所有写入操作的结果

5. 响应发送: /acme/SPT_GW_001/FCM_001/write/response
```

### 映射配置管理

#### 动态映射配置
```json
{
    "mapping_config": {
        "version": "1.0",
        "auto_discovery": true,
        "default_mappings": {
            "FCM": {
                "node_template": "{device_id}_node",
                "default_group": "default_group",
                "property_mappings": {
                    "ONOFF": {"address": "0x01", "type": "BIT"},
                    "STEMP": {"address": "0x10", "type": "FLOAT"},
                    "RTEMP": {"address": "0x11", "type": "FLOAT"},
                    "SMODE": {"address": "0x02", "type": "UINT16"}
                }
            },
            "ECM": {
                "node_template": "{device_id}_node",
                "default_group": "sensor_group",
                "property_mappings": {
                    "TEMP": {"address": "0x20", "type": "FLOAT"},
                    "HUMI": {"address": "0x21", "type": "FLOAT"},
                    "CO2": {"address": "0x22", "type": "UINT16"}
                }
            }
        }
    }
}
```

#### 映射验证规则
```json
{
    "validation_rules": {
        "node_naming": {
            "pattern": "^[A-Z]{3}_[0-9]{3}_node$",
            "max_length": 32
        },
        "group_naming": {
            "pattern": "^[a-z_]+_group$",
            "max_length": 32
        },
        "tag_naming": {
            "pattern": "^[A-Z][A-Z0-9_]*$",
            "max_length": 32
        },
        "address_format": {
            "pattern": "^0x[0-9A-Fa-f]+$"
        }
    }
}
```

### 服务调用映射

#### 服务到多点位操作映射
```json
{
    "service_mappings": {
        "set_temperature": {
            "description": "设置温度服务映射",
            "input_mapping": {
                "temperature": {
                    "target_property": "STEMP",
                    "validation": {
                        "min": 16.0,
                        "max": 30.0
                    }
                }
            },
            "execution_sequence": [
                {
                    "step": 1,
                    "action": "write_property",
                    "property": "STEMP",
                    "value": "${input.temperature}"
                },
                {
                    "step": 2,
                    "action": "read_property",
                    "property": "STEMP",
                    "timeout": 5000
                }
            ],
            "output_mapping": {
                "result": {
                    "success_condition": "STEMP == ${input.temperature}",
                    "actual_temperature": "${STEMP.value}"
                }
            }
        },
        "power_control": {
            "description": "电源控制服务映射",
            "input_mapping": {
                "power": {
                    "target_property": "ONOFF",
                    "value_mapping": {
                        "true": 1,
                        "false": 0
                    }
                }
            },
            "execution_sequence": [
                {
                    "step": 1,
                    "action": "write_property",
                    "property": "ONOFF",
                    "value": "${mapped_value}"
                }
            ]
        }
    }
}
```

### 事件触发映射

#### 点位变化到事件映射
```json
{
    "event_mappings": {
        "temperature_alarm": {
            "trigger_conditions": [
                {
                    "property": "RTEMP",
                    "condition": "value > 30.0",
                    "event_type": "warning"
                },
                {
                    "property": "RTEMP",
                    "condition": "value < 10.0",
                    "event_type": "warning"
                }
            ],
            "event_data_mapping": {
                "current_temperature": "${RTEMP.value}",
                "threshold_type": {
                    "condition": "RTEMP.value > 30.0",
                    "true_value": 1,
                    "false_value": 2
                },
                "threshold_value": {
                    "condition": "RTEMP.value > 30.0",
                    "true_value": 30.0,
                    "false_value": 10.0
                }
            }
        }
    }
}
```

### 动态发现和自动映射机制

#### 设备自动发现规范
```json
{
    "auto_discovery": {
        "enabled": true,
        "discovery_methods": [
            {
                "method": "neuron_node_scan",
                "description": "扫描Neuron节点自动发现设备",
                "config": {
                    "scan_interval": 30000,
                    "node_pattern": "^[A-Z]{3}_[0-9]{3}_node$",
                    "exclude_nodes": ["system_node", "mqtt_node"]
                }
            },
            {
                "method": "lora_device_registration",
                "description": "LoRa设备注册时自动发现",
                "config": {
                    "auto_create_mapping": true,
                    "default_thing_model": "auto_detect"
                }
            }
        ],
        "mapping_templates": {
            "FCM": {
                "node_template": "{device_id}_node",
                "group_template": "{device_type}_group",
                "property_mappings": {
                    "ONOFF": {
                        "address_template": "0x01",
                        "type": "BIT",
                        "attribute": "RW"
                    },
                    "STEMP": {
                        "address_template": "0x10",
                        "type": "FLOAT",
                        "attribute": "RW"
                    },
                    "RTEMP": {
                        "address_template": "0x11",
                        "type": "FLOAT",
                        "attribute": "R"
                    }
                }
            },
            "ECM": {
                "node_template": "{device_id}_node",
                "group_template": "sensor_group",
                "property_mappings": {
                    "TEMP": {
                        "address_template": "0x20",
                        "type": "FLOAT",
                        "attribute": "R"
                    },
                    "HUMI": {
                        "address_template": "0x21",
                        "type": "FLOAT",
                        "attribute": "R"
                    }
                }
            }
        }
    }
}
```

#### 物模型自动匹配规则
```json
{
    "model_matching_rules": {
        "device_type_detection": [
            {
                "rule_id": "fcm_detection",
                "conditions": [
                    {"field": "device_id", "pattern": "^FCM_[0-9]{3}$"},
                    {"field": "device_eui", "length": 16},
                    {"field": "capabilities", "contains": ["temperature_control", "power_control"]}
                ],
                "thing_model_id": "acme.device.fcm.v1",
                "confidence": 0.95
            },
            {
                "rule_id": "ecm_detection",
                "conditions": [
                    {"field": "device_id", "pattern": "^ECM_[0-9]{3}$"},
                    {"field": "capabilities", "contains": ["temperature_sensor", "humidity_sensor"]}
                ],
                "thing_model_id": "acme.device.ecm.v1",
                "confidence": 0.90
            }
        ],
        "fallback_strategy": {
            "use_generic_model": true,
            "generic_model_id": "acme.device.generic.v1",
            "auto_learn_properties": true
        }
    }
}
```

#### 自动映射生成算法
```json
{
    "auto_mapping_algorithm": {
        "address_allocation": {
            "strategy": "sequential|hash_based|property_type_based",
            "base_address": "0x00",
            "address_increment": 1,
            "type_based_ranges": {
                "BIT": {"start": "0x00", "end": "0x0F"},
                "UINT16": {"start": "0x10", "end": "0x2F"},
                "FLOAT": {"start": "0x30", "end": "0x4F"},
                "STRING": {"start": "0x50", "end": "0x6F"}
            }
        },
        "group_organization": {
            "strategy": "by_access_pattern|by_data_type|by_update_frequency",
            "max_properties_per_group": 20,
            "group_naming": {
                "read_only": "{device_type}_readonly_group",
                "read_write": "{device_type}_control_group",
                "high_frequency": "{device_type}_realtime_group"
            }
        },
        "conflict_resolution": {
            "address_conflict": "auto_increment",
            "name_conflict": "add_suffix",
            "type_mismatch": "use_detected_type"
        }
    }
}
```

#### 动态映射更新机制
```json
{
    "dynamic_mapping_update": {
        "triggers": [
            {
                "event": "device_property_discovered",
                "action": "add_property_mapping",
                "auto_approve": true
            },
            {
                "event": "device_capability_changed",
                "action": "update_thing_model",
                "auto_approve": false,
                "require_confirmation": true
            },
            {
                "event": "neuron_tag_added",
                "action": "create_property_mapping",
                "auto_approve": true
            }
        ],
        "validation_rules": [
            {
                "rule": "address_uniqueness",
                "scope": "per_node",
                "action_on_violation": "auto_reassign"
            },
            {
                "rule": "property_name_uniqueness",
                "scope": "per_device",
                "action_on_violation": "add_suffix"
            }
        ],
        "rollback_mechanism": {
            "enabled": true,
            "backup_count": 5,
            "auto_rollback_on_error": true
        }
    }
}
```

#### 学习型映射优化
```json
{
    "learning_optimization": {
        "enabled": true,
        "learning_sources": [
            {
                "source": "usage_patterns",
                "description": "基于使用模式优化映射",
                "metrics": [
                    "property_access_frequency",
                    "group_read_patterns",
                    "error_rates"
                ]
            },
            {
                "source": "performance_metrics",
                "description": "基于性能指标优化",
                "metrics": [
                    "response_time",
                    "throughput",
                    "resource_usage"
                ]
            }
        ],
        "optimization_strategies": [
            {
                "strategy": "group_reorganization",
                "trigger": "high_cross_group_access",
                "action": "merge_frequently_accessed_groups"
            },
            {
                "strategy": "address_optimization",
                "trigger": "high_error_rate",
                "action": "reassign_problematic_addresses"
            }
        ],
        "learning_interval": 86400000,
        "min_data_points": 1000
    }
}
```

#### 映射发现事件通知
```json
{
    "discovery_events": {
        "device_discovered": {
            "name": "设备发现事件",
            "event_type": "info",
            "output_params": {
                "device_id": {"name": "设备ID", "data_type": "string"},
                "device_type": {"name": "设备类型", "data_type": "string"},
                "thing_model_id": {"name": "物模型ID", "data_type": "string"},
                "discovery_method": {"name": "发现方式", "data_type": "string"},
                "confidence": {"name": "匹配置信度", "data_type": "float"},
                "auto_mapped": {"name": "是否自动映射", "data_type": "bool"}
            }
        },
        "mapping_created": {
            "name": "映射创建事件",
            "event_type": "info",
            "output_params": {
                "device_id": {"name": "设备ID", "data_type": "string"},
                "property_count": {"name": "属性数量", "data_type": "int32"},
                "group_count": {"name": "组数量", "data_type": "int32"},
                "mapping_strategy": {"name": "映射策略", "data_type": "string"}
            }
        },
        "mapping_conflict": {
            "name": "映射冲突事件",
            "event_type": "warning",
            "output_params": {
                "conflict_type": {"name": "冲突类型", "data_type": "string"},
                "affected_devices": {"name": "受影响设备", "data_type": "array"},
                "resolution_action": {"name": "解决方案", "data_type": "string"}
            }
        }
    }
}
```

#### 映射质量评估
```json
{
    "mapping_quality_assessment": {
        "quality_metrics": [
            {
                "metric": "completeness",
                "description": "映射完整性",
                "calculation": "mapped_properties / total_properties",
                "weight": 0.3
            },
            {
                "metric": "accuracy",
                "description": "映射准确性",
                "calculation": "successful_operations / total_operations",
                "weight": 0.4
            },
            {
                "metric": "performance",
                "description": "性能表现",
                "calculation": "1 / average_response_time",
                "weight": 0.3
            }
        ],
        "quality_thresholds": {
            "excellent": 0.95,
            "good": 0.85,
            "acceptable": 0.70,
            "poor": 0.50
        },
        "improvement_suggestions": {
            "low_completeness": [
                "检查设备能力发现机制",
                "更新物模型定义",
                "手动补充缺失映射"
            ],
            "low_accuracy": [
                "验证地址映射正确性",
                "检查数据类型匹配",
                "优化映射算法"
            ],
            "low_performance": [
                "优化组织结构",
                "减少跨组访问",
                "调整读取频率"
            ]
        }
    }
}
```

这套动态发现和自动映射机制大大简化了物模型的部署和维护工作，实现了智能化的设备接入和映射管理。
