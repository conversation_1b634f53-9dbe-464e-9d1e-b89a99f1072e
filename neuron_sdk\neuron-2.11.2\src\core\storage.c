/**
 * NEURON IIoT System for Industry 4.0
 * Copyright (C) 2020-2022 EMQ Technologies Co., Ltd All rights reserved.
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 3 of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with this program; if not, write to the Free Software Foundation,
 * Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.
 **/
#include "errcodes.h"
#include "utils/log.h"
#include <stdio.h>
#include <stdlib.h>
#include <time.h>
#include <openssl/rand.h>
#include "adapter/storage.h"
#include "storage.h"

void manager_strorage_plugin(neu_manager_t *manager)
{
    UT_array *plugin_infos = NULL;

    plugin_infos = neu_manager_get_plugins(manager);

    int rv = neu_persister_store_plugins(plugin_infos);
    if (0 != rv) {
        nlog_error("failed to store plugins infos");
    }

    neu_persist_plugin_infos_free(plugin_infos);
    return;
}

void manager_storage_add_node(neu_manager_t *manager, const char *node)
{
    int                     rv        = 0;
    neu_persist_node_info_t node_info = {};

    rv = neu_manager_get_node_info(manager, node, &node_info);
    if (0 != rv) {
        nlog_error("unable to get adapter:%s info", node);
        return;
    }

    rv = neu_persister_store_node(&node_info);
    if (0 != rv) {
        nlog_error("failed to store adapter info");
    }

    neu_persist_node_info_fini(&node_info);
}

void manager_storage_update_node(neu_manager_t *manager, const char *node,
                                 const char *new_name)
{
    (void) manager;
    neu_persister_update_node(node, new_name);
}

void manager_storage_update_node_eui(neu_manager_t *manager, const char *node,
                                 const char *eui)
{
    (void) manager;
    neu_persister_update_node_eui(node, eui);
}

void manager_storage_del_node(neu_manager_t *manager, const char *node)
{
    (void) manager;
    neu_persister_delete_node(node);
}

void manager_storage_subscribe(neu_manager_t *manager, const char *app,
                               const char *driver, const char *group,
                               const char *params, const char *static_tags)
{
    (void) manager;
    int rv = neu_persister_store_subscription(app, driver, group, params,
                                              static_tags);
    if (0 != rv) {
        nlog_error("fail store subscription app:%s driver:%s group:%s", app,
                   driver, group);
    }
}

void manager_storage_update_subscribe(neu_manager_t *manager, const char *app,
                                      const char *driver, const char *group,
                                      const char *params,
                                      const char *static_tags)
{
    (void) manager;
    int rv = neu_persister_update_subscription(app, driver, group, params,
                                               static_tags);
    if (0 != rv) {
        nlog_error("fail update subscription app:%s driver:%s group:%s", app,
                   driver, group);
    }
}

void manager_storage_unsubscribe(neu_manager_t *manager, const char *app,
                                 const char *driver, const char *group)
{
    (void) manager;
    int rv = neu_persister_delete_subscription(app, driver, group);
    if (0 != rv) {
        nlog_error("fail delete subscription app:%s driver:%s group:%s", app,
                   driver, group);
    }
}

int manager_load_plugin(neu_manager_t *manager)
{
    UT_array *plugin_infos = NULL;

    int rv = neu_persister_load_plugins(&plugin_infos);
    if (rv != 0) {
        return rv;
    }

    utarray_foreach(plugin_infos, char **, name)
    {
        rv                    = neu_manager_add_plugin(manager, *name);
        const char *ok_or_err = (0 == rv) ? "success" : "fail";
        nlog_notice("load plugin %s, lib:%s", ok_or_err, *name);
    }

    utarray_foreach(plugin_infos, char **, name) { free(*name); }
    utarray_free(plugin_infos);

    return rv;
}

void generate_gateway_eui(char *eui) {

    unsigned char bytes[8];
    
    // 使用密码学安全的随机数生成
    if (RAND_bytes(bytes, sizeof(bytes)) != 1) {
        fprintf(stderr, "Error: Failed to generate secure random bytes\n");
        exit(1);
    }

    // 转换为十六进制字符串
    for (int i = 0; i < 8; i++) {
        sprintf(eui+(i * 2), "%02X", bytes[i]);
    }

    nlog_warn("gateway_eui generate:%s ",eui);
}

/*
* 生成网关设备
*/
static int generate_gateway_dev(neu_manager_t *manager,node_base_t *  base_info )
{
    int ret = 0;
    if(manager == NULL || base_info == NULL || manager->gw_type == NULL){
        nlog_debug("parameter error !!!");
        return -1;
    }

    if(strcmp(manager->gw_type,"SPT") == 0){        //SPT 网关
        base_info->mid = 0;        
        base_info->pid = 0;
        base_info->link = 1;
        base_info->modeType = 0;        //SPT modetype 为0
        //base_info->eui = strdup("123");
        base_info->deviceCode = 6;
        base_info->version = strdup("1.2.3");
        base_info->subType = 1;         //SPT 网关子类型为1
        base_info->online  = 1;
        base_info->userData = strdup("{}");

       
        //生成网关唯一 eui 地址
        base_info->eui = (char *)calloc(1,20);
        generate_gateway_eui(base_info->eui);
        nlog_notice("generate  gw node:%s || eui:%s || modeType:%d || plugin:%s ",manager->gw_type, base_info->eui, base_info->modeType, "ACME_GW");

    }else{
        nlog_debug("gw type:%s not support !!!",manager->gw_type);
        return -1;
    }

    return ret;
}

/*
* 功能： 从数据库加载网关Node 节点设备。
*  如果未加载成功，新建网关Node节点。
*/
int manager_load_gw(neu_manager_t *manager)
{
    int       rv         = 0;
    neu_persist_node_info_t * gw_node = NULL;

    rv = neu_persister_load_gw_node(&gw_node);
    if (0 != rv) {
        nlog_error("failed to load gw adapter infos");
    }

    node_base_t *  base_info = (node_base_t * )calloc(1,sizeof(node_base_t));
    //TODO: 未查询到网关Node 节点，需要主动生成网关设备，如果查询到则直接添加到 manager
    if(gw_node == NULL){

        //TODO: 此处创建网关设备需要单独封装一个函数实现，并同时实现 设备组和硬件点位的创建
        // ... 目前已实现设备的创建，下一步待完善网关设备组和组内硬件点的创建
        // ...

        nlog_warn("ACME gw node not found. Creeate start...\r\n");
        if(generate_gateway_dev(manager,base_info) < 0){
            nlog_warn("ACME gw node  Creeate faild ...\r\n");
            return -1;
        } 
        char * setting = strdup("{\"params\":{ } }");      //后续需要从配置文件中读取
       
        
        rv =  neu_manager_add_node(manager, manager->gw_type, "ACME_GW", setting,
                                 NEU_NODE_RUNNING_STATE_INIT, false,base_info);

        //存储设备表 node 节点信息
        if (rv == NEU_ERR_SUCCESS) {
            manager_storage_add_node(manager, manager->gw_type);
            manager->gw_node_name = strdup(manager->gw_type);      //保存网关Node 适配器名
        }else{
            nlog_error("failed to add gw node!");
        }

        //存储 setting 配置参数
        if (rv == NEU_ERR_SUCCESS) {
            adapter_storage_setting(manager->gw_type, setting);
            free(setting);
        } else {
            free(setting);
        }
    }else{
        nlog_notice("gw node load ok.");
        base_info->mid = gw_node->mid;        
        base_info->pid = gw_node->pid;
        base_info->link = gw_node->link;
        base_info->modeType = gw_node->modeType;
        base_info->eui = strdup(gw_node->eui);
        base_info->deviceCode = gw_node->deviceCode;
        base_info->version = strdup(gw_node->version);
        base_info->subType = gw_node->subType;
        base_info->online   = 1; //网关默认在线
        base_info->createTime = gw_node->createTime;
        base_info->updateTime = gw_node->updateTime;

        rv                    = neu_manager_add_node(manager, gw_node->name,
                                  gw_node->plugin_name, NULL,
                                  gw_node->state, true,base_info);
        const char *ok_or_err = (0 == rv) ? "success" : "fail";
        nlog_notice("load adapter %s type:%d, name:%s plugin:%s state:%d",
                    ok_or_err, gw_node->type, gw_node->name,
                    gw_node->plugin_name, gw_node->state);

        manager->gw_node_name = strdup(gw_node->name);      //保存网关Node 适配器名
        
        neu_persist_node_info_fini(gw_node);
        free(gw_node);
    }

    nlog_debug("ACME gw node load end ...");
    
    return rv;
}

int manager_load_node(neu_manager_t *manager)
{
    UT_array *node_infos = NULL;
    int       rv         = 0;

    rv = neu_persister_load_nodes(&node_infos);
    if (0 != rv) {
        nlog_error("failed to load adapter infos");
        return -1;
    }

    utarray_foreach(node_infos, neu_persist_node_info_t *, node_info)
    {
        node_base_t *  base_info = (node_base_t * )calloc(1,sizeof(node_base_t));
        base_info->mid = node_info->mid;        
        base_info->pid = node_info->pid;
        base_info->link = node_info->link;
        base_info->modeType = node_info->modeType;
        base_info->eui = strdup(node_info->eui);
        base_info->deviceCode = node_info->deviceCode;
        base_info->version = strdup(node_info->version);
        base_info->subType = node_info->subType;
        base_info->createTime = node_info->createTime;
        base_info->updateTime = node_info->updateTime;
        base_info->userData = strdup(node_info->userData);

        rv                    = neu_manager_add_node(manager, node_info->name,
                                  node_info->plugin_name, NULL,
                                  node_info->state, true,base_info);
        const char *ok_or_err = (0 == rv) ? "success" : "fail";
        nlog_notice("load adapter %s type:%d, name:%s plugin:%s state:%d",
                    ok_or_err, node_info->type, node_info->name,
                    node_info->plugin_name, node_info->state);
    }

    utarray_free(node_infos);
    return rv;
}

int manager_load_subscribe(neu_manager_t *manager)
{
    UT_array *nodes =
        neu_node_manager_get(manager->node_manager, NEU_NA_TYPE_APP);

    utarray_foreach(nodes, neu_resp_node_info_t *, node)
    {
        int       rv        = 0;
        UT_array *sub_infos = NULL;

        rv = neu_persister_load_subscriptions(node->node, &sub_infos);
        if (0 != rv) {
            nlog_warn("load %s subscribetion infos error", node->node);
        } else {
            utarray_foreach(sub_infos, neu_persist_subscription_info_t *, info)
            {
                uint16_t app_port = 0;
                rv                = neu_manager_subscribe(
                    manager, node->node, info->driver_name, info->group_name,
                    info->params, info->static_tags, &app_port);
                const char *ok_or_err = (0 == rv) ? "success" : "fail";
                nlog_notice(
                    "%s load subscription app:%s driver:%s grp:%s, static:%s",
                    ok_or_err, node->node, info->driver_name, info->group_name,
                    info->static_tags);
                if (0 == rv) {
                    neu_manager_send_subscribe(manager, node->node,
                                               info->driver_name,
                                               info->group_name, app_port,
                                               info->params, info->static_tags);
                }
            }

            utarray_free(sub_infos);
        }
    }

    utarray_free(nodes);
    return 0;
}
