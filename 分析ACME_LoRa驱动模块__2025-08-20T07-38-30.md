[ ] NAME:Current Task List DESCRIPTION:Root task for conversation 5eae50ac-c416-4aef-bb59-5b31f0712a9a
-[x] NAME:分析内存泄漏问题 DESCRIPTION:识别FCM设备30秒上报0x30空调状态数据导致内存从7M增长到28M的根本原因
-[x] NAME:修复UT_array内存泄漏 DESCRIPTION:修复fcm_dev_ctrl和fcm_dev_write_tag函数中UT_array actions未释放的问题
-[x] NAME:修复JSON字符串内存泄漏 DESCRIPTION:修复neu_json_decode_by_json解析后字符串未释放的问题
-[x] NAME:优化点位更新机制 DESCRIPTION:优化fcm_update_tag_by_address函数中频繁的内存分配和释放
-[x] NAME:添加内存泄漏检测 DESCRIPTION:添加内存使用监控和泄漏检测机制
-[x] NAME:实现直接推送机制 DESCRIPTION:从acme_lora适配器直接推送点位数据到acme_mqtt插件，绕过传统订阅机制
-[x] NAME:定义MQTT推送消息类型 DESCRIPTION:定义新的消息类型用于acme_lora到acme_mqtt的直接数据传输
-[x] NAME:实现acme_mqtt插件接收 DESCRIPTION:在acme_mqtt插件中实现接收和处理来自acme_lora的点位数据
-[x] NAME:集成MQTT云端推送 DESCRIPTION:将接收到的点位数据通过MQTT协议推送到云端