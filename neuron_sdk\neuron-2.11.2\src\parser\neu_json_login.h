/**
 * NEURON IIoT System for Industry 4.0
 * Copyright (C) 2020-2022 EMQ Technologies Co., Ltd All rights reserved.
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 3 of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with this program; if not, write to the Free Software Foundation,
 * Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.
 **/

/*
 * DO NOT EDIT THIS FILE MANUALLY!
 * It was automatically generated by `json-autotype`.
 */

#ifndef _NEU_JSON_API_NEU_JSON_LOGIN_H_
#define _NEU_JSON_API_NEU_JSON_LOGIN_H_

#include "define.h"
#include "json/json.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    char *pass;
    char *name;
} neu_json_login_req_t;

int  neu_json_decode_login_req(char *buf, neu_json_login_req_t **result);
void neu_json_decode_login_req_free(neu_json_login_req_t *req);

typedef struct {
    char *name;
    char *old_pass;
    char *new_pass;
} neu_json_password_req_t;

int  neu_json_decode_password_req(char *buf, neu_json_password_req_t **result);
void neu_json_decode_password_req_free(neu_json_password_req_t *req);

typedef struct {
    char name[NEU_USER_NAME_MAX_LEN + 1];
} neu_json_user_resp_t;

typedef struct {
    char *name;
    char *pass;
} neu_json_add_user_req_t;

typedef struct {
    char *name;
} neu_json_delete_user_req_t;

typedef struct {
    char *token;
} neu_json_login_resp_t;

int neu_json_encode_login_resp(void *json_object, void *param);

int  neu_json_decode_add_user_req(char *buf, neu_json_add_user_req_t **result);
void neu_json_decode_add_user_req_free(neu_json_add_user_req_t *req);
int  neu_json_decode_update_user_req(char *                    buf,
                                     neu_json_password_req_t **result);
void neu_json_decode_update_user_req_free(neu_json_password_req_t *req);

int  neu_json_decode_delete_user_req(char *                       buf,
                                     neu_json_delete_user_req_t **result);
void neu_json_decode_delete_user_req_free(neu_json_delete_user_req_t *req);
int  neu_json_encode_user_list_resp(void *json_object, void *param);

#ifdef __cplusplus
}
#endif

#endif