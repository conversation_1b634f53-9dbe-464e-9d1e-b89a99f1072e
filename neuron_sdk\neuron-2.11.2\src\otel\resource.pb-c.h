/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: opentelemetry/proto/resource/v1/resource.proto */

#ifndef PROTOBUF_C_opentelemetry_2fproto_2fresource_2fv1_2fresource_2eproto__INCLUDED
#define PROTOBUF_C_opentelemetry_2fproto_2fresource_2fv1_2fresource_2eproto__INCLUDED

#include <protobuf-c/protobuf-c.h>

PROTOBUF_C__BEGIN_DECLS

#if PROTOBUF_C_VERSION_NUMBER < 1003000
# error This file was generated by a newer version of protoc-c which is incompatible with your libprotobuf-c headers. Please update your headers.
#elif 1004000 < PROTOBUF_C_MIN_COMPILER_VERSION
# error This file was generated by an older version of protoc-c which is incompatible with your libprotobuf-c headers. Please regenerate this file with a newer version of protoc-c.
#endif

#include "common.pb-c.h"

typedef struct Opentelemetry__Proto__Resource__V1__Resource
    Opentelemetry__Proto__Resource__V1__Resource;

/* --- enums --- */

/* --- messages --- */

/*
 * Resource information.
 */
struct Opentelemetry__Proto__Resource__V1__Resource {
    ProtobufCMessage base;
    /*
     * Set of attributes that describe the resource.
     * Attribute keys MUST be unique (it is not allowed to have more than one
     * attribute with the same key).
     */
    size_t                                       n_attributes;
    Opentelemetry__Proto__Common__V1__KeyValue **attributes;
    /*
     * dropped_attributes_count is the number of dropped attributes. If the
     * value is 0, then no attributes were dropped.
     */
    uint32_t dropped_attributes_count;
};
#define OPENTELEMETRY__PROTO__RESOURCE__V1__RESOURCE__INIT             \
    {                                                                  \
        PROTOBUF_C_MESSAGE_INIT(                                       \
            &opentelemetry__proto__resource__v1__resource__descriptor) \
        , 0, NULL, 0                                                   \
    }

/* Opentelemetry__Proto__Resource__V1__Resource methods */
void opentelemetry__proto__resource__v1__resource__init(
    Opentelemetry__Proto__Resource__V1__Resource *message);
size_t opentelemetry__proto__resource__v1__resource__get_packed_size(
    const Opentelemetry__Proto__Resource__V1__Resource *message);
size_t opentelemetry__proto__resource__v1__resource__pack(
    const Opentelemetry__Proto__Resource__V1__Resource *message, uint8_t *out);
size_t opentelemetry__proto__resource__v1__resource__pack_to_buffer(
    const Opentelemetry__Proto__Resource__V1__Resource *message,
    ProtobufCBuffer *                                   buffer);
Opentelemetry__Proto__Resource__V1__Resource *
opentelemetry__proto__resource__v1__resource__unpack(
    ProtobufCAllocator *allocator, size_t len, const uint8_t *data);
void opentelemetry__proto__resource__v1__resource__free_unpacked(
    Opentelemetry__Proto__Resource__V1__Resource *message,
    ProtobufCAllocator *                          allocator);
/* --- per-message closures --- */

typedef void (*Opentelemetry__Proto__Resource__V1__Resource_Closure)(
    const Opentelemetry__Proto__Resource__V1__Resource *message,
    void *                                              closure_data);

/* --- services --- */

/* --- descriptors --- */

extern const ProtobufCMessageDescriptor
    opentelemetry__proto__resource__v1__resource__descriptor;

PROTOBUF_C__END_DECLS

#endif /* PROTOBUF_C_opentelemetry_2fproto_2fresource_2fv1_2fresource_2eproto__INCLUDED \
        */
