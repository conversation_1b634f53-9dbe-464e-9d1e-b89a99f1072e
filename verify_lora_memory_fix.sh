#!/bin/bash

# LoRa内存泄漏修复验证脚本
# 使用方法: ./verify_lora_memory_fix.sh

echo "=== LoRa内存泄漏修复验证脚本 ==="
echo "启动时间: $(date)"
echo

# 配置参数
NEURON_PROCESS="neuron"
LOG_FILE="/tmp/lora_memory_verification.log"
MONITOR_DURATION=3600  # 监控1小时
CHECK_INTERVAL=60      # 每分钟检查一次

# 创建日志文件
echo "# LoRa内存监控日志" > $LOG_FILE
echo "# 时间戳,RSS内存(KB),VSZ内存(KB),CPU使用率(%)" >> $LOG_FILE

# 获取neuron进程PID
get_neuron_pid() {
    PID=$(pidof $NEURON_PROCESS)
    if [ -z "$PID" ]; then
        echo "错误: 未找到neuron进程"
        exit 1
    fi
    echo $PID
}

# 获取进程内存信息
get_memory_info() {
    local pid=$1
    if [ -z "$pid" ]; then
        echo "0,0,0"
        return
    fi
    
    # 从/proc/pid/status获取详细内存信息
    if [ -f "/proc/$pid/status" ]; then
        local vmrss=$(grep "VmRSS:" /proc/$pid/status | awk '{print $2}')
        local vmsize=$(grep "VmSize:" /proc/$pid/status | awk '{print $2}')
        local cpu=$(ps -p $pid -o %cpu --no-headers | tr -d ' ')
        
        echo "${vmrss:-0},${vmsize:-0},${cpu:-0}"
    else
        echo "0,0,0"
    fi
}

# 分析内存趋势
analyze_memory_trend() {
    local log_file=$1
    echo
    echo "=== 内存使用趋势分析 ==="
    
    if [ ! -f "$log_file" ]; then
        echo "日志文件不存在: $log_file"
        return
    fi
    
    # 获取初始和最终内存值
    local first_line=$(grep -v "^#" $log_file | head -1)
    local last_line=$(grep -v "^#" $log_file | tail -1)
    
    if [ -z "$first_line" ] || [ -z "$last_line" ]; then
        echo "日志数据不足，无法分析趋势"
        return
    fi
    
    local initial_rss=$(echo $first_line | cut -d',' -f2)
    local final_rss=$(echo $last_line | cut -d',' -f2)
    local initial_time=$(echo $first_line | cut -d',' -f1)
    local final_time=$(echo $last_line | cut -d',' -f1)
    
    local rss_change=$((final_rss - initial_rss))
    local time_duration=$((final_time - initial_time))
    
    echo "监控时长: $((time_duration / 60)) 分钟"
    echo "初始RSS内存: ${initial_rss} KB"
    echo "最终RSS内存: ${final_rss} KB"
    echo "内存变化: ${rss_change} KB"
    
    if [ $rss_change -gt 1024 ]; then
        echo "⚠️  内存增长较大 (${rss_change} KB)，可能存在泄漏"
    elif [ $rss_change -gt 512 ]; then
        echo "⚠️  内存有一定增长 (${rss_change} KB)，需要继续观察"
    elif [ $rss_change -gt -512 ] && [ $rss_change -lt 512 ]; then
        echo "✅ 内存使用稳定 (变化${rss_change} KB)"
    else
        echo "📉 内存使用下降 (${rss_change} KB)"
    fi
    
    # 计算平均内存增长率
    if [ $time_duration -gt 0 ]; then
        local growth_rate=$((rss_change * 3600 / time_duration))  # KB/小时
        echo "内存增长率: ${growth_rate} KB/小时"
        
        if [ $growth_rate -gt 1024 ]; then
            echo "🚨 内存增长率过高，建议检查代码"
        elif [ $growth_rate -gt 256 ]; then
            echo "⚠️  内存增长率偏高，建议持续监控"
        else
            echo "✅ 内存增长率正常"
        fi
    fi
}

# 检查LoRa相关日志
check_lora_logs() {
    echo
    echo "=== LoRa相关日志检查 ==="
    
    # 检查内存监控日志
    if [ -f "/tmp/lora_memory_*.log" ]; then
        echo "发现LoRa内存监控日志:"
        ls -la /tmp/lora_memory_*.log
        
        echo
        echo "最近的内存监控记录:"
        tail -5 /tmp/lora_memory_*.log
    else
        echo "未找到LoRa内存监控日志"
    fi
    
    # 检查系统日志中的内存相关错误
    echo
    echo "检查系统日志中的内存错误:"
    if command -v journalctl >/dev/null 2>&1; then
        journalctl -u neuron --since "1 hour ago" | grep -i "memory\|leak\|malloc\|free" | tail -5
    else
        dmesg | grep -i "memory\|oom" | tail -5
    fi
}

# 生成内存使用报告
generate_report() {
    local log_file=$1
    local report_file="/tmp/lora_memory_report_$(date +%Y%m%d_%H%M%S).txt"
    
    echo "=== LoRa内存泄漏修复验证报告 ===" > $report_file
    echo "生成时间: $(date)" >> $report_file
    echo >> $report_file
    
    # 添加趋势分析
    analyze_memory_trend $log_file >> $report_file
    
    # 添加日志检查结果
    check_lora_logs >> $report_file
    
    # 添加建议
    echo >> $report_file
    echo "=== 建议 ===" >> $report_file
    echo "1. 如果内存使用稳定，说明修复有效" >> $report_file
    echo "2. 如果内存仍在增长，需要进一步排查其他泄漏点" >> $report_file
    echo "3. 建议长期监控(7天以上)以确认稳定性" >> $report_file
    echo "4. 可以使用valgrind等工具进行更详细的内存分析" >> $report_file
    
    echo "详细报告已保存到: $report_file"
}

# 主监控循环
main_monitor() {
    local pid=$(get_neuron_pid)
    echo "监控neuron进程 (PID: $pid)"
    echo "监控时长: $((MONITOR_DURATION / 60)) 分钟"
    echo "检查间隔: $CHECK_INTERVAL 秒"
    echo "日志文件: $LOG_FILE"
    echo
    
    local start_time=$(date +%s)
    local end_time=$((start_time + MONITOR_DURATION))
    local count=0
    
    while [ $(date +%s) -lt $end_time ]; do
        local current_time=$(date +%s)
        local memory_info=$(get_memory_info $pid)
        
        # 记录到日志文件
        echo "$current_time,$memory_info" >> $LOG_FILE
        
        # 显示当前状态
        local rss=$(echo $memory_info | cut -d',' -f1)
        local vsz=$(echo $memory_info | cut -d',' -f2)
        local cpu=$(echo $memory_info | cut -d',' -f3)
        
        count=$((count + 1))
        echo "[$count] $(date '+%H:%M:%S') - RSS: ${rss}KB, VSZ: ${vsz}KB, CPU: ${cpu}%"
        
        # 检查进程是否还在运行
        if ! kill -0 $pid 2>/dev/null; then
            echo "警告: neuron进程已停止"
            break
        fi
        
        sleep $CHECK_INTERVAL
    done
    
    echo
    echo "监控完成"
}

# 快速检查模式
quick_check() {
    echo "=== 快速内存检查 ==="
    local pid=$(get_neuron_pid)
    echo "neuron进程 PID: $pid"
    
    # 显示当前内存使用
    echo
    echo "当前内存使用:"
    ps -p $pid -o pid,ppid,rss,vsz,pmem,pcpu,cmd --no-headers
    
    # 显示详细内存信息
    echo
    echo "详细内存信息:"
    if [ -f "/proc/$pid/status" ]; then
        grep -E "VmRSS|VmSize|VmPeak|VmData|VmStk" /proc/$pid/status
    fi
    
    # 检查LoRa日志
    check_lora_logs
}

# 显示使用帮助
show_help() {
    echo "LoRa内存泄漏修复验证脚本"
    echo
    echo "使用方法:"
    echo "  $0 [选项]"
    echo
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  -q, --quick    快速检查当前内存状态"
    echo "  -m, --monitor  启动长期监控 (默认)"
    echo "  -a, --analyze  分析现有日志文件"
    echo "  -t TIME        设置监控时长(秒，默认3600)"
    echo "  -i INTERVAL    设置检查间隔(秒，默认60)"
    echo
    echo "示例:"
    echo "  $0 -q                    # 快速检查"
    echo "  $0 -m -t 7200 -i 30     # 监控2小时，每30秒检查一次"
    echo "  $0 -a                    # 分析现有日志"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -q|--quick)
            quick_check
            exit 0
            ;;
        -a|--analyze)
            analyze_memory_trend $LOG_FILE
            check_lora_logs
            exit 0
            ;;
        -t)
            MONITOR_DURATION="$2"
            shift 2
            ;;
        -i)
            CHECK_INTERVAL="$2"
            shift 2
            ;;
        -m|--monitor)
            # 默认行为，不需要特殊处理
            shift
            ;;
        *)
            echo "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 默认执行监控
main_monitor
analyze_memory_trend $LOG_FILE
generate_report $LOG_FILE

echo
echo "验证完成！"
