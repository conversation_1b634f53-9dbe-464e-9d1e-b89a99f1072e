name: compile

on: 
  push:
  pull_request:
  release:
    types:
      - published

env:
  VERSION: 2.11.2
  UI_VERSION: 2.6.1
  BRANCH: v2.11

jobs:
  cross-build:
    runs-on: ubuntu-24.04
    strategy:
      matrix:
        arch:
          - [aarch64-linux-gnu, aarch64, arm64]
          - [arm-linux-gnueabihf, armv4, armhf]
          - [x86_64-linux-gnu, x86_64, amd64]
    container: ghcr.io/neugates/build:${{ matrix.arch[1] }}-v2.11

    steps:
      - uses: actions/checkout@v4
        with:
          submodules: recursive

      - name: compile
        run: |
          git config --global --add safe.directory $(pwd)
          mkdir -p build && cd build
          cmake .. -DCMAKE_TOOLCHAIN_FILE=../cmake/${{ matrix.arch[0] }}.cmake \
            -DCMAKE_BUILD_TYPE=Release -DDISABLE_UT=ON
          make -j4

      - name: fetch dashboard
        uses: dsaltares/fetch-gh-release-asset@master
        with:
          repo: "emqx/neuron-dashboard-src"
          version: "tags/${{ env.UI_VERSION }}"
          file: "neuron-dashboard.zip"
          target: "neuron-dashboard.zip"

      - name: unpack dashboard
        run: |
          unzip neuron-dashboard.zip -d ./build

      - name: Create neuron sdk package
        run: |
          ./package-sdk.sh -p ${{ matrix.arch[2] }} -n neuron-sdk-$VERSION

      - uses: actions/upload-artifact@v4
        with:
          name: neuron-sdk-${{ matrix.arch[2] }}
          path: |
            neuron-sdk-*.tar.gz

  build-debug-sdk:
    runs-on: ubuntu-24.04
    strategy:
      matrix:
        arch:
          - [x86_64-linux-gnu, x86_64, amd64]
    container: ghcr.io/neugates/build:${{ matrix.arch[1] }}-v2.11

    steps:
      - uses: actions/checkout@v4
        with:
          submodules: recursive

      - name: compile
        run: |
          git config --global --add safe.directory $(pwd)
          mkdir -p build && cd build
          cmake .. -DCMAKE_TOOLCHAIN_FILE=../cmake/${{ matrix.arch[0] }}.cmake \
            -DDISABLE_UT=ON
          make -j4

      - name: Create neuron sdk package
        run: |
          ./package-sdk.sh -p ${{ matrix.arch[2] }} -n neuron-sdk-debug-$VERSION

      - uses: actions/upload-artifact@v4
        with:
          name: neuron-sdk-debug
          path: |
            neuron-sdk-debug-*.tar.gz
  
  create_update_daily_release:
    runs-on: ubuntu-latest
    if: github.event_name == 'push'
    needs: [cross-build, build-debug-sdk]
    steps:
      - name: delete old daily-pre-release
        uses: dev-drprasad/delete-tag-and-release@v0.2.1
        with:
          delete_release: true
          tag_name: ${{ env.BRANCH }}-daily
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - uses: actions/download-artifact@v4
        with:
          name: neuron-sdk-debug

      - uses: actions/download-artifact@v4
        with:
          name: neuron-sdk-arm64
      - uses: actions/download-artifact@v4
        with:
          name: neuron-sdk-armhf
      - uses: actions/download-artifact@v4
        with:
          name: neuron-sdk-amd64
      
      - uses: softprops/action-gh-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          name: Daily Release(${{ env.BRANCH }})
          tag_name: ${{ env.BRANCH }}-daily
          draft: false
          prerelease: true
          files: |
            neuron-sdk-*