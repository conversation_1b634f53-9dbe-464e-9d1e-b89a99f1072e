(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-d5975f04"],{"0974":function(e,t,r){},"2a59":function(e,t,r){"use strict";r.d(t,"j",(function(){return o})),r.d(t,"f",(function(){return i})),r.d(t,"c",(function(){return s})),r.d(t,"m",(function(){return p})),r.d(t,"g",(function(){return l})),r.d(t,"h",(function(){return f})),r.d(t,"d",(function(){return m})),r.d(t,"a",(function(){return d})),r.d(t,"k",(function(){return b})),r.d(t,"i",(function(){return g})),r.d(t,"e",(function(){return v})),r.d(t,"b",(function(){return j})),r.d(t,"l",(function(){return O}));var n=r("5530"),a=r("1da1"),u=(r("96cf"),r("d3b7"),r("d81d"),r("b0c0"),r("a9e3"),r("e423")),c=r("2de2"),o=function(){var e=Object(a["a"])(regeneratorRuntime.mark((function e(){var t,r;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,u["a"].get("/template");case 2:return t=e.sent,r=t.data,e.abrupt("return",Promise.resolve((null===r||void 0===r?void 0:r.templates)||[]));case 5:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),i=function(){var e=Object(a["a"])(regeneratorRuntime.mark((function e(t){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",u["a"].delete("/template",{params:{name:t}}));case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),s=function(){var e=Object(a["a"])(regeneratorRuntime.mark((function e(t){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",u["a"].post("/template",t));case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),p=function(){var e=Object(a["a"])(regeneratorRuntime.mark((function e(t){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",u["a"].post("/template",t));case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),l=function(){var e=Object(a["a"])(regeneratorRuntime.mark((function e(t){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",u["a"].get("/template",{params:{name:t}}));case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),f=function(){var e=Object(a["a"])(regeneratorRuntime.mark((function e(t){var r,a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,u["a"].get("/template/group",{params:{name:t}});case 2:return r=e.sent,a=r.data,e.abrupt("return",Promise.resolve(((null===a||void 0===a?void 0:a.groups)||[]).map((function(e){return Object(n["a"])(Object(n["a"])({},e),{},{group:e.name})}))));case 5:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),m=function(){var e=Object(a["a"])(regeneratorRuntime.mark((function e(t,r){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",u["a"].delete("/template/group",{data:{template:t,group:r}}));case 1:case"end":return e.stop()}}),e)})));return function(t,r){return e.apply(this,arguments)}}(),d=function(){var e=Object(a["a"])(regeneratorRuntime.mark((function e(t){var r,n,a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return r=t.group,n=t.interval,a=t.template,e.abrupt("return",u["a"].post("/template/group",{group:r,template:a,interval:Number(n)}));case 2:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),b=function(){var e=Object(a["a"])(regeneratorRuntime.mark((function e(t){var r,n,a,c;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return r=t.group,n=t.interval,a=t.template,c=t.new_name,e.abrupt("return",u["a"].put("/template/group",{group:r,template:a,interval:Number(n),new_name:c}));case 2:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),g=function(){var e=Object(a["a"])(regeneratorRuntime.mark((function e(){var t,r,n,a=arguments;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=a.length>0&&void 0!==a[0]?a[0]:{},e.next=3,u["a"].get("/template/tag",{params:t});case 3:return r=e.sent,n=r.data,e.abrupt("return",Promise.resolve(n.tags||[]));case 6:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),v=function(e){return u["a"].delete("/template/tag",{data:e})},j=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r={_handleCustomError:!0,timeout:t?c["a"]+100:c["a"]};return u["a"].post("/template/tag",e,Object(n["a"])({},r))},O=function(e,t,r){return u["a"].put("/template/tag",{template:e,group:t,tags:[r]})}},df41:function(e,t,r){"use strict";r("0974")},efb5:function(e,t,r){"use strict";r.r(t);var n=r("7a23"),a=r("9978"),u=r("1da1"),c=(r("96cf"),r("d3b7"),r("25f0"),r("a434"),r("47e2")),o=r("6c02"),i=r("d472"),s=r("2a59"),p=r("b3bd"),l=r("8c45"),f=r("73ec"),m=function(){var e=Object(o["c"])(),t=Object(o["d"])(),r=Object(c["b"])(),a=r.t,m=Object(p["b"])(),d=m.groupName,b=m.sliceTagList,g=m.parseTagData,v=m.handleValidTagFormError,j=Object(n["ref"])(),O=Object(n["ref"])([]),h=Object(p["a"])(),w=h.createRawTagForm,x=Object(n["ref"])([w()]),R=Object(n["ref"])({tagList:x.value}),k=Object(n["ref"])(!1),y=Object(n["ref"])({}),T=Object(n["computed"])((function(){return e.params.template.toString()})),C=Object(l["b"])(),N=C.getTemplatePluginInfo,V=function(){var e=Object(u["a"])(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,N();case 2:y.value=e.sent;case 3:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),I=function(e){e&&O.value.push(e)},_=function(){R.value.tagList.push(w())},P=function(e){R.value.tagList.splice(e,1)},S=function(e,t){0!==e?(i["EmqxMessage"].error(a("config.tagPartAddedFailedPopup",[Object(f["i"])(t)])),R.value.tagList=b(R.value.tagList,e)):Object(f["o"])(t)},D=function(){var e=Object(u["a"])(regeneratorRuntime.mark((function e(){var t,r,n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,g(R.value.tagList);case 3:return t=e.sent,e.next=6,Object(s["b"])({tags:t,template:T.value,group:d.value});case 6:return e.abrupt("return",Promise.resolve());case 9:return e.prev=9,e.t0=e["catch"](0),r=e.t0.data,n=void 0===r?{}:r,0!==n.error&&void 0!==n.index&&S(n.index,n.error),e.abrupt("return",Promise.reject(e.t0));case 14:case"end":return e.stop()}}),e,null,[[0,9]])})));return function(){return e.apply(this,arguments)}}(),E=function(){var e=Object(u["a"])(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,j.value.validate();case 3:return e.abrupt("return",Promise.resolve());case 6:return e.prev=6,e.t0=e["catch"](0),v(e.t0),e.abrupt("return",Promise.reject(e.t0));case 10:case"end":return e.stop()}}),e,null,[[0,6]])})));return function(){return e.apply(this,arguments)}}(),F=function(){var e=Object(u["a"])(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,k.value=!0,e.next=4,E();case 4:return e.next=6,D();case 6:i["EmqxMessage"].success(a("common.createSuccess")),t.push({name:"TemplateGroupTag"}),e.next=13;break;case 10:e.prev=10,e.t0=e["catch"](0),console.error(e.t0);case 13:return e.prev=13,k.value=!1,e.finish(13);case 16:case"end":return e.stop()}}),e,null,[[0,10,13,16]])})));return function(){return e.apply(this,arguments)}}(),L=function(){t.back()};return V(),{pluginInfo:y,tagFormRef:j,setFormRef:I,createRawTagForm:w,formData:R,isSubmitting:k,addTagItem:_,deleteTagItem:P,cancel:L,submit:F}},d=function(e){return Object(n["pushScopeId"])("data-v-1038a1cd"),e=e(),Object(n["popScopeId"])(),e},b={class:"add-tag"},g={class:"card-title"},v=d((function(){return Object(n["createElementVNode"])("i",{class:"iconfont iconcreate"},null,-1)})),j=Object(n["defineComponent"])({setup:function(e){var t=m(),r=t.pluginInfo,u=t.formData,c=t.isSubmitting,o=t.addTagItem,i=t.deleteTagItem,s=t.tagFormRef,p=t.cancel,l=t.submit;return function(e,t){var f=Object(n["resolveComponent"])("emqx-card"),m=Object(n["resolveComponent"])("emqx-button");return Object(n["openBlock"])(),Object(n["createElementBlock"])("div",b,[Object(n["createVNode"])(f,{shadow:"none"},{default:Object(n["withCtx"])((function(){return[Object(n["createElementVNode"])("h3",g,Object(n["toDisplayString"])(e.$t("config.addTags")),1)]})),_:1}),Object(n["createVNode"])(f,{shadow:"none"},{default:Object(n["withCtx"])((function(){return[Object(n["createVNode"])(a["a"],{ref:function(e,t){t["tagFormRef"]=e,Object(n["isRef"])(s)&&(s.value=e)},data:Object(n["unref"])(u),"node-plugin-info":Object(n["unref"])(r),onDeleteTagItem:Object(n["unref"])(i)},null,8,["data","node-plugin-info","onDeleteTagItem"]),Object(n["createVNode"])(m,{class:"btn-add-tag",onClick:Object(n["unref"])(o)},{default:Object(n["withCtx"])((function(){return[v,Object(n["createElementVNode"])("span",null,Object(n["toDisplayString"])(e.$t("common.add")),1)]})),_:1},8,["onClick"])]})),_:1}),Object(n["createVNode"])(f,{shadow:"none",class:"footer add-tag-ft"},{default:Object(n["withCtx"])((function(){return[Object(n["createVNode"])(m,{type:"primary",onClick:Object(n["unref"])(l),disabled:0===Object(n["unref"])(u).tagList.length,loading:Object(n["unref"])(c)},{default:Object(n["withCtx"])((function(){return[Object(n["createTextVNode"])(Object(n["toDisplayString"])(e.$t("common.create")),1)]})),_:1},8,["onClick","disabled","loading"]),Object(n["createVNode"])(m,{onClick:Object(n["unref"])(p)},{default:Object(n["withCtx"])((function(){return[Object(n["createTextVNode"])(Object(n["toDisplayString"])(e.$t("common.cancel")),1)]})),_:1},8,["onClick"])]})),_:1})])}}}),O=(r("df41"),r("6b0d")),h=r.n(O);const w=h()(j,[["__scopeId","data-v-1038a1cd"]]);t["default"]=w}}]);