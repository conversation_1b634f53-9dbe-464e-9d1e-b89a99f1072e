cmake_minimum_required(VERSION 3.12)

project(plugin-acme-gw)
enable_language(C)
set(CMAKE_C_STANDARD 99)
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -fno-common")
find_package(Threads)


set(LIBRARY_OUTPUT_PATH "${CMAKE_BINARY_DIR}/plugins")
#set(SDK_PATH "/opt/externs/libs/arm-linux-gnueabihf")
set(SDK_PATH "${CMAKE_SOURCE_DIR}/../../../neuron_sdk/neuron-2.11.2")

file(COPY ${CMAKE_SOURCE_DIR}/acme-gw.json DESTINATION ${CMAKE_BINARY_DIR}/plugins/schema/)

# 递归匹配所有子目录的.c文件
file(GLOB_RECURSE C_SOURCES 
    CONFIGURE_DEPENDS
    "lora_tty_driver/*.c"
	"${CMAKE_SOURCE_DIR}/../comman/*.c"
	"lora_protocol/*.c"
	"lora_device_manager/*.c"
)

add_library(${PROJECT_NAME} SHARED
  acme_gw.c
  ${C_SOURCES}
)

target_include_directories(${PROJECT_NAME} PRIVATE 
  ${SDK_PATH}/include/neuron)
include_directories(${SDK_PATH}/include)
include_directories(${CMAKE_SOURCE_DIR})
include_directories(${CMAKE_SOURCE_DIR}/../comman)
include_directories(${CMAKE_SOURCE_DIR}/lora_tty_driver)
include_directories(${CMAKE_SOURCE_DIR}/lora_protocol) 
include_directories(${CMAKE_SOURCE_DIR}/lora_device_manager) 


link_directories(/opt/externs/libs/arm-linux-gnueabihf/lib)

#target_link_libraries(${PROJECT_NAME} /opt/externs/libs/arm-linux-gnueabihf/lib/libneuron-base.so)
target_link_libraries(${PROJECT_NAME} ${SDK_PATH}/build/libneuron-base.so)
target_link_libraries(${PROJECT_NAME} ${CMAKE_THREAD_LIBS_INIT})

