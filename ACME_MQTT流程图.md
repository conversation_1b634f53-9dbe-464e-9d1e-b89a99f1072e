sequenceDiagram
    participant FCM as FCM设备
    participant Lo<PERSON><PERSON> as acme_lora驱动
    participant MQTT as acme_mqtt插件
    participant Cloud as MQTT云端服务器
    
    Note over FCM,Cloud: ACME_MQTT 数据传输流程
    
    %% 设备数据上报
    FCM->>LoRa: 0x30空调状态数据
    Note right of LoRa: 30秒周期上报
    
    LoRa->>LoRa: 协议解析
    LoRa->>LoRa: 点位更新
    
    %% 直接推送机制
    LoRa->>MQTT: NEU_REQ_ACME_MQTT_DATA_PUSH
    Note right of MQTT: 直接推送，绕过订阅
    
    MQTT->>MQTT: 参数验证
    MQTT->>MQTT: MQTT客户端状态检查
    
    alt MQTT连接正常
        MQTT->>MQTT: 构造JSON数据
        Note right of MQTT: {"timestamp":..., "driver":"FCM_001", "tags":[...]}
        
        MQTT->>MQTT: 生成主题
        Note right of MQTT: /neuron/SPT_GW_001/acme/1234567890ABCDEF/data
        
        MQTT->>Cloud: MQTT Publish (QoS1)
        Cloud-->>MQTT: PUBACK
        
        MQTT->>MQTT: 更新统计指标
        Note right of MQTT: 传输成功计数
        
    else MQTT连接断开
        MQTT->>MQTT: 数据缓存
        Note right of MQTT: 等待重连后发送
    end
    
    MQTT->>MQTT: 资源清理
    Note right of MQTT: 释放内存，防止泄漏
    
    %% 传统订阅模式对比
    Note over FCM,Cloud: 传统订阅模式 (对比)
    LoRa->>LoRa: 定时器触发
    LoRa->>MQTT: 订阅组数据
    MQTT->>Cloud: MQTT Publish
    Note right of Cloud: 延迟更高，资源消耗更大