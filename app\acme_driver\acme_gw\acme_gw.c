/**
 * NEURON IIoT System for Industry 4.0
 * Copyright (C) 2020-2024 EMQ Technologies Co., Ltd All rights reserved.
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 3 of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with this program; if not, write to the Free Software Foundation,
 * Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.
 **/

#include "otel/otel_manager.h"
#include "utils/asprintf.h"
#include "utils/time.h"
#include "acme_gw.h"
#include <stdlib.h>

#include <neuron.h>

#include "errcodes.h"
#include "lora_protocol.h"
#include "acme_lora_driver.h"


static neu_plugin_t *gw_driver_open(void);

static int gw_driver_close(neu_plugin_t *plugin);
static int gw_driver_init(neu_plugin_t *plugin, bool load);
static int gw_driver_uninit(neu_plugin_t *plugin);
static int gw_driver_start(neu_plugin_t *plugin);
static int gw_driver_stop(neu_plugin_t *plugin);
static int gw_driver_config(neu_plugin_t *plugin, const char *config);
static int gw_driver_request(neu_plugin_t *plugin, neu_reqresp_head_t *head,
                          void *data);
static int gw_driver_create(neu_plugin_t *plugin, void *data);

static int gw_driver_validate_tag(neu_plugin_t *plugin, neu_datatag_t *tag);
static int gw_driver_group_timer(neu_plugin_t *plugin, neu_plugin_group_t *group);
static int gw_driver_write(neu_plugin_t *plugin, void *req, neu_datatag_t *tag,
                        neu_value_u value);
static int gw_driver_write_tags(neu_plugin_t *plugin, void *req, UT_array *tags);

static const neu_plugin_intf_funs_t acme_gw_plugin_intf_funs = {
    .open    = gw_driver_open,
    .close   = gw_driver_close,
    .init    = gw_driver_init,
    .uninit  = gw_driver_uninit,
    .start   = gw_driver_start,
    .stop    = gw_driver_stop,
    .setting = gw_driver_config,
    .request = gw_driver_request,
    .create  = gw_driver_create,

    .driver.validate_tag  = gw_driver_validate_tag,
    .driver.group_timer   = gw_driver_group_timer,
    .driver.group_sync    = gw_driver_group_timer,
    .driver.write_tag     = gw_driver_write,
    .driver.tag_validator = NULL,
    .driver.write_tags    = gw_driver_write_tags,
    .driver.add_tags      = NULL,
    .driver.load_tags     = NULL,
    .driver.del_tags      = NULL,
};

const neu_plugin_module_t neu_plugin_module = {
    .version     = NEURON_PLUGIN_VER_1_0,
    .schema      = "acme-gw",
    .module_name = "ACME_GW",
    .module_descr =
        "This plugin is used for ACME gateway devices. Supports gateways such as BEC and IGW. "
        "Support hardware IO point or network, software point addition, configuration, and acquisition ",
    .module_descr_zh =
        "该插件用于ACME 网关设备。支持BEC 、IGW等网关。 "
        " 支持硬件IO点位或网络、软件点位添加、配置和采集",
    .intf_funs = &acme_gw_plugin_intf_funs,
    .kind      = NEU_PLUGIN_KIND_SYSTEM,
    .type      = NEU_NA_TYPE_DRIVER,
    .display   = true,
    .single    = false,
};


/*********************************** gw 网关插件接口实现 *****************************************/
/*
* 网关node 节点创建时，根据具体网关设备型号，如 SPT  BEC 等进行默认组和响应硬件点位的创建
* 或者其他类型的设备在创建时候需要进行的其他任务，在此接口进行封装并调用
*/
static int gw_driver_create(neu_plugin_t *plugin, void *data)
{
    (void) plugin;
    (void) data;

    neu_err_code_e error = NEU_ERR_SUCCESS;





    return error;
}

static neu_plugin_t *gw_driver_open(void)
{
    neu_plugin_t *plugin = calloc(1, sizeof(neu_plugin_t));

    neu_plugin_common_init(&plugin->common);

    return plugin;
}

static int gw_driver_close(neu_plugin_t *plugin)
{
    free(plugin);

    return 0;
}

/*
 * 网关节点初始化  同时进行Lora 串口配置、硬件IO配置等 
 */
static int gw_driver_init(neu_plugin_t *plugin, bool load)
{
    (void) load;
    plugin->subDevice_manager = neu_devices_manager_create();
    plugin->pair_mid = -1;

    acme_lora_init(plugin);

    
    lora_tty_start(plugin);

    plog_notice(plugin, "%s init success", plugin->common.name);
    return 0;
}

static int gw_driver_uninit(neu_plugin_t *plugin)
{
    return 0;
}

/**
 * 网关节点启动 同时启动串口接收线程
 */
static int gw_driver_start(neu_plugin_t *plugin)
{
    //lora_tty_start(plugin);
   
    acme_lora_driver_stop_search(plugin);   //适配器启动运行，停止配网

    return 0;
}

/*
* 网关节点停止运行
*/
static int gw_driver_stop(neu_plugin_t *plugin)
{
    //停止串口功能
    //lora_tty_stop(plugin);
    


    lora_parameter_setting(plugin); //适配器停止工作，启动配网
    return 0;
}

static int gw_driver_config(neu_plugin_t *plugin, const char *config)
{
    wave_mesh_broadcast_systime(plugin);
    //lora_parameter_setting(plugin);
    return 0;
}

static int gw_driver_request(neu_plugin_t *plugin, neu_reqresp_head_t *head,
                          void *data)
{
    (void) plugin;
    (void) head;
    (void) data;

    neu_err_code_e error = NEU_ERR_SUCCESS;


    switch (head->type) {
    case NEU_RESP_ERROR:{
        neu_resp_error_t *errInfo = (neu_resp_error_t *)data;
        plog_notice(plugin, "manager notify error code %d ",errInfo->error);
        break;    
    }
    case NEU_REQ_ACME_DEV_REGISTER:{
        //TODO: 子设备注册到网关...
        neu_acme_subDev_reg_t *dev = (neu_acme_subDev_reg_t *)data;
        neu_device_register(plugin, dev);
        break;
    }
    case NEU_REQ_ACME_DEV_LEAVE:{
        //子设备删除或者离网  需要从管理器中移除
        neu_acme_subDev_leave_t *dev = (neu_acme_subDev_leave_t *)data;
        plog_notice(plugin,"sub dev:%s leave .",dev->node);
        neu_device_leave(plugin, dev->node);
        break;
    }
    case NEU_REQ_ACME_DEV_CTRL_PUSH:{       //设备业务层下发 设备控制命令请求
        neu_acme_dev_ctrl_t *ctrl = (neu_acme_dev_ctrl_t *)data;

        plog_notice(plugin,"sub dev:%s ctrl request... .",ctrl->node);
        neu_device_ctrl_request(plugin,ctrl);
        
        break;
    }
        
    default:
        error = NEU_ERR_NODE_EXIST;
        break;
    }

    return error;
}

static int gw_driver_validate_tag(neu_plugin_t *plugin, neu_datatag_t *tag)
{

    return 0;
}

static int gw_driver_group_timer(neu_plugin_t *plugin, neu_plugin_group_t *group)
{
    return 0;
}

static int gw_driver_write(neu_plugin_t *plugin, void *req, neu_datatag_t *tag,
                        neu_value_u value)
{
    return 0;
}

static int gw_driver_write_tags(neu_plugin_t *plugin, void *req, UT_array *tags)
{
    return 0;
}


