# FCM 设备消息处理函数实现说明

## 概述

本文档描述了 `business_lora_dev_message_handle` 函数的实现，该函数用于处理从 LoRa 网关接收到的 FCM 空调设备数据，并通过 address 字段匹配来更新对应的点位值。

## 实现特点

### 1. 通过 Address 字段匹配点位
- **不依赖点位名称**：避免因用户修改点位名称导致的匹配失败
- **不依赖组名称**：避免因用户修改组名称导致的匹配失败
- **使用 address 字段**：通过点位的 address 字段进行精确匹配

### 2. 空调数据解析
支持解析以下空调设备数据格式：
```
温度(2B) + 设定温度(2B) + 模式(1B) + 星期(1B) + 周(1B) + 时(1B) + 分(1B) + 
开关(1B) + 高风(1B) + 中风(1B) + 低风(1B) + 冷阀(1B) + 热阀(1B) + 湿度(2B) + 
风速自动标志(1B) + GID(1B) + OID(1B) + IID(1B) + LOCK(1B) + 优先级字段(3B)
```

### 3. Address 映射关系
| Address | 点位功能 | 数据类型 | 说明 |
|---------|----------|----------|------|
| 1 | ONOFF | INT32 | 开关状态 |
| 2 | STEMP | FLOAT | 设定温度 |
| 3 | SMODE | INT32 | 运行模式 |
| 4 | WSPED | INT32 | 风速等级 |
| 5 | RTEMP | FLOAT | 读取温度 |
| 6 | ERROC | INT32 | 错误码 |
| 7 | HUMID | INT32 | 湿度 |

## 核心函数说明

### 1. business_lora_dev_message_handle()
- **功能**：主消息处理入口函数
- **参数**：plugin, dev, pInfo
- **返回值**：0-成功, -1-失败
- **说明**：根据设备类型分发到对应的处理函数

### 2. fcm_dev_message_handle()
- **功能**：FCM 设备消息处理
- **支持的消息类型**：
  - `LR_RSP_IOM_LP` (0x30)：IO状态上报
  - `LR_RSP_PTV_REP` (0x27)：点值上报

### 3. fcm_process_io_status_data()
- **功能**：处理 IO 状态数据
- **数据解析**：解析空调设备的完整状态信息
- **数据验证**：检查数据长度是否满足最小要求

### 4. fcm_update_tag_values()
- **功能**：更新所有相关点位的值
- **实现方式**：通过 adapter 获取 driver，遍历所有 groups 和 tags
- **匹配方式**：使用 address 字段进行精确匹配

### 5. fcm_update_tag_by_address()
- **功能**：通过 address 字段查找并更新指定点位
- **遍历逻辑**：
  1. 获取 driver 中的所有 groups
  2. 遍历每个 group 中的所有 tags
  3. 比较 tag 的 address 字段
  4. 找到匹配的 tag 后调用更新函数

### 6. fcm_update_tag_value_direct()
- **功能**：直接更新指定 group 和 tag 的值
- **更新方式**：使用 driver 的 update 回调函数
- **数据类型支持**：FLOAT, INT32, UINT32

## 数据处理流程

```
LoRa 数据接收
    ↓
business_lora_dev_message_handle()
    ↓
fcm_dev_message_handle()
    ↓
fcm_process_io_status_data()
    ↓
数据解析和验证
    ↓
fcm_update_tag_values()
    ↓
fcm_update_tag_by_address() (循环调用)
    ↓
遍历 groups 和 tags
    ↓
address 字段匹配
    ↓
fcm_update_tag_value_direct()
    ↓
更新点位值
```

## 风速处理逻辑

风速等级根据以下优先级确定：
1. **自动模式** (wind_auto_flag = true)：风速 = 4
2. **高速风** (high_wind = true)：风速 = 1
3. **中速风** (medium_wind = true)：风速 = 2
4. **低速风** (low_wind = true)：风速 = 3
5. **默认** (所有风速标志为 false)：风速 = 0 (停止)

## 错误处理

- **参数验证**：检查所有输入参数的有效性
- **数据长度检查**：确保接收到的数据长度满足最小要求
- **点位查找失败**：记录日志并返回错误码
- **更新失败**：记录详细的错误信息

## 测试验证

提供了两个测试程序：
1. **test_fcm_message_handle.c**：测试消息处理流程
2. **test_fcm_address_update.c**：测试基于 address 的点位更新

## 使用示例

```c
// 示例数据：00 f0 00 a0 04 00 14 03 01 00 00 00 00 01 00 00 01 ff ff ff
uint8_t ac_data[] = {
    0x00, 0xf0,  // 温度: 24.0°C
    0x00, 0xa0,  // 设定温度: 16.0°C
    0x04,        // 模式: 4
    0x00,        // 星期: 0
    0x14,        // 周: 20
    0x03,        // 时: 3
    0x01,        // 分: 1
    0x00,        // 开关: 关闭
    0x00,        // 高风: 关闭
    0x00,        // 中风: 关闭
    0x00,        // 低风: 关闭
    0x01,        // 冷阀: 开启
    0x00,        // 热阀: 关闭
    0x00, 0x01,  // 湿度: 0.1%
    0xff,        // 风速自动: 开启
    0xff,        // GID
    0xff,        // OID
    0xff         // IID
};

neu_acme_dev_data_pop_t pInfo = {
    .rsp = LR_RSP_IOM_LP,
    .len = sizeof(ac_data) + 1,
    .data = ac_data
};

// 调用消息处理函数
int result = business_lora_dev_message_handle(plugin, dev, &pInfo);
```

## 注意事项

1. **线程安全**：函数内部使用了 driver 的回调机制，需要确保线程安全
2. **内存管理**：及时释放分配的内存，避免内存泄漏
3. **错误日志**：详细记录错误信息，便于调试和维护
4. **数据验证**：严格验证输入数据的格式和长度
5. **扩展性**：设计支持添加新的设备类型和消息类型
