/**
 * NEURON IIoT System for Industry 4.0
 * Copyright (C) 2020-2024 EMQ Technologies Co., Ltd All rights reserved.
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 3 of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with this program; if not, write to the Free Software Foundation,
 * Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.
 **/

#ifndef NEURON_PLUGIN_ACME_LORA_DEVICE_MANAGER_H
#define NEURON_PLUGIN_ACME_LORA_DEVICE_MANAGER_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdio.h>
#include <stdlib.h>


/*
* 管理器返回设备节点基本信息
*/
typedef struct manager_resp_dev_info {
    char *name;
    int mid;
    int pid;
    int link;
    uint8_t online; //0-offline   1-online
    uint8_t pair_flag;      //是否正在配对
    uint8_t deviceType;     //设备类型
    uint8_t eui[20];
    char    version[10];
    int     subType;
} manager_dev_info_t;


static inline void manager_dev_info_free(manager_dev_info_t *e)
{
    free(e->name);
    free(e);
}

typedef struct neu_subDevice_manager neu_subDevice_manager_t;

neu_subDevice_manager_t *neu_devices_manager_create();
/*
* 设备管理器 销毁
*/
void neu_devices_manager_destroy(neu_subDevice_manager_t *mgr);

/*
* 添加子设备到管理器
*/
int neu_device_manager_add(neu_subDevice_manager_t *mgr, manager_dev_info_t *dev);

/*
* 查找对应名称的子设备
*/
manager_dev_info_t * neu_device_manager_get(neu_subDevice_manager_t *mgr, char *name);

/*
* 更新子设备名称
*/
int neu_device_manager_update_name(neu_subDevice_manager_t *mgr, const char *node_name,const char *new_node_name);

/*
* 子设备删除
*/
void neu_device_manager_del(neu_subDevice_manager_t *mgr, const char *name);

/*
* 子设备信息全量更新
*/
int neu_device_manager_update(neu_subDevice_manager_t *mgr, const char *name,manager_dev_info_t *dev);


/*
* 获取子设备个数
*/
uint16_t neu_device_manager_size(neu_subDevice_manager_t *mgr);

/*
* 查找 指定mid 设备的名称
*/
char * neu_device_manager_name_by_mid(neu_subDevice_manager_t *mgr, int mid);


/*
* 查找 指定mid 设备的设备基本信息
*/
manager_dev_info_t  * neu_device_manager_deviceInfo_by_mid(neu_subDevice_manager_t *mgr, int mid);

/*
* 查找 指定mid 设备的设备类型deviceType
*/
int neu_device_manager_deviceType_by_mid(neu_subDevice_manager_t *mgr, int mid);

/*
*  通过名称查找设备 Mid
*/
int neu_device_manager_mid_by_name(neu_subDevice_manager_t *mgr, char * name);

/*
* 查找指定类型deviceType 的未配对的设备
*/
manager_dev_info_t  *neu_device_manager_pair_dev_by_deviceType(neu_subDevice_manager_t *mgr, uint8_t deviceType);

/*
* 通过设备名称查找设备类型
*/
uint8_t neu_device_manager_get_modeType_by_name(neu_subDevice_manager_t *mgr, char *name);

/*
* 通过eui 查找模块名
*/
char  * neu_device_manager_get_name_by_eui(neu_subDevice_manager_t *mgr, char *eui);

#ifdef __cplusplus
}
#endif

#endif
