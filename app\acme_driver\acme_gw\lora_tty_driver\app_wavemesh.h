#ifndef __LORA_WAVEMESH_H__
#define __LORA_WAVEMESH_H__

/**************************gateway normal cfg para.**************/
#define CFG_FLAG0		0x1B
#define CFG_FLAG1		0x13
#define CFG_FLAG2		0x88
#define CFG_FLAG3		0x00
#define CFG_CHN_BAUD		0x07
#define CFG_UART_LEN		0x07
#define CFG_UART_START		0xEB
#define CFG_UART_END		0x0D
#define CFG_ADD_OFF_DES		0x02
#define CFG_ADD_OFF_SRC		0x02
#define CFG_LEN_OFF		0x0A
#define CFG_LEN_AMEND		0x0D
#define CFG_MAC0		0x30
#define CFG_MAC1		0x32
#define CFG_MAC2		0x41
#define CFG_MAC3		0x30
#define CFG_MAC4		0x30
#define CFG_MAC5		0x44
#define CFG_NET_ID0		0x00
#define CFG_NET_ID1		0x00
#define CFG_NET_ID2		0x00
#define CFG_AFFIX_START		0x7E
#define CFG_WILD_BC		0xFF
#define CFG_WILD_MC		0xFF
#define CFG_POWERON_DELAY	0x00
#define CFG_RESP_TIMEOUT	0x02
#define CFG_SLEEP_ASYNC		0x00
#define CFG_SLEEP_SYNC		0x00
#define CFG_SLEEP_AUTO		0x00
#define CFG_INFO_OFF		0xFF
#define CFG_LEN_ACK		0x50
#define CFG_PKT_ACK		    {0,0,0,0,0,0,0,0,0,0,0,0,0,0,0}
#define CFG_LEN_EMPTY		0x00
#define CFG_PKT_EMPTY		{0,0,0,0,0,0,0,0,0,0,0,0,0,0,0}
#define CFG_LEN_WAKE		0x00
#define CFG_PKT_WAKE		{0,0,0,0,0,0,0,0,0,0,0,0,0,0,0}
#define CFG_LEN_SENT		0x00
#define CFG_PKT_SENT		{0,0,0,0,0,0,0,0,0,0,0,0,0,0,0}
#define CFG_LEN_PROBE		0x00
#define CFG_PKT_PROBE		{0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0}
/*****************************************************************/


/**************************iom normal cfg para.**************/
#define sCFG_FLAG0		0x18
#define sCFG_FLAG1		0x13
#define sCFG_FLAG2		0x88
#define sCFG_FLAG3		0x01
#define sCFG_CHN_BAUD		0x07
#define sCFG_UART_LEN		0x07
#define sCFG_UART_START		0xEB
#define sCFG_UART_END		0x0D
#define sCFG_ADD_OFF_DES		0x02
#define sCFG_ADD_OFF_SRC		0x02
#define sCFG_LEN_OFF		0x0A
#define sCFG_LEN_AMEND		0x0D
#define sCFG_MAC0		0x00
#define sCFG_MAC1		0x00
#define sCFG_MAC2		0x62
#define sCFG_MAC3		0xF5
#define sCFG_MAC4		0xC0
#define sCFG_MAC5		0x1A
#define sCFG_NET_ID0		0x00
#define sCFG_NET_ID1		0x00
#define sCFG_NET_ID2		0x00
#define sCFG_AFFIX_START		0x7E
#define sCFG_WILD_BC		0xFF
#define sCFG_WILD_MC		0xFF
#define sCFG_POWERON_DELAY	0x02
#define sCFG_RESP_TIMEOUT	0x02
#define sCFG_SLEEP_ASYNC		0x00
#define sCFG_SLEEP_SYNC		0x00
#define sCFG_SLEEP_AUTO		0x00
#define sCFG_INFO_OFF		0xFF
#define sCFG_LEN_ACK		0x70

#define sCFG_PKT_ACK		{0,0,0,0,0,0,0,0,0,0,0,0,0,0,0}
#define sCFG_LEN_EMPTY		0x00
#define sCFG_PKT_EMPTY		{0,0,0,0,0,0,0,0,0,0,0,0,0,0,0}
#define sCFG_LEN_WAKE		0x00
#define sCFG_PKT_WAKE		{0,0,0,0,0,0,0,0,0,0,0,0,0,0,0}
#define sCFG_LEN_SENT		0x00
#define sCFG_PKT_SENT		{0,0,0,0,0,0,0,0,0,0,0,0,0,0,0}
#define sCFG_LEN_PROBE		0x00
#define sCFG_PKT_PROBE		{0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0}
/*****************************************************************/


typedef union
{
    unsigned char flag0; 
    struct 
    {
        unsigned char root_dev:1;    /* root device */
        unsigned char root_master:1; /* master root */
        unsigned char en_sleep:1;    /* enable sleep */
        unsigned char en_uart:1;     /* enable uart */

        unsigned char en_relay:1;    /* enable relay */
        unsigned char pa_index:3;    /* PA index */
    } bits; 
}CB_FLAG_0;

typedef union
{
    unsigned char flag1; 
    struct 
    {
        unsigned char wild_m_en:1;     /* enable wildcard multicast */
        unsigned char wild_b_en:1;     /* enable wildcard broadcast */
        unsigned char mac_head_out:1;  /* enable UART output packet MAC head */
        unsigned char mac_add_en:1;    /* enable MAC address */

        unsigned char sync_start:1;    /* enable UART packet start sync detection */
        unsigned char sync_end:1;      /* enable UART packet END sync detection */
        unsigned char sync_mac:1;      /* enable MAC sync start byte */
        unsigned char en_route:1;      /* enable hop table route */
    } bits; 
}CB_FLAG_1;

typedef union
{
    unsigned char flag2; 
    struct 
    {
        unsigned char mp0_newbc:1;     /* MP0 pin pluses when detecting new BC */
        unsigned char mp0_fuc_wake:1;  /* MP0 pin wakes up host */
        unsigned char mp0_fuc_power:1; /* MP0 pin controls the power of host */
        unsigned char mp0_sleep_h:1;   /* MP0 pin sleep high */

        unsigned char mp1_fuc_led:1;   /* MP1 pin outputs LED state */
        unsigned char mp1_fuc_485:1;   /* MP1 pin controls 485 chip direction */
        unsigned char mp1_fuc_pa:1;    /* MP1 pin controls extend PA direction */
        unsigned char mp1_sleep_h:1;   /* MP1 pin sleep high */
    } bits; 
}CB_FLAG_2;

typedef union
{
    unsigned char flag3; 
    struct 
    {
        unsigned char uart_interval:1;     /* enable UART out interval time */
        unsigned char info_upgrade:1;      /* enable append upgrade info */
        unsigned char info_temp:1;         /* enable append temperature info */
        unsigned char info_vcc:1;          /* enable append VCC info */

        unsigned char info_hop:1;          /* enable append hop info */
        unsigned char info_rssi_down:1;    /* enable append rssi-down info */
        unsigned char info_rssi_up:1;      /* enable append rssi-up info */
        unsigned char info_relay_add:1;    /* enable append relay info */
    } bits; 
}CB_FLAG_3;

typedef union
{
    unsigned char flack; 
    struct 
    {
        unsigned char length:4;             

        unsigned char txrx_sleep_h:1;   /* enable UART TXD and RXD pins sleep high */
        unsigned char little_end:1;     /* address is little end */
        unsigned char force_hash:1;     /* force hash address field */
        unsigned char repeat_root:1;    /* enable repeat root */
    } bits; 
}CB_flagAck;

typedef union
{
    unsigned char flEmpty; 
    struct 
    {
        unsigned char length:4;              

        unsigned char freq_band:2;    /* radio frequency band  */
        unsigned char instant_bc:1;   /* enable instant BC */
        unsigned char force_sleep:1;  /* enable force sleep after work 8.94785min */
    } bits; 
}CB_flagEmpty;


typedef union
{
    unsigned char val; 
    struct 
    {
        unsigned char baudIndex:3;  
        unsigned char signalChn:5; 
    } bits; 
}CB_chnBaud;





typedef struct {
    CB_FLAG_0 flag0; /* 0x00 - flags 0 */
    CB_FLAG_1 flag1; /* 0x01 - flags 1 */
    CB_FLAG_2 flag2; /* 0x02 - flags 2 */
    CB_FLAG_3 flag3; /* 0x03 - flags 3 */
    CB_chnBaud chnBaud; /* 0x04 - (bit 7-3) main channel; (bit 2-0) baud index */
    uint8_t uartLen; /* 0x05 - (bit 7-6) parity; (bit 5-4) stop; (bit 3-0) address length */
    uint8_t uartStart; /* 0x06 - UART packet start sync byte */
    uint8_t uartEnd; /* 0x07 - UART packet end sync byte */
    uint8_t addDesOff; /* 0x08 - UART packet destination address offset */
    uint8_t addSrcOff; /* 0x09 - UART packet original address offset */
    uint8_t lenOff; /* 0x0A - UART packet length offset */
    uint8_t lenAmend; /* 0x0B - UART packet length amendment */
    uint8_t macAdd[6]; /* 0x0C - MAC address */
    uint8_t netID[3]; /* 0x12 - network ID */
    uint8_t affixSync; /* 0x15 - UART packet affix sync byte */
    uint8_t wildcardB; /* 0x16 - UART packet broadcasting address wildcard */
    uint8_t wildcardM; /* 0x17 - UART packet multicasting address wildcard */
    uint8_t wakeDelay; /* 0x18 - host init time after wakeup up to 256ms, unit:1ms */
    uint8_t respTimeout; /* 0x19 - UART response timeout up to 256ms, unit:1ms */
    uint8_t sleepAsync; /* 0x1A - async-sleep time interval 4.178s, unit:16.384ms */
    uint8_t sleepSync; /* 0x1B - sync-sleep time interval 17.8957min, unit:4194.304ms */
    uint8_t sleepAuto; /* 0x1C - auto-sleep time interval 4.474min, unit:1048.576ms */
    uint8_t infoOff; /* 0x1D - network info offset */
    CB_flagAck flAck; /* 0x1E - (high 4-bit) flags; (low 4-bit) length of ack packet */
    uint8_t pktAck[15]; /* 0x1F - ack packet */
    CB_flagEmpty flEmpty; /* 0x2E - (high 4-bit) flags; (low 4-bit) length of empty packet */
    uint8_t pktEmpty[15]; /* 0x2F - empty packet */
    uint8_t flWake; /* 0x3E - (high 4-bit) flags; (low 4-bit) length of wake packet */
    uint8_t pktWake[15]; /* 0x3F - wake packet */
    uint8_t flSent; /* 0x4E - (high 4-bit) flags; (low 4-bit) length of sent packet */
    uint8_t pktSent[15]; /* 0x4F - sent packet */
    uint8_t flProbe; /* 0x5E - (high 3-bit) flags; (low 5-bit) length of probe packet */
    uint8_t pktProbe[23]; /* 0x5F - host probe packet */
} cfgBlock;


typedef struct 
{   
    int mode;                           //主从机模式  0-主机  1-从机
    uint8_t eui[8];                      // eui 地址
    int netid;                          //网络ID
    uint8_t chn;                        //信道
    uint8_t freq;                       //频段
    uint8_t pa_index;                   //功率
    uint8_t baudIndex;                  //波特率
    uint8_t flWake;                     //距离等级
}lora_mesh_cfg;

#endif
