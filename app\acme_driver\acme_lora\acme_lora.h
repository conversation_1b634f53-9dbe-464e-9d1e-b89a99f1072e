/**
 * NEURON IIoT System for Industry 4.0
 * Copyright (C) 2020-2024 EMQ Technologies Co., Ltd All rights reserved.
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 3 of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with this program; if not, write to the Free Software Foundation,
 * Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.
 **/

#ifndef NEURON_PLUGIN_ACME_LORA_H
#define NEURON_PLUGIN_ACME_LORA_H

#ifdef __cplusplus
extern "C" {
#endif

#include "neuron.h"

//网关插件功能： 网关设备参数维护、硬件点位、
//              硬件串口通信、Lora 子设备协议（所有的子模块Node加载时，需要注册到网关子模块管理内）




struct neu_plugin {
    neu_plugin_common_t common;

    int modeType;

    //TODO: 此处添加Lora 驱动相关的定义 如串口fd 等
    //...

};



#ifdef __cplusplus
}
#endif

#endif
