/**
 * NEURON IIoT System for Industry 4.0
 * Copyright (C) 2020-2024 EMQ Technologies Co., Ltd All rights reserved.
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 3 of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with this program; if not, write to the Free Software Foundation,
 * Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.
 **/

#ifndef NEURON_PLUGIN_ACME_LORA_DRIVER_H
#define NEURON_PLUGIN_ACME_LORA_DRIVER_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdio.h>
#include <stdlib.h>
#include "neuron.h"
#include "acme_gw.h"
#include "app_wavemesh.h"
#include "lora_device_manager.h"

/************************************** 硬件配置 *********************************/
//TTY
#define SPT_LORA_TTY_DEV        "/dev/ttyS1"
#define SPT_LORA_TTY_BAUD       NEU_CONN_TTY_BAUD_115200
#define SPT_LORA_TTY_DATA_BIT   NEU_CONN_TTY_DATA_8
#define SPT_LORA_TTY_PARITY     NEU_CONN_TTY_PARITY_NONE
#define SPT_LORA_TTY_STOP_BIT   NEU_CONN_TTY_STOP_1
#define SPT_LORA_TTY_TIMEOUT    3000

//RST
#define SPT_LORA_RST_PIN_NUM     ((1*32)+3)     //GPIO1_A3  --- 1*32 + (1-1)*8 + 3 




#define GWMP_HEADER_STX		0xEB
#define GWMP_DATA_LEN		128
#define USER_ID_LEN		3
#define EUI_ADR_LEN		20

typedef enum lora_rcvsta_e{
    PCK_HED = 0,
    PCK_DATA, //通信数据
    PCK_CCFG, //配置信息
    PCK_EXTH, //扩展头信息
    PCK_END
}lora_rcvsta_enum;




int acme_lora_init(neu_plugin_t *plugin);
int acme_lora_serial_init(neu_plugin_t *plugin,char *serial_dev,neu_conn_tty_baud_e baud, neu_conn_tty_data_e data, neu_conn_tty_parity_e parity, neu_conn_tty_stop_e  stop,uint16_t timeout);      //Lora 串口初始化

int acme_lora_msg_send(neu_plugin_t *plugin,uint8_t modeType,uint8_t rsp, char *eui, uint8_t *buff, uint16_t buff_len);       //lora 发送msg 数据


int acme_lora_msg_recv(enum neu_event_io_type type, int fd, void *usr_data);       //lora 接收数据
int wave_mesh_ack_join_request(neu_plugin_t *plugin,char * eui,int deviceType);

int lora_parameter_setting(neu_plugin_t *plugin);
int lora_tty_start(neu_plugin_t *plugin);
int lora_tty_stop(neu_plugin_t *plugin);

int acme_lora_driver_stop_search(neu_plugin_t *plugin);     //停止搜网
#ifdef __cplusplus
}
#endif

#endif
