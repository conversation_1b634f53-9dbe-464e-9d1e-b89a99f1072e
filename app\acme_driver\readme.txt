
次文件夹为南向设备驱动插件模块。涉及后续Lora 、Modbus等驱动模块，每个驱动模块在此文件夹下新建文件夹进行代码维护。
插件编译输出均为动态库形式接入Neuron。

/********************** 业务插件应用 开发编译 ******************************/
acme_server 编译命令:
新建build 目录下:
export PATH=/opt/gcc-arm-10.3-2021.07-x86_64-arm-none-linux-gnueabihf/bin:${PATH}

cmake .. -DCMAKE_TOOLCHAIN_FILE=../../cmake/arm-none-linux-gnueabihf.cmake

make 

插件动态库输出在 build/plugins 目录下，同时插件对应的配置参数文件在同目录下的schema下，拷贝到SPT设备plugins 目录即可。
