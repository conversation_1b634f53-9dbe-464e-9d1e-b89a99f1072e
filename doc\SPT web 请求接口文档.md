# SPT HTTP API 接口文档



## 记录

| 日期       | 描述                                             | 备注 |
| ---------- | ------------------------------------------------ | ---- |
| 2025.07.04 | 初稿（账号、Node、Group、Tag相关接口定义）       |      |
| 2025.07.07 | 第一次评审，修改部分参数字段                     |      |
| 2025.07.08 | 新增设备驱动类型查询接口定义（插件信息）         |      |
| 2025.07.17 | 新增查询所有点位信息接口定义                     |      |
| 2025.07.23 | 添加node 接口*POST* **/api/v2/node**下发参数调整 |      |
| 2025.07.24 | 修改GET 相关查询接口，改为URL传参，适配HTTP规范  |      |



## 说明

​	本文档主要描述 SPT 网关web 应用前端与后端数据交互的接口定义， 采用 http api 接口进行数据交互，涉及登录认证、账户修改、设备节点查询、设备参数设置与查询、添加子模块、搜索子设备（点位）、点位数据采集等业务。



## HTTP API接口定义

### 账户

#### 登录

- **路径**

> *POST* **/api/v2/login**

- ##### 请求头

Content-Type: application/json

- **请求体**

  ```json
  {
      "name": "admin",
      "pass": "12345678"
  }
  ```

- **响应**

  ```json
  {
      "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE2MzcyODcxNjMsImlhdCI6MTYzNzIwMDc2MywiaXNzIjoiRU1RIFRlY2hub2xvZ2llcyBDby4sIEx0ZCBBbGwgcmlnaHRzIHJlc2VydmVkLiIsInBhc3MiOiIwMDAwIiwidXNlciI6ImFkbWluIn0.2EZzPC9djErrCeYNrK2av0smh-eKxDYeyu7cW4MyknI"
  }
  ```

- **响应状态**

  | 状态码 | 结果                                                         |
  | ------ | ------------------------------------------------------------ |
  | 200    | OK                                                           |
  | 401    | 1004  - 缺少令牌<br />1005 - 解码令牌错误                    |
  | 403    | 1006 - 令牌过期<br />1007 - 验证令牌错误<br />1008 - 无效令牌 |

  

#### 修改密码

- 路径

  > *POST* **/api/v2/password**

- 请求头

  **Content-Type** application/json

  **Authorization** Bearer <token>

- 请求体

  ```json
  {
      "name": "admin",
      "old_pass": "12345678",
      "new_pass": "acme888"
  }
  ```

  

- 响应

  ```json
  {
      "error": 0
  }
  ```

  

- 响应状态

| 状态码 | 结果                                                         |
| ------ | ------------------------------------------------------------ |
| 200    | OK                                                           |
| 401    | 1004  - 缺少令牌 <br />1005 - 解码令牌错误<br />1012 - 密码长度太短或太长<br />1013 - 密码重复 |
| 403    | 1006 - 令牌过期 <br />1007 - 验证令牌错误 <br />1008 - 无效令牌 |



#### Ping

- 路径

  > *POST* **/api/v2/ping**

- 请求头

  **Content-Type** application/json

- 请求体

  ```json
  {
      //Body 可为空
  }
  ```

  

- 响应

  ```json
  {
     //响应Body 为空，已响应码为结果
  }
  ```

  

- 响应状态

| 状态码 | 结果 |
| ------ | ---- |
| 200    | OK   |



### 获取设备驱动类型（插件信息）

#### 获取插件

- 路径

  > GET **/api/v2/plugin**

- 请求头

  **Authorization** Bearer <token>

- 请求体

  ```json
  {
      //Body 可为空
  }
  ```

  

- 响应

  ```json
  {
      "plugins": [
          {
              //plugin kind
              "kind": 1,
              //node type
              "node_type": 1,
              //plugin name
              "name": "Modbus TCP",
              //plugin library name
              "library": "libplugin-modbus-tcp.so",
              "description": "description",
              "description_zh": "描述",
              "schema": "modbus-tcp"
          },
          {
              "kind": 1,
              "node_type": 2,
              "name": "MQTT",
              "library": "libplugin-mqtt.so",
              "description": "Neuron northbound MQTT plugin bases on NanoSDK.",
              "description_zh": "基于 NanoSDK 的 Neuron 北向应用 MQTT 插件",
              "schema": "mqtt"
          }
      ]
  }
  ```

  

- 响应状态

| 状态码 | 结果 |
| ------ | ---- |
| 200    | OK   |



### 设备Node操作

#### 设备添加

- 路径

  > *POST* **/api/v2/node**

- 请求头

  **Content-Type** application/json

  **Authorization** Bearer <token>

- 请求体

  | 参数名      | 参数说明  | 备注          |
  | ----------- | --------- | ------------- |
  | id          | id        | key值 int     |
  | name        | 名称      |               |
  | modetype    | 模型类型  | 0,1,2,3（int) |
  | eui         | EUI地址   |               |
  | appeui      | APPEUI    |               |
  | appkey      | APPKEY    |               |
  | nettype     | 入网模式  | int           |
  | classtype   | class类型 | int           |
  | createTime  | 创建时间  |               |
  | uid         |           | 该字段待确认  |
  | parentUid   |           | 该字段待确认  |
  | pid         |           | 该字段待确认  |
  | mid         |           | 该字段待确认  |
  | linkselect  |           | 该字段待确认  |
  | pointList   |           | 该字段待确认  |
  | pointConfig |           | 该字段待确认  |

  ```json
  {
      //node name设备名
      "name": "modbus-tcp-node",
      //plugin name 所属插件名或设备类型
      "plugin": "Modbus TCP",
      
      "id": 3,
      "modeType": 3,
      "eui": 'ffffff1000001114',
      "link": 1,
      "pid": 1,
      "deviceCode":0,
      "subType":1,
      
      //setting (optional)
      "params": {
          "lora_cfg":{
              int	mode,	//主从机模式 0-主机  1-从机
              int freq,	//频段
              int chn,	//信道
              int pa,		//功率
              int netId,	//网络ID
              int flWake, //距离等级
          },
          "com_cfg":{
          
          }      	
      }
  }
  ```

  

- 响应

  ```json
  {
      "msg": "设备成功",
      “name”:"fxxxx",
      "mid":1,
      "error": 0
  }
  ```

  

- 响应状态

| 状态码 | 结果                                               |
| ------ | -------------------------------------------------- |
| 200    | OK                                                 |
| 400    | 2001 - Node 设备类型无效<br />2004 - Node 配置无效 |
| 404    | 2301  - 未找到插件库                               |
| 409    | 2002 - node设备 已存在                             |



#### 设备删除 

- 路径

  > ***DELETE*** /api/v2/node

- 请求头

  **Content-Type** application/json

  **Authorization** Bearer <token>

- 请求体

  | 参数名 | 参数说明           | 备注      |
  | ------ | ------------------ | --------- |
  | id     | id 设备唯一标识    | key值 int |
  | name   | 需要删除的设备名称 | string    |

  ```json
  {
      //node name
      "name": "modbus-tcp-node"   
  }
  ```
  
  
  
- 响应

  ```json
  {
      "msg": "设备删除成功",
       "error": 0
  }
  ```

  

- 响应状态

| 状态码 | 结果               |
| ------ | ------------------ |
| 200    | OK                 |
| 404    | 2003  - 设备不存在 |



#### 获取设备列表

- 路径

  > *GET* /api/v2/node

- 请求参数

  type   : 设备类型 (1 - 南向设备   2-北向应用)     -- 必选

  node :  需要查询的设备名 ,  --- 可选 （查询单个设备带该参数，查询所有的设备列表，不需要带该参数）

- 请求头

  **Content-Type** application/json

  **Authorization** Bearer <token>

- 请求体

  无

  

- 响应

  | 参数名     | 参数说明     | 备注                                                         |
  | ---------- | ------------ | ------------------------------------------------------------ |
  | mid        | 模块id       | key值，唯一,int , id=1为bec                                  |
  | pid        | 父模块id     | 树形拓扑中父模块id;                                          |
  | linkselect | 链路选择来源 | 0：主节点；1：lora下挂，2：rs485 com1下挂；3：rs485 com2下挂， |
  | node       | 名称         |                                                              |
  | modetype   | 模块类型     | 0,1,2,3（对应值） int                                        |
  | eui        | EUI地址      | 设备唯一地址                                                 |
  | version    | 版本号       |                                                              |
  | classtype  | class类型    | 0,1,2（对应值） int                                          |
  | status     | 在线状态     | 0 代表离线  、1代表在线 、 2升级状态                         |
  | frequency  | 频段         |                                                              |
  | saddr      | 从机地址     |                                                              |
  | brate      | 波特率       |                                                              |
  | dbits      | 数据位       |                                                              |
  | parity     | 奇偶校验     |                                                              |
  | stopbit    | 停止位       |                                                              |

  ```
  modetype对应值
  value="0"   BEC-22L
  value="1"  ACME-IOM-08DL
  value="2"  ACME-IOM-06DL
  value="3"  ACME-IOM-06AL
  value="4"  ACME-IOM-GWL01
  value="5"  FCU-IOM-FCM
  
  模块索引：
      BEC_22L = 0,
      IOM_08DL, //1
      IOM_06DL, //2
      IOM_06AL, //3
      IOM_GWL01,//4
      IOM_FCM,  //5
      IOM_07IO, //6
      IOM_3XIO, //7
      ACM,      //8
      ICM,      //9
      SCM,      //10
      DTM,      //11
      IGW_OCC,  //12 回传时igw使用12给配置工具识别，配置工具下发时涉及igw模块类型还是按0索引；
  
  
  nettype 对应值
  0 AIC私有协议
  1 OTTA  
  2 ABP
  3 WMESH
  
  classtype
  0 A类
  1 B类  
  2 C类
  nettype 为WMESH时做信道使用
  ```

  响应数据:

  ```json
  {
      "nodes": [
          {
            "mid": 1,
            "pid": 0,
            "linkselect": 0,
            "node": "IGW-150",
            "plugin": "ACME_LORA",
            "modetype": 12,      
            "eui": "17689EB18F1F6CD0",
            "version": "17689EB18F1F6CD017689EB18F1F6CD0",
            "subType": 2,
            "deviceCode": 3,
            "createTime": 1753426324,
            "updateTime": 1753426693,
            "userData": ""         
      	},
          {
            "mid": 2,
            "pid": 1,
            "linkselect": 1,
            "node": "XTX_TTL",
            "plugin": "ACME_LORA",
            "modetype": 4,         
            "eui": "3768343564983404",
            "version": "37683435649834043768343564983404",
            "subType": 0,
            "deviceCode": 0,
            "createTime": 1753426324,
            "updateTime": 1753426389,
            "userData": ""
          }
        ],
        "error":0
  }
  ```

  

- 响应状态

| 状态码 | 结果 |
| ------ | ---- |
| 200    | OK   |



#### 获取指定设备

- 路径

  > *GET* /api/v2/node

**查询指定设备接口与前面查询设备列表共用同一个接口，http 请求参数添加要查询的设备名node 即可，返回的body 内容格式也一致**

- 响应状态

  | 状态码 | 结果               |
  | ------ | ------------------ |
  | 200    | OK                 |
  | 404    | 2003  - 设备不存在 |



#### 更新设备基本信息

- 路径

  > *PUT* **/api/v2/node**

- 请求头

  **Content-Type** application/json

  **Authorization** Bearer <token>

- 请求体

  | 参数名   | 参数说明     | 备注        |
  | -------- | ------------ | ----------- |
  | node     | 原子模块名称 |             |
  | new_name | 新名称       | 0,1,2,3 int |
  
  ```json
  {
      "node": "ECM26",
      "new_name": 1
  }
  ```
  
  
  
- 响应

  ```json
  {
      "msg": "设备更新成功",
      "error": 0
  }
  ```

  

- 响应状态

| 状态码 | 结果               |
| ------ | ------------------ |
| 200    | OK                 |
| 404    | 2003  - 设备不存在 |



#### 设备参数修改

- 路径

  > *POST* /api/v2/node/setting

- 请求头

  **Content-Type** application/json

  **Authorization** Bearer <token>

- 请求体

  | 参数名 | 参数说明           | 备注                                             |
  | ------ | ------------------ | ------------------------------------------------ |
  | mid    | id 设备唯一标识    | key值 int                                        |
  | name   | 需要配置的设备名称 | string                                           |
  | params | 参数列表           | 前端根据不同的参数子页面，可一次性下发多个子参数 |

  ```json
  {
      //node name
      "name": "modbus-tcp-node", 
      "params":[
          "commSet":{....},		//端口配置
  		"netSet":{...},			//网络设置
          ...
      ]
  }
  ```
  
  
  
- 响应

  ```json
  {
      "msg": "参数配置成功",
       "error": 0
  }
  ```

  

- 响应状态

| 状态码 | 结果               |
| ------ | ------------------ |
| 200    | OK                 |
| 404    | 2003  - 设备不存在 |



#### 设备参数查询

- 路径

  > *GET* /api/v2/node/setting

- 请求头

  **Content-Type** application/json

  **Authorization** Bearer <token>

- 请求体

  | 参数名 | 参数说明           | 备注                                             |
  | ------ | ------------------ | ------------------------------------------------ |
  | mid    | id 设备唯一标识    | key值 int                                        |
  | name   | 需要配置的设备名称 | string                                           |
  | params | 参数列表           | 前端根据不同的参数子页面，可一次性下发多个子参数 |

  ```json
  {
      //node name
      "name": "modbus-tcp-node",
       mid: 1
  }
  ```

  

- 响应

  ```json
  {
          //node name
      "name": "modbus-tcp-node", 
       "params":[
          "commSet":{....},		//端口配置
  		"netSet":{...},			//网络设置
          ...
      ],                  
       "error": 0
  }
  ```
  
  
  
- 响应状态

| 状态码 | 结果               |
| ------ | ------------------ |
| 200    | OK                 |
| 404    | 2003  - 设备不存在 |



#### 设备控制

- 路径

  > *POST* /api/v2/node/ctl

- 请求头

  **Content-Type** application/json

  **Authorization** Bearer <token>

- 请求体

  | 参数名 | 参数说明                                                     | 备注                  |
  | ------ | ------------------------------------------------------------ | --------------------- |
  | mid    | id 设备唯一标识                                              | key值 int             |
  | name   | 需要配置的设备名称                                           | string                |
  | cmd    | 命令定义<br />0:  启动设备适配器运行<br />1：停止设备适配器运行<br />2：搜索子设备<br />3：设备通道直控<br />... | 设备控制命令  int类型 |
  | params | 控制参数<br />cmd=0/1  该字段内容可为空<br />cmd=2 该字段内筒可为空<br />cmd=3  : type-通道类型(AI,AO,DI,DO,BI,BO,UI,CO) \|\| ai1-ai8 :8个通道  ,值 |                       |

  

  ```json
  {
      //node name
      "node": "fcm03-1",
      "cmd": 3,
      "params":{
          "type": "CO",
          "atChn":1,
    		"ai1": 1
      }
  }
  ```

  

- 响应

  ```json
  //cmd=2 搜索子设备应答示例
  {
          //node name
      "node": "modbus-tcp-node",
      "cmd":2,
       "msg":"设备搜索成功",
       "error": 0,
       "data":{
          "sub_device":[
          {
  		"name": "VRV_O1I1",
  		"desc": "",
  		"info": "lora 38 9 0",
  		"addr": 257,
  		"dtype": 1,
          "atMid": 2
         },
         {
  		"name": "VRV_O1I2",
  		"desc": "",
  		"info": "lora 38 9 0",
  		"addr": 258,
  		"dtype": 1,
          "atMid": 2
         }
       ]
      }
  }
  ```

  ```
  {
  	//cmd = 3 设备通道直控 应答示例
  	"node":"fcm03-1",
  	"cmd":3,
  	"msg":"设备控制成功",
  	"error":0	
  }
  ```

  

- 响应状态

| 状态码 | 结果                 |
| ------ | -------------------- |
| 200    | OK                   |
| 404    | 2003  - 子设备不存在 |



### 组Group操作

#### 添加组

- 路径

  > *POST* /api/v2/group

- 请求头

  **Content-Type** application/json

  **Authorization** Bearer <token>

- 请求体

  | 参数名   | 参数说明                        | 备注      |
  | -------- | ------------------------------- | --------- |
  | mid      | id 设备唯一标识                 | key值 int |
  | name     | 需要配置的设备名称              | string    |
  | group    | 组名                            |           |
  | interval | 采集/上报组内点位的时间周期(ms) | int       |

  ```json
  {
      //node name
      "node": "modbus-tcp-node",    
      "group":"modbus_tcp_group1",
      //read/upload interval(ms)
      "interval": 10000
  }
  ```
  
  
  
- 响应

  ```json
  {
   
       "msg":"组modbus_tcp_group1添加成功",
       "error": 0
  }
  ```

  

- 响应状态

| 状态码 | 结果                                     |
| ------ | ---------------------------------------- |
| 200    | OK                                       |
| 404    | 2003  - Node设备不存在                   |
| 409    | 2103 - 组不允许添加<br />2104 - 组已添加 |



#### 查询Node设备的组信息

- 路径

  > *GET* /api/v2/group

- 请求参数

  node :   设备名  --必选

  mid:    设备唯一id  可选

- 请求头

  **Content-Type** application/json

  **Authorization** Bearer <token>

- 请求体

  无

- 响应

  ```json
  {
      //node name
      "name": "modbus-tcp-node",    
      "groups": [
          {
              //group name
              "name": "config_modbus_tcp_sample_2",
              //read/upload interval(ms)
              "interval": 2000,
              //tag count 组内点位总数
              "tag_count": 0
          },
          {
              "name": "gconfig1",
              "interval": 10000,
              "tag_count": 0
          }
      ],
      "msg":"设备组查询成功",
       "error": 0
  }
  ```
  
  
  
- 响应状态

| 状态码 | 结果                   |
| ------ | ---------------------- |
| 200    | OK                     |
| 404    | 2003  - Node设备不存在 |



#### 删除组

- 路径

  > *DELETE* /api/v2/group

- 请求头

  **Content-Type** application/json

  **Authorization** Bearer <token>

- 请求体

  | 参数名 | 参数说明           | 备注      |
  | ------ | ------------------ | --------- |
  | mid    | id 设备唯一标识    | key值 int |
  | name   | 需要配置的设备名称 | string    |
  | group  | 需要删除的组名     | string    |

  ```json
  {
      //node name
      "node": "modbus-tcp-node",
      "group":"modbus_tcp_group1"
  }
  ```
  
- 响应

  ```json
  {
      "msg":"设备组删除成功",
       "error": 0
  }
  ```

- 响应状态

| 状态码 | 结果                                        |
| ------ | ------------------------------------------- |
| 200    | OK                                          |
| 404    | 2003  - Node设备不存在<br />2106 - 组不存在 |



#### 更新组

- 路径

  > *PUT* /api/v2/group

- 请求头

  **Content-Type** application/json

  **Authorization** Bearer <token>

- 请求体

  | 参数名    | 参数说明                                   | 备注      |
  | --------- | ------------------------------------------ | --------- |
  | mid       | id 设备唯一标识                            | key值 int |
  | name      | 需要配置的设备名称                         | string    |
  | group     | 组名                                       | string    |
  | new_group | 新的组名称（如果名称没有修改，次字段不加） | string    |
  | interval  | 采集/上报组内点位的时间周期(ms)            | int       |

  ```json
  {
      //node name
      "name": "xxx_node",  
      "group":"xxx",
      //更新组名
      "new_group":"new_xxx",
      "interval":500
  }
  ```
  
- 响应

  ```json
  {
      "msg":"设备组更新成功",
      "error": 0
  }
  ```

- 响应状态

| 状态码 | 结果                                        |
| ------ | ------------------------------------------- |
| 200    | OK                                          |
| 404    | 2003  - Node设备不存在<br />2106 - 组不存在 |
| 409    | 2104 - 组参数相同                           |



### 点位操作

#### 添加点位Tags

- 路径

  > *POST* /api/v2/tags

- 请求头

  **Content-Type** application/json

  **Authorization** Bearer <token>

- 请求体

  | 相关字段 | 说明                | 备注 |
  | -------- | ------------------- | ---- |
  | "oid",   | 点位索引-内部排序用 |      |
  | "id",    | 点位id-数据库唯一值 |      |
  | "pName", | 点位名称            |      |
  | "pDesc", | 点位描述            |      |
  | "pRang", | 取值范围            |      |
  | "pUnit", | 单位                |      |
  | "pStep", | 步进                |      |
  | "pArea", | 位置信息            |      |
  | "info1", | 描述1               |      |
  | "info2", | 当前值              |      |
  | "ctlNo", | 功能码              |      |
  | "rwFlg", | 读写标志            |      |
  | "pType", | 数据类型            |      |
  | "mName", | 模块名              |      |
  | "mType", | 模块类型            |      |
  | "atMid", | 数据来源            |      |
  | "atChn", | 通道                |      |
  | "pAddr", | 地址                |      |
  | "pLink", | 消息类型            |      |
  | "times", | 时间-未用           |      |

  ```json
  {
     //node name
      "node": "modbus-node",
      "mid": 1,
     //group name
      "group": "config_modbus_tcp_sample_2",
      "tags": [
          //硬件点例子
          {
            "pLink": 0,           //索引0 为硬件点
            "pName": "hw_name1",  //点名
            "pDesc": "desc",      //点位描述
            "atChn": 0,           //硬件点通道
            "pType": 3,           //数据类型
            "pUnit": "%",         //单位
            "rwFlg": 1,           //读写标志
            "atMid": 1,           //点所属模块id
            "pInfo": {}          //信息描述：转发配置参数
    		},
          //软件点例子
          {
            "pLink": 1,           //索引1 为软件点
            "pName": "sw_name1",  //点名
            "pDesc": "desc",      //点位描述
            "atChn": 0,           //软件点通道
            "pType": 3,           //数据类型
            "pUnit": ,            //单位
            "rwFlg": 0,           //读写标志
            "atMid": 1,           //点所属模块id
            "pInfo": {}          //信息描述：转发配置参数
          },
          //网络点例子
          {
            "pLink": 2,
            "pName": "bs_name3",
            "pDesc": "desc3",
            "atChn": 0,
            "pType": 3,
            "pUnit": "105",
            "rwFlg": 1,
            "atMid": 1,
            "pInfo": {
              "tCom": 15,
              "tPro": 1024,
              "sCom": 0,
              "sPro": 0,
              "sPar": {
                "sid": 2,
                "fuc": 3,
                "reg": 123,
                "len": 2
              }
            }
          }
      ]
  }
  ```

  信息描述 - 转发配置参数描述 说明

  ```
  {
    "tCom": 15,     //转发链路端口 （serial：0-14, lan：15,  lora：30）
    "tPro": 1024，  //转发协议索引
    "tXXX":...
  }
  ```

  信息描述 - 转发+采集配置参数描述 说明

  ```
  {
    "tCom": 15,     //转发链路端口 （serial：0-14, lan：15,  lora：30）
    "tPro": 1024,   //转发协议索引 
    "tPar":{
  	  "M": 1,
  	  "T": 3,
    },
    "sCom": 0,      //采集链路端口 （serial：0-14, lan：15,  lora：30）
    "sPro": 0,      //采集协议索引  
    "sPar": {
      "sid": 2,
  	"fuc": 3,
  	"reg": 123,
  	"len": 2,
  	"fmt": "dcba",
  	"mac": "502 ***************",        //modbus tcp 指定
    }，
    "app": {"id":1,"dat":"0.8","bia":-0.3,“vdn":0,“vup":100,"dif":3}  
  		//id 为适配类型，dat K值或文件名，bia 为校准偏置，vdn 映射下限，vup 映射上限；     
          //1--(线性)倍率设定，*0.8 带vdn,vup；
          //2- 通用电阻适配 NTC10K-B3435
          //3- 通用电阻适配 NTC10K-B3950
          //4- 通用电阻适配 PT1000
          //5- 通用电阻适配 PT100
          //6- 电阻表，dat自动带上自定义文件名“pt10kBXX.csv”；
          //7- 取反，前提是数据类型为bit，bool；
          //8- 上下限比例映射 带vdn,vup；
    
  }
  ```

  采集配置参数描述("sPar") 说明

  | 协议                                   | 参数    | 说明                                                         |
  | -------------------------------------- | ------- | ------------------------------------------------------------ |
  | ModBus-RTU(主) ModBus-TCP(主)          | "sid"   | 从机id - number                                              |
  | -                                      | "fuc"   | 功能码 - number                                              |
  | -                                      | "reg"   | 地址   - number                                              |
  | -                                      | "len"   | 长度   - number                                              |
  | -                                      | "fmt"   | 字节序格式 - string（e.g., "abcd" 、 "dcba" 、"abcdefgh"  ...） |
  | -                                      | "mac"   | mac地址 - string (ModBusTcp e.g., "502 ***************")     |
  | -                                      | "app"   | 应用数据  - json                                             |
  | BACNet-IP(Client) BACNet-MS/TP(Client) | "dev"   | 设备id - number                                              |
  | -                                      | "obj"   | 对象类型 - number   AI = 0, AO = 1, AV = 2, BI = 3, BO = 4, BV = 5, IV = 45 |
  | -                                      | "oid"   | 对象索引 - number                                            |
  | -                                      | "cov"   | 订阅cov  - number 0:不订阅，1：订阅                          |
  | -                                      | "mac"   | mac地址  - string (BIP e.g.,"47808 ***************",MSTP e.g.,"123") |
  | Siemens-S7(Client)                     | "rack"  | 机架号 - number                                              |
  | -                                      | "slot"  | 槽号 - number                                                |
  | -                                      | "svip"  | 服务器ip地址  - string (e.g.,"***************")              |
  | -                                      | "area"  | Area ID(区id) - number ： 'PE': 0x81,  #input 输入区 'PA': 0x82,  #output 输出区 'MK': 0x83,  #bit memory 中间存储区（M区） 'DB': 0x84,  #DB区 'CT': 0x1C,  #counters（暂不支持） 'TM': 0x1D,  #Timers（暂不支持） |
  | -                                      | "dbNum" | 数据块地址- number                                           |
  | -                                      | "start" | 起始地址 - number                                            |
  | -                                      | "len"   | 地址长度 - number                                            |
  | DLT645                                 | "mac"   | mac地址形式“数据标识 电表地址”  - string (BIP e.g.,"2010100 240323135007") |
  | 多联机无线\空调一体化\ACME 内部协议    | "par1"  | 参数1 - number                                               |
  | LONWORKS                               | "nid"   | neure id - number                                            |
  | -                                      | "num"   | 索引号 - number                                              |
  | -                                      | "snv"   | 标准网络变量 - number                                        |
  | -                                      | "group" | 采集分组号 - number                                          |
  |                                        |         |                                                              |

  采集协议索引：

  | 协议                 | 索引 |
  | -------------------- | ---- |
  | ModBus-RTU(主)       | 0    |
  | ModBus-TCP(主)       | 6    |
  | BACNet-IP(Client)    | 8    |
  | BACNet-MS/TP(Client) | 35   |
  | Siemens-S7(Client)   | 41   |
  | 多联机无线采集       | 38   |
  | 空调一体化无线采集   | 39   |
  | ACME 内部协议        | 40   |

  转发配置参数描述"tPar" 说明

  | 协议                                   | 参数  | 说明                                                         |
  | -------------------------------------- | ----- | ------------------------------------------------------------ |
  | ModBus-RTU(从) ModBus-TCP(从)          | "fuc" | 功能码 - number                                              |
  | -                                      | "reg" | 映射地址   - number                                          |
  | BACNet-IP(Server) BACNet-MS/TP(Server) |       |                                                              |
  | -                                      | "obj" | 映射对象类型 - number   AI = 0, AO = 1, AV = 2, BI = 3, BO = 4, BV = 5, IV = 45 |
  | -                                      | "oid" | 映射对象索引 - number                                        |
  | MQTT-ACME                              | "T"   | 回传周期 单位s                                               |
  |                                        | "M"   | 回传策略 1：当且仅当值变化回传，2：周期（见“T”）回传，3：周期内变化，在周期时间点回传；... |

  转发协议索引：（默认mqtt）

  | 协议                 | 索引 |
  | -------------------- | ---- |
  | ModBus-RTU(从)       | 36   |
  | ModBus-TCP(从)       | 37   |
  | BACNet-IP(Server)    | 34   |
  | BACNet-MS/TP(Server) | 7    |
  | MQTT-ACME            | 1024 |

  

- 响应

  ```json
  {
      "msg":"点位添加成功",
       "error": 0
  }
  ```

- 响应状态

| 状态码 | 结果                                                         |
| ------ | ------------------------------------------------------------ |
| 200    | OK                                                           |
| 404    | 2003  - Node设备不存在<br />2106 - 组不存在                  |
| 206    | 2202 - 标签名称冲突<br/>2203 - 标签属性不支持<br/>2204 - 标签类型不支持<br/>2205 - 标签地址格式无效 |



#### 查询点位Tags

- 路径

  > *GET* /api/v2/tags

- 请求参数

  node :  设备名 -- 必选

  group:  组名  -- 必选

  mid:  设备唯一标识  --可选

  tagName:  点位名称 --- 可选  （**存在该参数则查询指定点的信息，不存在该参数，则查询当前设备组内的所有点信息**）

- 请求头

  **Content-Type** application/json

  **Authorization** Bearer <token>

- 请求体

​	无

- 响应

  ```json
  {
     //node name
      "node": "modbus-node",
      mid: 1,
     //group name
      "group": "config_modbus_tcp_sample_2",
      "tags": [
          //硬件点例子
          {
            "pLink": 0,           //索引0 为硬件点
            "pName": "hw_name1",  //点名
            "pDesc": "desc",      //点位描述
            "atChn": 0,           //硬件点通道
            "pType": 3,           //数据类型
            "pUnit": "%",         //单位
            "rwFlg": 1,           //读写标志
            "atMid": 1,           //点所属模块id
            "pInfo": {}          //信息描述：转发配置参数
    		},
          //软件点例子
          {
            "pLink": 1,           //索引1 为软件点
            "pName": "sw_name1",  //点名
            "pDesc": "desc",      //点位描述
            "atChn": 0,           //软件点通道
            "pType": 3,           //数据类型
            "pUnit": ,            //单位
            "rwFlg": 0,           //读写标志
            "atMid": 1,           //点所属模块id
            "pInfo": {}          //信息描述：转发配置参数
          },
          //网络点例子
          {
            "pLink": 2,
            "pName": "bs_name3",
            "pDesc": "desc3",
            "atChn": 0,
            "pType": 3,
            "pUnit": "105",
            "rwFlg": 1,
            "atMid": 1,
            "pInfo": {
              "tCom": 15,
              "tPro": 1024,
              "sCom": 0,
              "sPro": 0,
              "sPar": {
                "sid": 2,
                "fuc": 3,
                "reg": 123,
                "len": 2
              }
            }
          }
      ]
  }
  ```

- 响应状态

| 状态码 | 结果                                        |
| ------ | ------------------------------------------- |
| 200    | OK                                          |
| 404    | 2003  - Node设备不存在<br />2106 - 组不存在 |



#### 查询所有点位 AllTags (有资源风险，待确认)

- 路径

  > *GET* /api/v2/AllTags

- 请求头

  **Content-Type** application/json

  **Authorization** Bearer <token>

- 请求体

  无

- 响应

  ```json
  {
     "AllTags":[
         {
             //node name
              "node": "modbus-node",
              mid: 1,
             //group name
              "group": "config_modbus_tcp_sample_2",
              "tags": [
                  //硬件点例子
                  {
                    "pLink": 0,           //索引0 为硬件点
                    "pName": "hw_name1",  //点名
                    "pDesc": "desc",      //点位描述
                    "atChn": 0,           //硬件点通道
                    "pType": 3,           //数据类型
                    "pUnit": "%",         //单位
                    "rwFlg": 1,           //读写标志
                    "atMid": 1,           //点所属模块id
                    "pInfo": {}          //信息描述：转发配置参数
                  },
                  {
                      //....
                  }
            	]
          },
         {
            //node name
              "node": "modbus-node",
              mid: 1,
             //group name
              "group": "config_modbus_tcp_sample_2",
              "tags": [
                  //硬件点例子
                  {
                    "pLink": 0,           //索引0 为硬件点
                    "pName": "hw_name1",  //点名
                    "pDesc": "desc",      //点位描述
                    "atChn": 0,           //硬件点通道
                    "pType": 3,           //数据类型
                    "pUnit": "%",         //单位
                    "rwFlg": 1,           //读写标志
                    "atMid": 1,           //点所属模块id
                    "pInfo": {}          //信息描述：转发配置参数
                  },
                  //软件点例子
                  {
                    "pLink": 1,           //索引1 为软件点
                    "pName": "sw_name1",  //点名
                    "pDesc": "desc",      //点位描述
                    "atChn": 0,           //软件点通道
                    "pType": 3,           //数据类型
                    "pUnit": ,            //单位
                    "rwFlg": 0,           //读写标志
                    "atMid": 1,           //点所属模块id
                    "pInfo": {}          //信息描述：转发配置参数
                  }
            	]           
         },
         {
             //node name
              "node": "modbus-node",
              mid: 1,
             //group name
              "group": "config_modbus_tcp_sample_2",
              "tags": [
                  //硬件点例子
                  {
                    "pLink": 0,           //索引0 为硬件点
                    "pName": "hw_name1",  //点名
                    "pDesc": "desc",      //点位描述
                    "atChn": 0,           //硬件点通道
                    "pType": 3,           //数据类型
                    "pUnit": "%",         //单位
                    "rwFlg": 1,           //读写标志
                    "atMid": 1,           //点所属模块id
                    "pInfo": {}          //信息描述：转发配置参数
                  },
                  //网络点例子
                  {
                    "pLink": 2,
                    "pName": "bs_name3",
                    "pDesc": "desc3",
                    "atChn": 0,
                    "pType": 3,
                    "pUnit": "105",
                    "rwFlg": 1,
                    "atMid": 1,
                    "pInfo": {
                      "tCom": 15,
                      "tPro": 1024,
                      "sCom": 0,
                      "sPro": 0,
                      "sPar": {
                        "sid": 2,
                        "fuc": 3,
                        "reg": 123,
                        "len": 2
                     }
                  }
            	]
         }
      ]
  }
  ```

- 响应状态

| 状态码 | 结果 |
| ------ | ---- |
| 200    | OK   |



#### 更新点位Tags

- 路径

  > *PUT* /api/v2/tags

- 请求头

  **Content-Type** application/json

  **Authorization** Bearer <token>

- 请求体

  | 参数名 | 参数说明               | 备注      |
  | ------ | ---------------------- | --------- |
  | mid    | id 设备唯一标识        | key值 int |
  | name   | 需要配置的设备名称     | string    |
  | group  | 组名                   | string    |
  | tags   | 需要更新的点位tags列表 | string    |

  ```json
  {
     //node name
      "node": "modbus-node",
      mid: 1,
     //group name
      "group": "config_modbus_tcp_sample_2",
      "tags": [
          //硬件点例子
          {
            "pLink": 0,           //索引0 为硬件点
            "pName": "hw_name1",  //点名
            "pDesc": "desc",      //点位描述
            "atChn": 0,           //硬件点通道
            "pType": 3,           //数据类型
            "pUnit": "%",         //单位
            "rwFlg": 1,           //读写标志
            "atMid": 1,           //点所属模块id
            "pInfo": {}          //信息描述：转发配置参数
    		},
          //软件点例子
          {
            "pLink": 1,           //索引1 为软件点
            "pName": "sw_name1",  //点名
            "pDesc": "desc",      //点位描述
            "atChn": 0,           //软件点通道
            "pType": 3,           //数据类型
            "pUnit": ,            //单位
            "rwFlg": 0,           //读写标志
            "atMid": 1,           //点所属模块id
            "pInfo": {}          //信息描述：转发配置参数
          },
          //网络点例子
          {
            "pLink": 2,
            "pName": "bs_name3",
            "pDesc": "desc3",
            "atChn": 0,
            "pType": 3,
            "pUnit": "105",
            "rwFlg": 1,
            "atMid": 1,
            "pInfo": {
              "tCom": 15,
              "tPro": 1024,
              "sCom": 0,
              "sPro": 0,
              "sPar": {
                "sid": 2,
                "fuc": 3,
                "reg": 123,
                "len": 2
              }
            }
          }
      ]
  }
  ```

- 响应

  ```json
  {
     //node name
      "node": "modbus-node",
      mid: 1,
     //group name
      "group": "config_modbus_tcp_sample_2",
      "tags": [
          {
              "oid": 1,
              "id": 4,
              "pName": "M_3AIO_UI1",
              "pDesc": "NULL",
              "pRang": "8",
              "pUnit": "%",
              "pStep": "",
              "pArea": "",
              "pInfo": {
                "sPar": {
                  "sid": 123,
                  "fuc": 3,
                  "reg": 300,
                  "len": 8,
                  "mac": "502 192.168.108.240"
                },
                "sCom": 15,
                "sPro": 6
              },
              "info1": "lan1 6 0 502 192.168.108.240 0",
              "info2": "",
              "ctlNo": 3,
              "rwFlg": 0,
              "pType": 3,
              "mName": "M1_ECM26_LAN",
              "mType": 14,
              "atMid": 2,
              "atChn": 123,
              "pAddr": 300,
              "pLink": 2,
              "async": 0,
              "pLinkName": "网络点",
              "atChnName": 123,
              "proName": "lan1 6 0 502 192.168.108.240 0"           
            }
    		},
          {
              "oid": 1,
              "id": 4,
              "pName": "M_3AIO_UI1",
              "pDesc": "NULL",
              "pUnit": "%",
              "pInfo": {
                "sPar": {
                  "sid": 123,
                  "fuc": 3,
                  "reg": 300,
                  "len": 8,
                  "mac": "502 192.168.108.240"
                },
                "sCom": 15,
                "sPro": 6
              },
              "rwFlg": 0,
              "pType": 3,
              "mName": "M1_ECM26_LAN",
              "atMid": 2,
          },
          //网络点例子
          {
            "pLink": 2,
            "pName": "bs_name3",
            "pDesc": "desc3",
            "atChn": 0,
            "pType": 3,
            "pUnit": "105",
            "rwFlg": 1,
            "atMid": 1,
            "pInfo": {
              "tCom": 15,
              "tPro": 1024,
              "sCom": 0,
              "sPro": 0,
              "sPar": {
                "sid": 2,
                "fuc": 3,
                "reg": 123,
                "len": 2
              }
            }
          }
      ]
  }
  ```

- 响应状态

| 状态码 | 结果                                                         |
| ------ | ------------------------------------------------------------ |
| 200    | OK                                                           |
| 404    | 2003  - Node设备不存在<br />2106 - 组不存在                  |
| 206    | 2201 - tag 点位不存在<br />2202 - 标签名称冲突<br/>2203 - 标签属性不支持<br/>2204 - 标签类型不支持<br/>2205 - 标签地址格式无效 |



#### 删除点位Tags

- 路径

  > *DELETE* /api/v2/tags

- 请求头

  **Content-Type** application/json

  **Authorization** Bearer <token>

- 请求体

  | 参数名 | 参数说明               | 备注      |
  | ------ | ---------------------- | --------- |
  | mid    | id 设备唯一标识        | key值 int |
  | name   | 需要配置的设备名称     | string    |
  | group  | 组名                   | string    |
  | tags   | 需要更新的点位tags列表 | string    |

  ```json
  {
     //node name
      "node": "modbus-node",
      "mid": 1,
     //group name
      "group": "config_modbus_tcp_sample_2",
      "tags":[
          {
               "tag":"hw_name1",
     			 "oid":0
          },
          {
               "tag":"hw_name2",
      		 "oid":1
          }
      ]
  }
  ```

- 响应

  ```json
  {
      "msg":"点位删除成功"
      "error": 0
  }
  ```

- 响应状态

| 状态码 | 结果                                        |
| ------ | ------------------------------------------- |
| 200    | OK                                          |
| 404    | 2003  - Node设备不存在<br />2106 - 组不存在 |
| 206    | 2201 - tag 点位不存在                       |



### 系统设置







### 模块设置









### 实时数据







### 固件升级







### 网关日志 







### RS485设置







### 清除模块及点表记录







### 执行命令







### 获取文件内容





### Lora 配对、升级







### 红外接口





### 批量下发





### 批量获取配置信息







### 本地配置









#### 

# -附录-

## 1、模块通道索引：

### BEC_22L

| IO类型 | 通道号范围 | 总数 |
| ------ | ---------- | ---- |
| UI     | 0--7       | 8    |
| BI     | 8--13      | 6    |
| BO     | 14--15     | 2    |
| AO     | 16--19     | 4    |
| CO     | 20--21     | 2    |


### 08DL

| IO类型 | 通道号范围 | 总数 |
| ------ | ---------- | ---- |
| BI     | 0--7       | 8    |


### 06DL

| IO类型 | 通道号范围 | 总数 |
| ------ | ---------- | ---- |
| BI     | 0--3       | 4    |
| B0     | 4--5       | 2    |


### 06AL

| IO类型 | 通道号范围 | 总数 |
| ------ | ---------- | ---- |
| UI     | 0--2       | 3    |
| A0     | 3--5       | 3    |


### FCM

| IO类型 | 通道号范围 | 总数 |
| ------ | ---------- | ---- |
| AI     | 0--1       | 2    |
| AO     | 2--6       | 5    |
| BO     | 7--12      | 6    |


### ACM

| IO类型 | 通道号范围 | 总数 |
| ------ | ---------- | ---- |
| AI     | 0--3       | 4    |
| AO     | 4--11      | 8    |
| BO     | 12--19     | 8    |


### 7IO

| IO类型 | 通道号范围 | 总数 |
| ------ | ---------- | ---- |
| UI     | 0--3       | 4    |
| B0     | 4--6       | 3    |


### 3AIO

| IO类型 | 通道号范围 | 总数 |
| ------ | ---------- | ---- |
| UI     | 0--7       | 8    |
| BI     | 8--17      | 10   |
| BO     | 18--25     | 8    |
| AO     | 26--27     | 2    |
| CO     | 28--29     | 2    |


### ECM06

| IO类型 | 通道号范围 | 总数 |
| ------ | ---------- | ---- |
| UI     | 0--3       | 4    |
| X0     | 4--5       | 2    |


### ECM26-(T1)

| IO类型 | 通道号范围 | 总数 |
| ------ | ---------- | ---- |
| UI     | 0--15      | 16   |
| X0     | 16--19     | 4    |
| CO     | 20--25     | 6    |



### ECM26-（T2\T3）

| IO类型 | 通道号范围 | 总数 |
| ------ | ---------- | ---- |
| UI     | 0--15      | 16   |
| X0     | 16--21     | 6    |
| CO     | 22--25     | 4    |


## 2、模块版本号说明：

### 格式：

> HxxxSxxx.{compile time}.Txxx
> H-硬件版本代号，S固件版本代号，T-子类型代号
> 举例，上传的升级包：DTU_H210S116T001.bin, 版本获取上报的字段为："H210S116.2024121216241512.T001"


### 网关子类型的定义：

| 子类型  | 代号 | 说明 |
| ------- | ---- | ---- |
| BEC-22L | T0   |      |
| IGW     | T1   |      |


### ACM子类型的定义：

| 子类型 | 代号 | 说明                           |
| ------ | ---- | ------------------------------ |
| ACM01  | T1   | ACM01小体积插头版              |
| ACM02  | T2   | ACM01小体积插头版,带红外延长线 |
| ACM03  | T3   | ACM220交流220V接线版           |
| ACM04  | T4   | ACM380 ,3相接线版              |

### FCM子类型的定义：

| 子类型 | 代号 | 说明                                             |
| ------ | ---- | ------------------------------------------------ |
| FCM01  | T1   | H360带红外;   H302 EEPROM 512k;  H300 EEPROM 2k; |
|        |      |                                                  |
|        |      |                                                  |


### MTU/DTU子类型的定义：

| 子类型                      | 代号 | 说明                                                       |
| --------------------------- | ---- | ---------------------------------------------------------- |
| 海信日立、约克（青岛）-hbus | T1   | 适配海信日立多联机                                         |
| 海尔TX系列-485              | T2   | （测试外机型号：RFC450TX-K、RFC400TX-K）集控接口波特率9600 |
| 海尔KX系列-485              | T3   | （测试外机型号：FDC450KXZE1GQ）集控接口波特率38400         |
| TCL-can                     | T4   |                                                            |
| 格力-can                    | T5   |                                                            |
| 美的-485                    | T6   |                                                            |
| 天加-485                    | T7   |                                                            |
| 大金-hbs                    | T8   |                                                            |
| 富士通-协议转换器           | T9   |                                                            |
| DTU-modbus                  | T10  | ModBus数据采集单元                                         |
| DTU-HBS                     | T11  | hbs数据采集单元                                            |
| DTU-CAN                     | T12  | can数据采集单元                                            |
| 东芝-hbs                    | T13  |                                                            |


### ECM06\26子类型的定义：

| 子类型          | 代号 | 说明           |
| --------------- | ---- | -------------- |
| ECM06\ECM26     | T1   | io旧版本       |
| FDC0402\FDC1610 | T2   | 有线版(无lora) |
| ECM0402\ECM1610 | T3   | 无线版(带lora) |





















