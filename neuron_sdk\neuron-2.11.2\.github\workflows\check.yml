name: check

on: 
  push:
  pull_request:
  release:
    types:
      - published

jobs:
  clang_format_check:
    runs-on: ubuntu-20.04

    steps:
      # checkout
      - uses: actions/checkout@v4
        with:
          submodules: recursive

      - name: Run clang-format style check for C/C++ programs.
        uses: DoozyX/clang-format-lint-action@v0.18
        with:
          source: 'src plugins include tests'
          exclude: 'include/neuron/utils/uthash.h include/neuron/utils/utarray.h include/neuron/utils/utlist.h include/neuron/utils/zlog.h src/otel/trace.pb-c.h src/otel/trace.pb-c.c src/otel/resource.pb-c.h src/otel/resource.pb-c.c src/otel/common.pb-c.c src/otel/common.pb-c.h' 
          style: file
          clangFormatVersion: 10

  cppcheck:
    runs-on: ubuntu-20.04

    steps:
      - uses: actions/checkout@v4
        with:
          submodules: recursive
      
      - name: cppcheck
        run: |
          sudo apt install cppcheck
          cppcheck --enable=all  --error-exitcode=2 --inline-suppr --suppress=missingInclude --suppress=unusedFunction --suppress=*:src/otel/trace.pb-c.c --suppress=*:src/otel/common.pb-c.c --suppress=*:src/otel/resource.pb-c.c  ./plugins ./src
          cppcheck --enable=all  --error-exitcode=2 --inline-suppr --suppress=missingInclude  --suppress=variableScope ./simulator

  ut:
    runs-on: ubuntu-20.04
    container: ghcr.io/neugates/build:x86_64-v2.11

    steps:
      - uses: actions/checkout@v4
        with:
          submodules: recursive
      
      - name: compile
        run: |
          git config --global --add safe.directory $(pwd)
          mkdir -p build && cd build
          cmake -DUSE_GCOV=1 .. -DCMAKE_TOOLCHAIN_FILE=../cmake/x86_64-linux-gnu.cmake
          make -j4

      - name: unit test
        run: |
          cd build
          ctest --output-on-failure

      - name: create cov report
        run: |
            sudo apt-get update
            sudo apt-get -y install lcov
            mkdir -p cov_report
            ./create_cov_report.sh ut

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v4
        with:
          verbose: true
          fail_ci_if_error: true
          token: ${{ secrets.CODECOV_TOKEN }}
          directory: ./cov_report/
          files: ./cov_report/cov-ut.info

  pft:
    runs-on: ubuntu-20.04
    container: ghcr.io/neugates/build:x86_64-v2.11
    strategy:
      matrix:
        plugin: [core, ekuiper, modbus, mqtt, azure, metrics]

    steps:
      - uses: actions/checkout@v4
        with:
          submodules: recursive

      - name: setup dependencies
        run: |
          sudo apt-get install -y mosquitto
          pip install -U pytest requests prometheus-client gmqtt pynng sniffio
          sudo apt-get install -y socat

      - name: compile
        run: |
          git config --global --add safe.directory $(pwd)
          mkdir -p build && cd build
          cmake -DUSE_GCOV=1 .. -DCMAKE_TOOLCHAIN_FILE=../cmake/x86_64-linux-gnu.cmake
          make -j4

      - name: function test
        run: |
          if [ "${{ matrix.plugin }}" = "core" ]; then
            pytest -s -v tests/ft \
              --ignore=tests/ft/app/test_ekuiper.py          \
              --ignore=tests/ft/app/test_mqtt.py             \
              --ignore=tests/ft/app/test_azure.py            \
              --ignore=tests/ft/driver/test_modbus.py        \
              --ignore=tests/ft/metrics/test_metrics.py
          elif [ "${{ matrix.plugin }}" = "ekuiper" ]; then
            pytest -s -v tests/ft/app/"test_ekuiper.py"
          elif [ "${{ matrix.plugin }}" = "modbus" ]; then
            pytest -s -v tests/ft/driver/"test_modbus.py"
          elif [ "${{ matrix.plugin }}" = "mqtt" ]; then
            pytest -s -v tests/ft/app/"test_mqtt.py"
          elif [ "${{ matrix.plugin }}" = "azure" ]; then
            pytest -s -v tests/ft/app/"test_azure.py"
          else
            pytest -s -v tests/ft/metrics/"test_metrics.py"          
          fi

      - name: create cov report
        run: |
            sudo apt-get update
            sudo apt-get -y install lcov
            mkdir -p cov_report
            ./create_cov_report.sh ${{ matrix.plugin }}

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v4
        with:
          verbose: true
          fail_ci_if_error: true
          token: ${{ secrets.CODECOV_TOKEN }}
          directory: ./cov_report/
          files: ./cov_report/cov-${{ matrix.plugin }}.info
