/**
 * NEURON IIoT System for Industry 4.0
 * Copyright (C) 2020-2022 EMQ Technologies Co., Ltd All rights reserved.
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 3 of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with this program; if not, write to the Free Software Foundation,
 * Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.
 **/

/*
 * DO NOT EDIT THIS FILE MANUALLY!
 * It was automatically generated by `json-autotype`.
 */

#include <stdlib.h>
#include <string.h>

#include <jansson.h>

#include "define.h"
#include "msg.h"
#include "json/json.h"

#include "json/neu_json_scan.h"

int neu_json_decode_scan_tags_req(char *buf, neu_json_scan_tags_req_t **result)
{
    void *json_obj = neu_json_decode_new(buf);
    if (NULL == json_obj) {
        return -1;
    }

    neu_json_scan_tags_req_t *req = calloc(1, sizeof(neu_json_scan_tags_req_t));
    if (req == NULL) {
        return -1;
    }

    neu_json_elem_t req_node_elems[] = { {
        .name = "node",
        .t    = NEU_JSON_STR,
    } };

    neu_json_elem_t req_id_elems[] = {
        {
            .name = "id",
            .t    = NEU_JSON_STR,
        },
    };

    neu_json_elem_t req_ctx_elems[] = {
        {
            .name = "ctx",
            .t    = NEU_JSON_STR,
        },
    };

    if (0 !=
        neu_json_decode_by_json(json_obj, NEU_JSON_ELEM_SIZE(req_node_elems),
                                req_node_elems)) {
        neu_json_decode_free(json_obj);
        free(req);
        return -1;
    }

    neu_json_decode_by_json(json_obj, NEU_JSON_ELEM_SIZE(req_id_elems),
                            req_id_elems);

    neu_json_decode_by_json(json_obj, NEU_JSON_ELEM_SIZE(req_ctx_elems),
                            req_ctx_elems);

    req->node = req_node_elems[0].v.val_str;
    req->id   = req_id_elems[0].v.val_str;
    req->ctx  = req_ctx_elems[0].v.val_str;
    *result   = req;

    neu_json_decode_free(json_obj);
    return 0;
}

void neu_json_decode_scan_tags_req_free(neu_json_scan_tags_req_t *req)
{
    free(req->node);
    free(req->id);
    free(req->ctx);
    free(req);
}

int neu_json_encode_scan_tags_resp(void *json_object, void *param)
{
    int                   ret       = 0;
    neu_resp_scan_tags_t *resp      = (neu_resp_scan_tags_t *) param;
    void *                tag_array = neu_json_array();
    int                   len       = 0;

    if (NULL != resp->scan_tags) {
        len = utarray_len(resp->scan_tags);
        utarray_foreach(resp->scan_tags, neu_scan_tag_t *, p_tag)
        {
            if (NULL != p_tag) {
                neu_json_elem_t tag_elems[4];
                tag_elems[0] = (neu_json_elem_t) {
                    .name      = "name",
                    .t         = NEU_JSON_STR,
                    .v.val_str = p_tag->name,
                };
                tag_elems[1] = (neu_json_elem_t) {
                    .name      = "id",
                    .t         = NEU_JSON_STR,
                    .v.val_str = p_tag->id,
                };
                tag_elems[2] = (neu_json_elem_t) {
                    .name      = "tag",
                    .t         = NEU_JSON_INT,
                    .v.val_int = p_tag->tag,
                };
                if (p_tag->is_last_layer) {
                    tag_elems[3] = (neu_json_elem_t) {
                        .name      = "type",
                        .t         = NEU_JSON_INT,
                        .v.val_int = p_tag->type,
                    };
                }

                neu_json_encode_array(tag_array, tag_elems,
                                      p_tag->is_last_layer ? 4 : 3);
            }
        }

        utarray_free(resp->scan_tags);
    }

    neu_json_elem_t resp_elems[] = { {
                                         .name      = "error",
                                         .t         = NEU_JSON_INT,
                                         .v.val_int = resp->error,
                                     },
                                     {
                                         .name      = "total",
                                         .t         = NEU_JSON_INT,
                                         .v.val_int = len,
                                     },
                                     {
                                         .name      = "type",
                                         .t         = NEU_JSON_INT,
                                         .v.val_int = resp->type,
                                     },
                                     {
                                         .name       = "is_array",
                                         .t          = NEU_JSON_BOOL,
                                         .v.val_bool = resp->is_array,
                                     },
                                     {
                                         .name      = "ctx",
                                         .t         = NEU_JSON_STR,
                                         .v.val_str = resp->ctx,
                                     },
                                     {
                                         .name         = "tags",
                                         .t            = NEU_JSON_OBJECT,
                                         .v.val_object = tag_array,
                                     } };

    ret = neu_json_encode_field(json_object, resp_elems,
                                NEU_JSON_ELEM_SIZE(resp_elems));

    return ret;
}
