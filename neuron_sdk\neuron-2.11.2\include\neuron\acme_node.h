/**
 * NEURON IIoT System for Industry 4.0
 * Copyright (C) 2020-2022 EMQ Technologies Co., Ltd All rights reserved.
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 3 of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with this program; if not, write to the Free Software Foundation,
 * Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.
 **/

#ifndef ACME_NODE_HHHHH_
#define ACME_NODE_HHHHH_

#include <stdint.h>
#include <time.h>

#define ACME_EUI_NULL   "0000000000000000"


enum PROTYPE_ENUM{
    E_PRO_MODBUS = 0,
    E_PRO_SANJIANG_ALARM, //三江消防                       1
    E_PRO_HAIWAN_ALARM,   //海湾消防232                    2
    E_PRO_NUODIFEIER,     //泛海三江-诺蒂菲尔(RS232)           3     
    E_PRO_YINKOUTIANCHENG,//泛海三江-营口天成CRT(RS232)        4 
    E_PRO_BEIDAQINGNIAO,  //泛海三江-北大青鸟(can  )           5    
    E_PRO_ModBus_TCP,     //ModBus-TCP(主站)             6                   
    E_PRO_Bacnet_MSTP,    //Bacnet-MS/TP-Server        7                 
    E_PRO_Bacnet_IP,      //Bancet-IP-Client           8                 
    E_PRO_ProfitBus,      //ProfitBus                  9               
    E_PRO_ProfitNET,      //ProfitNET                  10          
    E_PRO_N2,             //N2                         11
    E_PRO_N2_MASTER,      //N2 Master                  12
    
    E_PRO_HAIWAN_CAN   = 33,//海湾消防CAN                  33
    E_PRO_Bacnet_IP_SV  =34,//Bancet-IP SERVER         34 
    E_PRO_Bacnet_MSTP_C =35,//Bacnet-MS/TP-Client      35 
    E_PRO_MODBUS_SLAVE  =36, //ModBus-RTU(从站)          36
    E_PRO_ModBus_TCP_SLV=37,//ModBus-TCP(从站)           37
    E_PRO_HBUS_HAIXIN  = 38,//hombus-海信等多联机            38
    E_PRO_ACME_AIRLINK = 39,//ACME 空调一体化协议控制点          39
    E_PRO_ACME_IOLINK  = 40,//ACME 内部协议控制点             40
    E_PRO_SIEMENS_S7_Ci= 41,//Siemens S7 PLCs Client   41
    E_PRO_SIEMENS_S7_Sv= 42,//Siemens S7 PLCs Server   42
    E_PRO_HARDWARE_PT  = 43,//硬件点                      43
    E_PRO_DLT645       = 44,//dlt645                   44
    E_PRO_GREE_DUCT    = 45,//格力风管机                    45
    E_PRO_SYSTEM_PAR   = 46,//系统参数                     46
    E_PRO_LONWORKS     = 47,//lonworks                 47
    E_PRO_VRV_MODBUS   = 48,//（TCL...）多联机modbus协议转换器 48
    E_PRO_JMFK_DLT645  = 49,//金茂费控电表
    
    E_PRO_MQTT         = 1024,     //ACME-MQTT
    E_PRO_CTWING_MQTT  = 1025,     //天翼物联-MQTT
    E_PRO_ONENET_MQTT  = 1026,     //中移物联-MQTT
    
    E_PRO_OTHER
}; 

enum ACME_MODULE_TYPE{ //modetype
    BEC_22L = 0,
    IGW     = 0,
    SPT     = 0,
    IOM_08DL, //1
    IOM_06DL, //2
    IOM_06AL, //3
    IOM_GWL01,//4
    IOM_FCM,  //5
    IOM_07IO, //6
    IOM_3XIO, //7
    ACM,      //8
    ICM,      //9
    SCM,      //10
    DTM,      //11
    IGW_OCC,  //12
    ECM06,    //13
    ECM26,    //14
    X2X_GW,   //15
    PCM,      //16 WDTSY
};


// 设备节点的基本信息
typedef struct {
    int                 mid;        //mid 设备唯一id
    int                 pid;
    int                 link;
    int                 modeType;
    char*                eui;
    int                 deviceCode;
    char*                version;
    int                 subType;
    uint8_t             online; //0-offline   1-online
    time_t              createTime;
    time_t              updateTime;
    char *              userData;
}node_base_t;

#endif
