/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: opentelemetry/proto/common/v1/common.proto */

/* Do not generate deprecated warnings for self */
#ifndef PROTOBUF_C__NO_DEPRECATED
#define PROTOBUF_C__NO_DEPRECATED
#endif

#include "common.pb-c.h"
void opentelemetry__proto__common__v1__any_value__init(
    Opentelemetry__Proto__Common__V1__AnyValue *message)
{
    static const Opentelemetry__Proto__Common__V1__AnyValue init_value =
        OPENTELEMETRY__PROTO__COMMON__V1__ANY_VALUE__INIT;
    *message = init_value;
}
size_t opentelemetry__proto__common__v1__any_value__get_packed_size(
    const Opentelemetry__Proto__Common__V1__AnyValue *message)
{
    assert(message->base.descriptor ==
           &opentelemetry__proto__common__v1__any_value__descriptor);
    return protobuf_c_message_get_packed_size(
        (const ProtobufCMessage *) (message));
}
size_t opentelemetry__proto__common__v1__any_value__pack(
    const Opentelemetry__Proto__Common__V1__AnyValue *message, uint8_t *out)
{
    assert(message->base.descriptor ==
           &opentelemetry__proto__common__v1__any_value__descriptor);
    return protobuf_c_message_pack((const ProtobufCMessage *) message, out);
}
size_t opentelemetry__proto__common__v1__any_value__pack_to_buffer(
    const Opentelemetry__Proto__Common__V1__AnyValue *message,
    ProtobufCBuffer *                                 buffer)
{
    assert(message->base.descriptor ==
           &opentelemetry__proto__common__v1__any_value__descriptor);
    return protobuf_c_message_pack_to_buffer((const ProtobufCMessage *) message,
                                             buffer);
}
Opentelemetry__Proto__Common__V1__AnyValue *
opentelemetry__proto__common__v1__any_value__unpack(
    ProtobufCAllocator *allocator, size_t len, const uint8_t *data)
{
    return (Opentelemetry__Proto__Common__V1__AnyValue *)
        protobuf_c_message_unpack(
            &opentelemetry__proto__common__v1__any_value__descriptor, allocator,
            len, data);
}
void opentelemetry__proto__common__v1__any_value__free_unpacked(
    Opentelemetry__Proto__Common__V1__AnyValue *message,
    ProtobufCAllocator *                        allocator)
{
    if (!message)
        return;
    assert(message->base.descriptor ==
           &opentelemetry__proto__common__v1__any_value__descriptor);
    protobuf_c_message_free_unpacked((ProtobufCMessage *) message, allocator);
}
void opentelemetry__proto__common__v1__array_value__init(
    Opentelemetry__Proto__Common__V1__ArrayValue *message)
{
    static const Opentelemetry__Proto__Common__V1__ArrayValue init_value =
        OPENTELEMETRY__PROTO__COMMON__V1__ARRAY_VALUE__INIT;
    *message = init_value;
}
size_t opentelemetry__proto__common__v1__array_value__get_packed_size(
    const Opentelemetry__Proto__Common__V1__ArrayValue *message)
{
    assert(message->base.descriptor ==
           &opentelemetry__proto__common__v1__array_value__descriptor);
    return protobuf_c_message_get_packed_size(
        (const ProtobufCMessage *) (message));
}
size_t opentelemetry__proto__common__v1__array_value__pack(
    const Opentelemetry__Proto__Common__V1__ArrayValue *message, uint8_t *out)
{
    assert(message->base.descriptor ==
           &opentelemetry__proto__common__v1__array_value__descriptor);
    return protobuf_c_message_pack((const ProtobufCMessage *) message, out);
}
size_t opentelemetry__proto__common__v1__array_value__pack_to_buffer(
    const Opentelemetry__Proto__Common__V1__ArrayValue *message,
    ProtobufCBuffer *                                   buffer)
{
    assert(message->base.descriptor ==
           &opentelemetry__proto__common__v1__array_value__descriptor);
    return protobuf_c_message_pack_to_buffer((const ProtobufCMessage *) message,
                                             buffer);
}
Opentelemetry__Proto__Common__V1__ArrayValue *
opentelemetry__proto__common__v1__array_value__unpack(
    ProtobufCAllocator *allocator, size_t len, const uint8_t *data)
{
    return (Opentelemetry__Proto__Common__V1__ArrayValue *)
        protobuf_c_message_unpack(
            &opentelemetry__proto__common__v1__array_value__descriptor,
            allocator, len, data);
}
void opentelemetry__proto__common__v1__array_value__free_unpacked(
    Opentelemetry__Proto__Common__V1__ArrayValue *message,
    ProtobufCAllocator *                          allocator)
{
    if (!message)
        return;
    assert(message->base.descriptor ==
           &opentelemetry__proto__common__v1__array_value__descriptor);
    protobuf_c_message_free_unpacked((ProtobufCMessage *) message, allocator);
}
void opentelemetry__proto__common__v1__key_value_list__init(
    Opentelemetry__Proto__Common__V1__KeyValueList *message)
{
    static const Opentelemetry__Proto__Common__V1__KeyValueList init_value =
        OPENTELEMETRY__PROTO__COMMON__V1__KEY_VALUE_LIST__INIT;
    *message = init_value;
}
size_t opentelemetry__proto__common__v1__key_value_list__get_packed_size(
    const Opentelemetry__Proto__Common__V1__KeyValueList *message)
{
    assert(message->base.descriptor ==
           &opentelemetry__proto__common__v1__key_value_list__descriptor);
    return protobuf_c_message_get_packed_size(
        (const ProtobufCMessage *) (message));
}
size_t opentelemetry__proto__common__v1__key_value_list__pack(
    const Opentelemetry__Proto__Common__V1__KeyValueList *message, uint8_t *out)
{
    assert(message->base.descriptor ==
           &opentelemetry__proto__common__v1__key_value_list__descriptor);
    return protobuf_c_message_pack((const ProtobufCMessage *) message, out);
}
size_t opentelemetry__proto__common__v1__key_value_list__pack_to_buffer(
    const Opentelemetry__Proto__Common__V1__KeyValueList *message,
    ProtobufCBuffer *                                     buffer)
{
    assert(message->base.descriptor ==
           &opentelemetry__proto__common__v1__key_value_list__descriptor);
    return protobuf_c_message_pack_to_buffer((const ProtobufCMessage *) message,
                                             buffer);
}
Opentelemetry__Proto__Common__V1__KeyValueList *
opentelemetry__proto__common__v1__key_value_list__unpack(
    ProtobufCAllocator *allocator, size_t len, const uint8_t *data)
{
    return (Opentelemetry__Proto__Common__V1__KeyValueList *)
        protobuf_c_message_unpack(
            &opentelemetry__proto__common__v1__key_value_list__descriptor,
            allocator, len, data);
}
void opentelemetry__proto__common__v1__key_value_list__free_unpacked(
    Opentelemetry__Proto__Common__V1__KeyValueList *message,
    ProtobufCAllocator *                            allocator)
{
    if (!message)
        return;
    assert(message->base.descriptor ==
           &opentelemetry__proto__common__v1__key_value_list__descriptor);
    protobuf_c_message_free_unpacked((ProtobufCMessage *) message, allocator);
}
void opentelemetry__proto__common__v1__key_value__init(
    Opentelemetry__Proto__Common__V1__KeyValue *message)
{
    static const Opentelemetry__Proto__Common__V1__KeyValue init_value =
        OPENTELEMETRY__PROTO__COMMON__V1__KEY_VALUE__INIT;
    *message = init_value;
}
size_t opentelemetry__proto__common__v1__key_value__get_packed_size(
    const Opentelemetry__Proto__Common__V1__KeyValue *message)
{
    assert(message->base.descriptor ==
           &opentelemetry__proto__common__v1__key_value__descriptor);
    return protobuf_c_message_get_packed_size(
        (const ProtobufCMessage *) (message));
}
size_t opentelemetry__proto__common__v1__key_value__pack(
    const Opentelemetry__Proto__Common__V1__KeyValue *message, uint8_t *out)
{
    assert(message->base.descriptor ==
           &opentelemetry__proto__common__v1__key_value__descriptor);
    return protobuf_c_message_pack((const ProtobufCMessage *) message, out);
}
size_t opentelemetry__proto__common__v1__key_value__pack_to_buffer(
    const Opentelemetry__Proto__Common__V1__KeyValue *message,
    ProtobufCBuffer *                                 buffer)
{
    assert(message->base.descriptor ==
           &opentelemetry__proto__common__v1__key_value__descriptor);
    return protobuf_c_message_pack_to_buffer((const ProtobufCMessage *) message,
                                             buffer);
}
Opentelemetry__Proto__Common__V1__KeyValue *
opentelemetry__proto__common__v1__key_value__unpack(
    ProtobufCAllocator *allocator, size_t len, const uint8_t *data)
{
    return (Opentelemetry__Proto__Common__V1__KeyValue *)
        protobuf_c_message_unpack(
            &opentelemetry__proto__common__v1__key_value__descriptor, allocator,
            len, data);
}
void opentelemetry__proto__common__v1__key_value__free_unpacked(
    Opentelemetry__Proto__Common__V1__KeyValue *message,
    ProtobufCAllocator *                        allocator)
{
    if (!message)
        return;
    assert(message->base.descriptor ==
           &opentelemetry__proto__common__v1__key_value__descriptor);
    protobuf_c_message_free_unpacked((ProtobufCMessage *) message, allocator);
}
void opentelemetry__proto__common__v1__instrumentation_scope__init(
    Opentelemetry__Proto__Common__V1__InstrumentationScope *message)
{
    static const Opentelemetry__Proto__Common__V1__InstrumentationScope
        init_value =
            OPENTELEMETRY__PROTO__COMMON__V1__INSTRUMENTATION_SCOPE__INIT;
    *message = init_value;
}
size_t opentelemetry__proto__common__v1__instrumentation_scope__get_packed_size(
    const Opentelemetry__Proto__Common__V1__InstrumentationScope *message)
{
    assert(
        message->base.descriptor ==
        &opentelemetry__proto__common__v1__instrumentation_scope__descriptor);
    return protobuf_c_message_get_packed_size(
        (const ProtobufCMessage *) (message));
}
size_t opentelemetry__proto__common__v1__instrumentation_scope__pack(
    const Opentelemetry__Proto__Common__V1__InstrumentationScope *message,
    uint8_t *                                                     out)
{
    assert(
        message->base.descriptor ==
        &opentelemetry__proto__common__v1__instrumentation_scope__descriptor);
    return protobuf_c_message_pack((const ProtobufCMessage *) message, out);
}
size_t opentelemetry__proto__common__v1__instrumentation_scope__pack_to_buffer(
    const Opentelemetry__Proto__Common__V1__InstrumentationScope *message,
    ProtobufCBuffer *                                             buffer)
{
    assert(
        message->base.descriptor ==
        &opentelemetry__proto__common__v1__instrumentation_scope__descriptor);
    return protobuf_c_message_pack_to_buffer((const ProtobufCMessage *) message,
                                             buffer);
}
Opentelemetry__Proto__Common__V1__InstrumentationScope *
opentelemetry__proto__common__v1__instrumentation_scope__unpack(
    ProtobufCAllocator *allocator, size_t len, const uint8_t *data)
{
    return (Opentelemetry__Proto__Common__V1__InstrumentationScope *)
        protobuf_c_message_unpack(
            &opentelemetry__proto__common__v1__instrumentation_scope__descriptor,
            allocator, len, data);
}
void opentelemetry__proto__common__v1__instrumentation_scope__free_unpacked(
    Opentelemetry__Proto__Common__V1__InstrumentationScope *message,
    ProtobufCAllocator *                                    allocator)
{
    if (!message)
        return;
    assert(
        message->base.descriptor ==
        &opentelemetry__proto__common__v1__instrumentation_scope__descriptor);
    protobuf_c_message_free_unpacked((ProtobufCMessage *) message, allocator);
}
static const ProtobufCFieldDescriptor
    opentelemetry__proto__common__v1__any_value__field_descriptors[7] = {
        {
            "string_value", 1, PROTOBUF_C_LABEL_NONE, PROTOBUF_C_TYPE_STRING,
            offsetof(Opentelemetry__Proto__Common__V1__AnyValue, value_case),
            offsetof(Opentelemetry__Proto__Common__V1__AnyValue, string_value),
            NULL, &protobuf_c_empty_string,
            0 | PROTOBUF_C_FIELD_FLAG_ONEOF, /* flags */
            0, NULL, NULL                    /* reserved1,reserved2, etc */
        },
        {
            "bool_value", 2, PROTOBUF_C_LABEL_NONE, PROTOBUF_C_TYPE_BOOL,
            offsetof(Opentelemetry__Proto__Common__V1__AnyValue, value_case),
            offsetof(Opentelemetry__Proto__Common__V1__AnyValue, bool_value),
            NULL, NULL, 0 | PROTOBUF_C_FIELD_FLAG_ONEOF, /* flags */
            0, NULL, NULL /* reserved1,reserved2, etc */
        },
        {
            "int_value", 3, PROTOBUF_C_LABEL_NONE, PROTOBUF_C_TYPE_INT64,
            offsetof(Opentelemetry__Proto__Common__V1__AnyValue, value_case),
            offsetof(Opentelemetry__Proto__Common__V1__AnyValue, int_value),
            NULL, NULL, 0 | PROTOBUF_C_FIELD_FLAG_ONEOF, /* flags */
            0, NULL, NULL /* reserved1,reserved2, etc */
        },
        {
            "double_value", 4, PROTOBUF_C_LABEL_NONE, PROTOBUF_C_TYPE_DOUBLE,
            offsetof(Opentelemetry__Proto__Common__V1__AnyValue, value_case),
            offsetof(Opentelemetry__Proto__Common__V1__AnyValue, double_value),
            NULL, NULL, 0 | PROTOBUF_C_FIELD_FLAG_ONEOF, /* flags */
            0, NULL, NULL /* reserved1,reserved2, etc */
        },
        {
            "array_value", 5, PROTOBUF_C_LABEL_NONE, PROTOBUF_C_TYPE_MESSAGE,
            offsetof(Opentelemetry__Proto__Common__V1__AnyValue, value_case),
            offsetof(Opentelemetry__Proto__Common__V1__AnyValue, array_value),
            &opentelemetry__proto__common__v1__array_value__descriptor, NULL,
            0 | PROTOBUF_C_FIELD_FLAG_ONEOF, /* flags */
            0, NULL, NULL                    /* reserved1,reserved2, etc */
        },
        {
            "kvlist_value", 6, PROTOBUF_C_LABEL_NONE, PROTOBUF_C_TYPE_MESSAGE,
            offsetof(Opentelemetry__Proto__Common__V1__AnyValue, value_case),
            offsetof(Opentelemetry__Proto__Common__V1__AnyValue, kvlist_value),
            &opentelemetry__proto__common__v1__key_value_list__descriptor, NULL,
            0 | PROTOBUF_C_FIELD_FLAG_ONEOF, /* flags */
            0, NULL, NULL                    /* reserved1,reserved2, etc */
        },
        {
            "bytes_value", 7, PROTOBUF_C_LABEL_NONE, PROTOBUF_C_TYPE_BYTES,
            offsetof(Opentelemetry__Proto__Common__V1__AnyValue, value_case),
            offsetof(Opentelemetry__Proto__Common__V1__AnyValue, bytes_value),
            NULL, NULL, 0 | PROTOBUF_C_FIELD_FLAG_ONEOF, /* flags */
            0, NULL, NULL /* reserved1,reserved2, etc */
        },
    };
static const unsigned
    opentelemetry__proto__common__v1__any_value__field_indices_by_name[] = {
        4, /* field[4] = array_value */
        1, /* field[1] = bool_value */
        6, /* field[6] = bytes_value */
        3, /* field[3] = double_value */
        2, /* field[2] = int_value */
        5, /* field[5] = kvlist_value */
        0, /* field[0] = string_value */
    };
static const ProtobufCIntRange
    opentelemetry__proto__common__v1__any_value__number_ranges[1 + 1] = {
        { 1, 0 }, { 0, 7 }
    };
const ProtobufCMessageDescriptor
    opentelemetry__proto__common__v1__any_value__descriptor = {
        PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
        "opentelemetry.proto.common.v1.AnyValue",
        "AnyValue",
        "Opentelemetry__Proto__Common__V1__AnyValue",
        "opentelemetry.proto.common.v1",
        sizeof(Opentelemetry__Proto__Common__V1__AnyValue),
        7,
        opentelemetry__proto__common__v1__any_value__field_descriptors,
        opentelemetry__proto__common__v1__any_value__field_indices_by_name,
        1,
        opentelemetry__proto__common__v1__any_value__number_ranges,
        (ProtobufCMessageInit)
            opentelemetry__proto__common__v1__any_value__init,
        NULL,
        NULL,
        NULL /* reserved[123] */
    };
static const ProtobufCFieldDescriptor
    opentelemetry__proto__common__v1__array_value__field_descriptors[1] = {
        {
            "values", 1, PROTOBUF_C_LABEL_REPEATED, PROTOBUF_C_TYPE_MESSAGE,
            offsetof(Opentelemetry__Proto__Common__V1__ArrayValue, n_values),
            offsetof(Opentelemetry__Proto__Common__V1__ArrayValue, values),
            &opentelemetry__proto__common__v1__any_value__descriptor, NULL,
            0,            /* flags */
            0, NULL, NULL /* reserved1,reserved2, etc */
        },
    };
static const unsigned
    opentelemetry__proto__common__v1__array_value__field_indices_by_name[] = {
        0, /* field[0] = values */
    };
static const ProtobufCIntRange
    opentelemetry__proto__common__v1__array_value__number_ranges[1 + 1] = {
        { 1, 0 }, { 0, 1 }
    };
const ProtobufCMessageDescriptor
    opentelemetry__proto__common__v1__array_value__descriptor = {
        PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
        "opentelemetry.proto.common.v1.ArrayValue",
        "ArrayValue",
        "Opentelemetry__Proto__Common__V1__ArrayValue",
        "opentelemetry.proto.common.v1",
        sizeof(Opentelemetry__Proto__Common__V1__ArrayValue),
        1,
        opentelemetry__proto__common__v1__array_value__field_descriptors,
        opentelemetry__proto__common__v1__array_value__field_indices_by_name,
        1,
        opentelemetry__proto__common__v1__array_value__number_ranges,
        (ProtobufCMessageInit)
            opentelemetry__proto__common__v1__array_value__init,
        NULL,
        NULL,
        NULL /* reserved[123] */
    };
static const ProtobufCFieldDescriptor
    opentelemetry__proto__common__v1__key_value_list__field_descriptors[1] = {
        {
            "values", 1, PROTOBUF_C_LABEL_REPEATED, PROTOBUF_C_TYPE_MESSAGE,
            offsetof(Opentelemetry__Proto__Common__V1__KeyValueList, n_values),
            offsetof(Opentelemetry__Proto__Common__V1__KeyValueList, values),
            &opentelemetry__proto__common__v1__key_value__descriptor, NULL,
            0,            /* flags */
            0, NULL, NULL /* reserved1,reserved2, etc */
        },
    };
static const unsigned
    opentelemetry__proto__common__v1__key_value_list__field_indices_by_name
        [] = {
            0, /* field[0] = values */
        };
static const ProtobufCIntRange
    opentelemetry__proto__common__v1__key_value_list__number_ranges[1 + 1] = {
        { 1, 0 }, { 0, 1 }
    };
const ProtobufCMessageDescriptor
    opentelemetry__proto__common__v1__key_value_list__descriptor = {
        PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
        "opentelemetry.proto.common.v1.KeyValueList",
        "KeyValueList",
        "Opentelemetry__Proto__Common__V1__KeyValueList",
        "opentelemetry.proto.common.v1",
        sizeof(Opentelemetry__Proto__Common__V1__KeyValueList),
        1,
        opentelemetry__proto__common__v1__key_value_list__field_descriptors,
        opentelemetry__proto__common__v1__key_value_list__field_indices_by_name,
        1,
        opentelemetry__proto__common__v1__key_value_list__number_ranges,
        (ProtobufCMessageInit)
            opentelemetry__proto__common__v1__key_value_list__init,
        NULL,
        NULL,
        NULL /* reserved[123] */
    };
static const ProtobufCFieldDescriptor
    opentelemetry__proto__common__v1__key_value__field_descriptors[2] = {
        {
            "key", 1, PROTOBUF_C_LABEL_NONE, PROTOBUF_C_TYPE_STRING,
            0, /* quantifier_offset */
            offsetof(Opentelemetry__Proto__Common__V1__KeyValue, key), NULL,
            &protobuf_c_empty_string, 0, /* flags */
            0, NULL, NULL                /* reserved1,reserved2, etc */
        },
        {
            "value", 2, PROTOBUF_C_LABEL_NONE, PROTOBUF_C_TYPE_MESSAGE,
            0, /* quantifier_offset */
            offsetof(Opentelemetry__Proto__Common__V1__KeyValue, value),
            &opentelemetry__proto__common__v1__any_value__descriptor, NULL,
            0,            /* flags */
            0, NULL, NULL /* reserved1,reserved2, etc */
        },
    };
static const unsigned
    opentelemetry__proto__common__v1__key_value__field_indices_by_name[] = {
        0, /* field[0] = key */
        1, /* field[1] = value */
    };
static const ProtobufCIntRange
    opentelemetry__proto__common__v1__key_value__number_ranges[1 + 1] = {
        { 1, 0 }, { 0, 2 }
    };
const ProtobufCMessageDescriptor
    opentelemetry__proto__common__v1__key_value__descriptor = {
        PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
        "opentelemetry.proto.common.v1.KeyValue",
        "KeyValue",
        "Opentelemetry__Proto__Common__V1__KeyValue",
        "opentelemetry.proto.common.v1",
        sizeof(Opentelemetry__Proto__Common__V1__KeyValue),
        2,
        opentelemetry__proto__common__v1__key_value__field_descriptors,
        opentelemetry__proto__common__v1__key_value__field_indices_by_name,
        1,
        opentelemetry__proto__common__v1__key_value__number_ranges,
        (ProtobufCMessageInit)
            opentelemetry__proto__common__v1__key_value__init,
        NULL,
        NULL,
        NULL /* reserved[123] */
    };
static const ProtobufCFieldDescriptor
    opentelemetry__proto__common__v1__instrumentation_scope__field_descriptors
        [4] = {
            {
                "name", 1, PROTOBUF_C_LABEL_NONE, PROTOBUF_C_TYPE_STRING,
                0, /* quantifier_offset */
                offsetof(Opentelemetry__Proto__Common__V1__InstrumentationScope,
                         name),
                NULL, &protobuf_c_empty_string, 0, /* flags */
                0, NULL, NULL /* reserved1,reserved2, etc */
            },
            {
                "version", 2, PROTOBUF_C_LABEL_NONE, PROTOBUF_C_TYPE_STRING,
                0, /* quantifier_offset */
                offsetof(Opentelemetry__Proto__Common__V1__InstrumentationScope,
                         version),
                NULL, &protobuf_c_empty_string, 0, /* flags */
                0, NULL, NULL /* reserved1,reserved2, etc */
            },
            {
                "attributes", 3, PROTOBUF_C_LABEL_REPEATED,
                PROTOBUF_C_TYPE_MESSAGE,
                offsetof(Opentelemetry__Proto__Common__V1__InstrumentationScope,
                         n_attributes),
                offsetof(Opentelemetry__Proto__Common__V1__InstrumentationScope,
                         attributes),
                &opentelemetry__proto__common__v1__key_value__descriptor, NULL,
                0,            /* flags */
                0, NULL, NULL /* reserved1,reserved2, etc */
            },
            {
                "dropped_attributes_count", 4, PROTOBUF_C_LABEL_NONE,
                PROTOBUF_C_TYPE_UINT32, 0, /* quantifier_offset */
                offsetof(Opentelemetry__Proto__Common__V1__InstrumentationScope,
                         dropped_attributes_count),
                NULL, NULL, 0, /* flags */
                0, NULL, NULL  /* reserved1,reserved2, etc */
            },
        };
static const unsigned
    opentelemetry__proto__common__v1__instrumentation_scope__field_indices_by_name
        [] = {
            2, /* field[2] = attributes */
            3, /* field[3] = dropped_attributes_count */
            0, /* field[0] = name */
            1, /* field[1] = version */
        };
static const ProtobufCIntRange
    opentelemetry__proto__common__v1__instrumentation_scope__number_ranges
        [1 + 1] = { { 1, 0 }, { 0, 4 } };
const ProtobufCMessageDescriptor
    opentelemetry__proto__common__v1__instrumentation_scope__descriptor = {
        PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
        "opentelemetry.proto.common.v1.InstrumentationScope",
        "InstrumentationScope",
        "Opentelemetry__Proto__Common__V1__InstrumentationScope",
        "opentelemetry.proto.common.v1",
        sizeof(Opentelemetry__Proto__Common__V1__InstrumentationScope),
        4,
        opentelemetry__proto__common__v1__instrumentation_scope__field_descriptors,
        opentelemetry__proto__common__v1__instrumentation_scope__field_indices_by_name,
        1,
        opentelemetry__proto__common__v1__instrumentation_scope__number_ranges,
        (ProtobufCMessageInit)
            opentelemetry__proto__common__v1__instrumentation_scope__init,
        NULL,
        NULL,
        NULL /* reserved[123] */
    };
