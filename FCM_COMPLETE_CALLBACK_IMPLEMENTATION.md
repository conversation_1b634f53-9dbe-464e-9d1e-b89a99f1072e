# FCM 设备消息处理函数 - 完整回调函数实现

## 概述

根据您的要求，我已经完成了对 `fcm_update_tag_by_address` 函数的完整修改，现在该函数完全通过 adapter 回调函数来获取组信息和点位信息，不再直接调用任何适配器层的接口函数。这确保了插件层和适配器层之间的正确抽象和解耦。

## 完整修改内容

### 1. 扩展 adapter_callbacks_t 结构

在 `neuron_sdk/neuron-2.11.2/include/neuron/adapter.h` 中添加了两个回调函数：

```c
typedef struct adapter_callbacks {
    // ... 现有的回调函数 ...
    void * (*get_dev_base)(neu_adapter_t *adapter);
    UT_array * (*get_groups)(neu_adapter_t *adapter);                           // 获取组信息
    int (*get_tags)(neu_adapter_t *adapter, const char *group, UT_array **tags); // 获取点位信息
    // ...
} adapter_callbacks_t;
```

### 2. 实现回调函数

在 `neuron_sdk/neuron-2.11.2/src/adapter/adapter.c` 中实现：

```c
// 获取组信息的回调函数
static UT_array *adapter_get_groups(neu_adapter_t *adapter)
{
    if(adapter == NULL) {
        return NULL;
    }
    
    if(adapter->module->type == NEU_NA_TYPE_DRIVER) {
        return acme_get_group((neu_adapter_driver_t *)adapter);
    }
    
    return NULL;
}

// 获取点位信息的回调函数
static int adapter_get_tags(neu_adapter_t *adapter, const char *group, UT_array **tags)
{
    if(adapter == NULL || group == NULL || tags == NULL) {
        return -1;
    }
    
    if(adapter->module->type == NEU_NA_TYPE_DRIVER) {
        return neu_adapter_driver_get_tag((neu_adapter_driver_t *)adapter, group, tags);
    }
    
    return -1;
}
```

### 3. 注册回调函数

```c
static const adapter_callbacks_t callback_funs = {
    // ... 现有的回调函数 ...
    .get_dev_base    = adapter_get_dev_base,
    .get_groups      = adapter_get_groups,
    .get_tags        = adapter_get_tags,
};
```

### 4. 完全修改 FCM 点位更新函数

在 `app/acme_driver/acme_lora/business_logic/product/business_fcm.c` 中的关键修改：

**之前的实现（直接调用适配器函数）：**
```c
// 直接调用适配器内部函数（不推荐）
UT_array *groups = neu_adapter_driver_get_group(driver);
// ...
if(neu_adapter_driver_get_tag(driver, group_info->name, &tags) != 0) {
    // ...
}
```

**修改后的实现（使用回调函数）：**
```c
// 通过回调函数获取组信息
neu_adapter_t *adapter = (neu_adapter_t *)driver;
UT_array *groups = plugin->common.adapter_callbacks->get_groups(adapter);
// ...
// 通过回调函数获取点位信息
if(plugin->common.adapter_callbacks->get_tags(adapter, group_info->name, &tags) != 0) {
    // ...
}
```

## 设计优势

### 1. 完全的层次分离
- **插件层**：只通过回调函数接口访问适配器功能
- **适配器层**：提供标准化的回调函数接口
- **无直接依赖**：插件不再直接调用任何适配器内部函数

### 2. 一致的设计模式
- 所有适配器访问都通过 `plugin->common.adapter_callbacks->xxx()` 进行
- 与 `get_dev_base` 保持完全一致的调用模式
- 统一的错误处理和参数验证

### 3. 增强的可测试性
- 可以轻松模拟所有回调函数
- 支持独立的单元测试
- 便于验证不同场景下的行为

### 4. 更好的可维护性
- 清晰的接口定义
- 职责分离明确
- 易于扩展和修改

## 完整的调用流程

```
1. LoRa 设备数据接收
   ↓
2. business_lora_dev_message_handle()
   ↓
3. fcm_dev_message_handle()
   ↓
4. fcm_process_io_status_data()
   ↓
5. fcm_update_tag_values()
   ↓
6. fcm_update_tag_by_address() (循环调用)
   ↓
7. plugin->common.adapter_callbacks->get_groups(adapter)     // 获取组信息
   ↓
8. 遍历所有 groups
   ↓
9. plugin->common.adapter_callbacks->get_tags(adapter, group, &tags)  // 获取点位信息
   ↓
10. 遍历所有 tags，通过 address 字段匹配
    ↓
11. fcm_update_tag_value_direct()
    ↓
12. plugin->common.adapter_callbacks->driver.update()       // 更新点位值
```

## 关键改进对比

| 方面 | 之前的实现 | 修改后的实现 |
|------|------------|--------------|
| 组信息获取 | `neu_adapter_driver_get_group(driver)` | `plugin->common.adapter_callbacks->get_groups(adapter)` |
| 点位信息获取 | `neu_adapter_driver_get_tag(driver, group, &tags)` | `plugin->common.adapter_callbacks->get_tags(adapter, group, &tags)` |
| 点位值更新 | `driver->adapter.cb_funs.driver.update(...)` | `plugin->common.adapter_callbacks->driver.update(...)` |
| 层次分离 | 部分直接调用 | 完全通过回调函数 |
| 可测试性 | 难以模拟 | 易于模拟和测试 |

## Address 映射关系

| Address | 点位功能 | 数据类型 | 说明 |
|---------|----------|----------|------|
| "1" | ONOFF | INT32 | 开关状态 |
| "2" | STEMP | FLOAT | 设定温度 |
| "3" | SMODE | INT32 | 运行模式 |
| "4" | WSPED | INT32 | 风速等级 |
| "5" | RTEMP | FLOAT | 读取温度 |
| "6" | ERROC | INT32 | 错误码 |
| "7" | HUMID | INT32 | 湿度 |

## 测试验证

创建了 `test_fcm_complete_callback.c` 测试程序，验证了：

1. **完整的回调机制**：所有适配器访问都通过回调函数
2. **组信息获取**：`get_groups` 回调函数正常工作
3. **点位信息获取**：`get_tags` 回调函数正常工作
4. **地址匹配**：通过 address 字段成功匹配和更新点位
5. **错误处理**：处理不存在的 address 地址
6. **数据解析**：验证空调数据的正确解析和映射

## 注意事项

1. **内存管理**：确保正确释放 `utarray_free(groups)` 和 `utarray_free(tags)`
2. **错误处理**：检查所有回调函数返回值，处理 NULL 和错误情况
3. **线程安全**：回调函数内部需要考虑线程安全问题
4. **向后兼容**：新增的回调函数不影响现有功能

## 总结

通过这次完整的修改，我们成功地：

1. ✅ **完全消除了直接调用**：插件层不再直接调用任何适配器层函数
2. ✅ **统一了回调模式**：所有适配器访问都通过回调函数进行
3. ✅ **保持了设计一致性**：与 `get_dev_base` 回调函数保持相同的模式
4. ✅ **增强了抽象层次**：插件层和适配器层完全解耦
5. ✅ **提高了可测试性**：可以轻松模拟和测试所有回调函数
6. ✅ **实现了功能需求**：通过 address 字段匹配更新点位值

这个实现完全满足了您的要求：**在 LoRa 插件内不能直接调用 `neu_adapter_driver_get_tag` 等适配器层接口函数，必须通过 adapter 回调函数获取所有信息**。
