# ACME LoRa 到 MQTT 直接推送机制使用指南

## 概述

实现了从 `acme_lora` 适配器直接推送点位数据到 `acme_mqtt` 插件的机制，绕过传统的订阅机制，提供更高效的数据传输。

## 架构设计

```
FCM设备 -> acme_lora适配器 -> 直接推送 -> acme_mqtt插件 -> MQTT云端
```

### 传统方式 vs 直接推送方式

**传统方式**:
```
FCM设备 -> acme_lora -> 定时器 -> 订阅组 -> acme_mqtt -> MQTT云端
```

**直接推送方式**:
```
FCM设备 -> acme_lora -> 实时推送 -> acme_mqtt -> MQTT云端
```

## 实现细节

### 1. 新增消息类型

在 `neuron_sdk/neuron-2.11.2/include/neuron/msg.h` 中新增：

```c
// 消息类型枚举
NEU_REQ_ACME_MQTT_DATA_PUSH,        // 直接推送数据到MQTT插件

// 数据结构
typedef struct neu_acme_mqtt_data_push {
    char driver[NEU_NODE_NAME_LEN];     // 驱动名称
    char group[NEU_GROUP_NAME_LEN];     // 组名称  
    char device_eui[20];                // 设备EUI
    int64_t timestamp;                  // 时间戳
    uint16_t n_tag;                     // 点位数量
    neu_resp_tag_value_t *tags;         // 点位数据数组
} neu_acme_mqtt_data_push_t;
```

### 2. acme_lora 发送端

在 `business_fcm.c` 中的 `fcm_update_tag_values()` 函数：

- 更新点位值到适配器缓存
- 同时收集点位数据
- 调用 `fcm_push_data_to_mqtt()` 直接推送到MQTT插件

### 3. acme_mqtt 接收端

在 `mqtt_plugin_intf.c` 中添加消息处理：

```c
case NEU_REQ_ACME_MQTT_DATA_PUSH:
    error = handle_acme_mqtt_data_push(plugin, data);
    break;
```

在 `mqtt_handle.c` 中实现 `handle_acme_mqtt_data_push()` 函数。

## 使用配置

### 1. MQTT主题格式

数据推送到的MQTT主题格式：
```
/neuron/{gateway_id}/acme/{device_eui}/data
```

示例：
```
/neuron/SPT_GW_001/acme/1234567890ABCDEF/data
```

### 2. JSON数据格式

推送到云端的JSON数据格式：
```json
{
    "timestamp": 1703123456789,
    "driver": "FCM_001", 
    "group": "FCM_001_default_group",
    "tags": [
        {
            "tag": "ONOFF",
            "value": 1,
            "error": 0
        },
        {
            "tag": "STEMP", 
            "value": 25.5,
            "error": 0
        },
        {
            "tag": "RTEMP",
            "value": 24.8,
            "error": 0
        }
    ]
}
```

### 3. MQTT配置

在 acme_mqtt 插件配置中设置：

```json
{
    "client_id": "SPT_GW_001",
    "host": "mqtt.acme-cloud.com",
    "port": 1883,
    "username": "gateway_user",
    "password": "gateway_pass",
    "qos": 1,
    "retain": false
}
```

## 触发机制

### 自动触发

当FCM设备每30秒上报0x30空调状态数据时：

1. `fcm_dev_message_handle()` 接收数据
2. `fcm_process_io_status_data()` 解析数据
3. `fcm_update_tag_values()` 更新点位并推送
4. `fcm_push_data_to_mqtt()` 发送到MQTT插件
5. `handle_acme_mqtt_data_push()` 处理并发布到云端

### 手动触发

也可以在其他业务逻辑中手动调用：

```c
// 准备点位数据
neu_resp_tag_value_t tags[3];
strcpy(tags[0].tag, "ONOFF");
tags[0].value.i32 = 1;
tags[0].value.type = NEU_TYPE_INT32;

// 推送到MQTT
fcm_push_data_to_mqtt(plugin, dev, "default_group", tags, 3);
```

## 优势特点

### 1. 实时性
- 数据产生后立即推送，无需等待定时器
- 减少数据传输延迟

### 2. 效率
- 绕过订阅机制，减少中间环节
- 降低系统资源消耗

### 3. 可靠性
- 使用QoS1确保数据可靠传输
- 完整的错误处理和资源清理

### 4. 可扩展性
- 支持多种设备类型扩展
- 灵活的主题和数据格式配置

## 监控和调试

### 1. 日志监控

```bash
# 查看推送日志
tail -f /var/log/neuron.log | grep "ACME data push"

# 查看MQTT发布日志  
tail -f /var/log/neuron.log | grep "Successfully published"
```

### 2. 统计指标

在acme_mqtt插件中会更新以下指标：
- `NEU_METRIC_TRANS_DATA_5S`: 5秒内传输数据量
- `NEU_METRIC_TRANS_DATA_30S`: 30秒内传输数据量  
- `NEU_METRIC_TRANS_DATA_60S`: 60秒内传输数据量

### 3. 错误处理

常见错误及处理：

- `NEU_ERR_MQTT_IS_NULL`: MQTT客户端未连接
- `NEU_ERR_EINTERNAL`: 内存分配失败
- `NEU_ERR_PARAM_IS_WRONG`: 参数错误

## 注意事项

### 1. 内存管理
- 发送方只在发送失败时释放数据
- 接收方负责释放接收到的数据
- 避免内存泄漏和重复释放

### 2. 线程安全
- 所有操作都在插件线程中执行
- 无需额外的同步机制

### 3. 性能考虑
- 批量更新点位，减少函数调用
- 使用结构体复制，避免指针传递风险

### 4. 扩展性
- 可以轻松扩展到其他设备类型
- 支持自定义主题格式和数据格式

## 测试验证

### 1. 功能测试
```bash
# 启动neuron
./neuron

# 查看FCM设备数据上报
tail -f /var/log/neuron.log | grep "FCM device.*recv message"

# 查看MQTT推送
tail -f /var/log/neuron.log | grep "Successfully published"
```

### 2. 性能测试
- 监控内存使用情况
- 检查数据传输延迟
- 验证MQTT连接稳定性

这个直接推送机制为ACME系统提供了高效、实时的数据传输能力，特别适合IoT场景下的实时数据采集和云端推送需求。
