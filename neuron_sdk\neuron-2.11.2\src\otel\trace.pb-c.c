/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: opentelemetry/proto/trace/v1/trace.proto */

/* Do not generate deprecated warnings for self */
#ifndef PROTOBUF_C__NO_DEPRECATED
#define PROTOBUF_C__NO_DEPRECATED
#endif

#include "trace.pb-c.h"
void opentelemetry__proto__trace__v1__traces_data__init(
    Opentelemetry__Proto__Trace__V1__TracesData *message)
{
    static const Opentelemetry__Proto__Trace__V1__TracesData init_value =
        OPENTELEMETRY__PROTO__TRACE__V1__TRACES_DATA__INIT;
    *message = init_value;
}
size_t opentelemetry__proto__trace__v1__traces_data__get_packed_size(
    const Opentelemetry__Proto__Trace__V1__TracesData *message)
{
    assert(message->base.descriptor ==
           &opentelemetry__proto__trace__v1__traces_data__descriptor);
    return protobuf_c_message_get_packed_size(
        (const ProtobufCMessage *) (message));
}
size_t opentelemetry__proto__trace__v1__traces_data__pack(
    const Opentelemetry__Proto__Trace__V1__TracesData *message, uint8_t *out)
{
    assert(message->base.descriptor ==
           &opentelemetry__proto__trace__v1__traces_data__descriptor);
    return protobuf_c_message_pack((const ProtobufCMessage *) message, out);
}
size_t opentelemetry__proto__trace__v1__traces_data__pack_to_buffer(
    const Opentelemetry__Proto__Trace__V1__TracesData *message,
    ProtobufCBuffer *                                  buffer)
{
    assert(message->base.descriptor ==
           &opentelemetry__proto__trace__v1__traces_data__descriptor);
    return protobuf_c_message_pack_to_buffer((const ProtobufCMessage *) message,
                                             buffer);
}
Opentelemetry__Proto__Trace__V1__TracesData *
opentelemetry__proto__trace__v1__traces_data__unpack(
    ProtobufCAllocator *allocator, size_t len, const uint8_t *data)
{
    return (Opentelemetry__Proto__Trace__V1__TracesData *)
        protobuf_c_message_unpack(
            &opentelemetry__proto__trace__v1__traces_data__descriptor,
            allocator, len, data);
}
void opentelemetry__proto__trace__v1__traces_data__free_unpacked(
    Opentelemetry__Proto__Trace__V1__TracesData *message,
    ProtobufCAllocator *                         allocator)
{
    if (!message)
        return;
    assert(message->base.descriptor ==
           &opentelemetry__proto__trace__v1__traces_data__descriptor);
    protobuf_c_message_free_unpacked((ProtobufCMessage *) message, allocator);
}
void opentelemetry__proto__trace__v1__resource_spans__init(
    Opentelemetry__Proto__Trace__V1__ResourceSpans *message)
{
    static const Opentelemetry__Proto__Trace__V1__ResourceSpans init_value =
        OPENTELEMETRY__PROTO__TRACE__V1__RESOURCE_SPANS__INIT;
    *message = init_value;
}
size_t opentelemetry__proto__trace__v1__resource_spans__get_packed_size(
    const Opentelemetry__Proto__Trace__V1__ResourceSpans *message)
{
    assert(message->base.descriptor ==
           &opentelemetry__proto__trace__v1__resource_spans__descriptor);
    return protobuf_c_message_get_packed_size(
        (const ProtobufCMessage *) (message));
}
size_t opentelemetry__proto__trace__v1__resource_spans__pack(
    const Opentelemetry__Proto__Trace__V1__ResourceSpans *message, uint8_t *out)
{
    assert(message->base.descriptor ==
           &opentelemetry__proto__trace__v1__resource_spans__descriptor);
    return protobuf_c_message_pack((const ProtobufCMessage *) message, out);
}
size_t opentelemetry__proto__trace__v1__resource_spans__pack_to_buffer(
    const Opentelemetry__Proto__Trace__V1__ResourceSpans *message,
    ProtobufCBuffer *                                     buffer)
{
    assert(message->base.descriptor ==
           &opentelemetry__proto__trace__v1__resource_spans__descriptor);
    return protobuf_c_message_pack_to_buffer((const ProtobufCMessage *) message,
                                             buffer);
}
Opentelemetry__Proto__Trace__V1__ResourceSpans *
opentelemetry__proto__trace__v1__resource_spans__unpack(
    ProtobufCAllocator *allocator, size_t len, const uint8_t *data)
{
    return (Opentelemetry__Proto__Trace__V1__ResourceSpans *)
        protobuf_c_message_unpack(
            &opentelemetry__proto__trace__v1__resource_spans__descriptor,
            allocator, len, data);
}
void opentelemetry__proto__trace__v1__resource_spans__free_unpacked(
    Opentelemetry__Proto__Trace__V1__ResourceSpans *message,
    ProtobufCAllocator *                            allocator)
{
    if (!message)
        return;
    assert(message->base.descriptor ==
           &opentelemetry__proto__trace__v1__resource_spans__descriptor);
    protobuf_c_message_free_unpacked((ProtobufCMessage *) message, allocator);
}
void opentelemetry__proto__trace__v1__scope_spans__init(
    Opentelemetry__Proto__Trace__V1__ScopeSpans *message)
{
    static const Opentelemetry__Proto__Trace__V1__ScopeSpans init_value =
        OPENTELEMETRY__PROTO__TRACE__V1__SCOPE_SPANS__INIT;
    *message = init_value;
}
size_t opentelemetry__proto__trace__v1__scope_spans__get_packed_size(
    const Opentelemetry__Proto__Trace__V1__ScopeSpans *message)
{
    assert(message->base.descriptor ==
           &opentelemetry__proto__trace__v1__scope_spans__descriptor);
    return protobuf_c_message_get_packed_size(
        (const ProtobufCMessage *) (message));
}
size_t opentelemetry__proto__trace__v1__scope_spans__pack(
    const Opentelemetry__Proto__Trace__V1__ScopeSpans *message, uint8_t *out)
{
    assert(message->base.descriptor ==
           &opentelemetry__proto__trace__v1__scope_spans__descriptor);
    return protobuf_c_message_pack((const ProtobufCMessage *) message, out);
}
size_t opentelemetry__proto__trace__v1__scope_spans__pack_to_buffer(
    const Opentelemetry__Proto__Trace__V1__ScopeSpans *message,
    ProtobufCBuffer *                                  buffer)
{
    assert(message->base.descriptor ==
           &opentelemetry__proto__trace__v1__scope_spans__descriptor);
    return protobuf_c_message_pack_to_buffer((const ProtobufCMessage *) message,
                                             buffer);
}
Opentelemetry__Proto__Trace__V1__ScopeSpans *
opentelemetry__proto__trace__v1__scope_spans__unpack(
    ProtobufCAllocator *allocator, size_t len, const uint8_t *data)
{
    return (Opentelemetry__Proto__Trace__V1__ScopeSpans *)
        protobuf_c_message_unpack(
            &opentelemetry__proto__trace__v1__scope_spans__descriptor,
            allocator, len, data);
}
void opentelemetry__proto__trace__v1__scope_spans__free_unpacked(
    Opentelemetry__Proto__Trace__V1__ScopeSpans *message,
    ProtobufCAllocator *                         allocator)
{
    if (!message)
        return;
    assert(message->base.descriptor ==
           &opentelemetry__proto__trace__v1__scope_spans__descriptor);
    protobuf_c_message_free_unpacked((ProtobufCMessage *) message, allocator);
}
void opentelemetry__proto__trace__v1__span__event__init(
    Opentelemetry__Proto__Trace__V1__Span__Event *message)
{
    static const Opentelemetry__Proto__Trace__V1__Span__Event init_value =
        OPENTELEMETRY__PROTO__TRACE__V1__SPAN__EVENT__INIT;
    *message = init_value;
}
void opentelemetry__proto__trace__v1__span__link__init(
    Opentelemetry__Proto__Trace__V1__Span__Link *message)
{
    static const Opentelemetry__Proto__Trace__V1__Span__Link init_value =
        OPENTELEMETRY__PROTO__TRACE__V1__SPAN__LINK__INIT;
    *message = init_value;
}
void opentelemetry__proto__trace__v1__span__init(
    Opentelemetry__Proto__Trace__V1__Span *message)
{
    static const Opentelemetry__Proto__Trace__V1__Span init_value =
        OPENTELEMETRY__PROTO__TRACE__V1__SPAN__INIT;
    *message = init_value;
}
size_t opentelemetry__proto__trace__v1__span__get_packed_size(
    const Opentelemetry__Proto__Trace__V1__Span *message)
{
    assert(message->base.descriptor ==
           &opentelemetry__proto__trace__v1__span__descriptor);
    return protobuf_c_message_get_packed_size(
        (const ProtobufCMessage *) (message));
}
size_t opentelemetry__proto__trace__v1__span__pack(
    const Opentelemetry__Proto__Trace__V1__Span *message, uint8_t *out)
{
    assert(message->base.descriptor ==
           &opentelemetry__proto__trace__v1__span__descriptor);
    return protobuf_c_message_pack((const ProtobufCMessage *) message, out);
}
size_t opentelemetry__proto__trace__v1__span__pack_to_buffer(
    const Opentelemetry__Proto__Trace__V1__Span *message,
    ProtobufCBuffer *                            buffer)
{
    assert(message->base.descriptor ==
           &opentelemetry__proto__trace__v1__span__descriptor);
    return protobuf_c_message_pack_to_buffer((const ProtobufCMessage *) message,
                                             buffer);
}
Opentelemetry__Proto__Trace__V1__Span *
opentelemetry__proto__trace__v1__span__unpack(ProtobufCAllocator *allocator,
                                              size_t len, const uint8_t *data)
{
    return (Opentelemetry__Proto__Trace__V1__Span *) protobuf_c_message_unpack(
        &opentelemetry__proto__trace__v1__span__descriptor, allocator, len,
        data);
}
void opentelemetry__proto__trace__v1__span__free_unpacked(
    Opentelemetry__Proto__Trace__V1__Span *message,
    ProtobufCAllocator *                   allocator)
{
    if (!message)
        return;
    assert(message->base.descriptor ==
           &opentelemetry__proto__trace__v1__span__descriptor);
    protobuf_c_message_free_unpacked((ProtobufCMessage *) message, allocator);
}
void opentelemetry__proto__trace__v1__status__init(
    Opentelemetry__Proto__Trace__V1__Status *message)
{
    static const Opentelemetry__Proto__Trace__V1__Status init_value =
        OPENTELEMETRY__PROTO__TRACE__V1__STATUS__INIT;
    *message = init_value;
}
size_t opentelemetry__proto__trace__v1__status__get_packed_size(
    const Opentelemetry__Proto__Trace__V1__Status *message)
{
    assert(message->base.descriptor ==
           &opentelemetry__proto__trace__v1__status__descriptor);
    return protobuf_c_message_get_packed_size(
        (const ProtobufCMessage *) (message));
}
size_t opentelemetry__proto__trace__v1__status__pack(
    const Opentelemetry__Proto__Trace__V1__Status *message, uint8_t *out)
{
    assert(message->base.descriptor ==
           &opentelemetry__proto__trace__v1__status__descriptor);
    return protobuf_c_message_pack((const ProtobufCMessage *) message, out);
}
size_t opentelemetry__proto__trace__v1__status__pack_to_buffer(
    const Opentelemetry__Proto__Trace__V1__Status *message,
    ProtobufCBuffer *                              buffer)
{
    assert(message->base.descriptor ==
           &opentelemetry__proto__trace__v1__status__descriptor);
    return protobuf_c_message_pack_to_buffer((const ProtobufCMessage *) message,
                                             buffer);
}
Opentelemetry__Proto__Trace__V1__Status *
opentelemetry__proto__trace__v1__status__unpack(ProtobufCAllocator *allocator,
                                                size_t len, const uint8_t *data)
{
    return (Opentelemetry__Proto__Trace__V1__Status *)
        protobuf_c_message_unpack(
            &opentelemetry__proto__trace__v1__status__descriptor, allocator,
            len, data);
}
void opentelemetry__proto__trace__v1__status__free_unpacked(
    Opentelemetry__Proto__Trace__V1__Status *message,
    ProtobufCAllocator *                     allocator)
{
    if (!message)
        return;
    assert(message->base.descriptor ==
           &opentelemetry__proto__trace__v1__status__descriptor);
    protobuf_c_message_free_unpacked((ProtobufCMessage *) message, allocator);
}
static const ProtobufCFieldDescriptor
    opentelemetry__proto__trace__v1__traces_data__field_descriptors[1] = {
        {
            "resource_spans", 1, PROTOBUF_C_LABEL_REPEATED,
            PROTOBUF_C_TYPE_MESSAGE,
            offsetof(Opentelemetry__Proto__Trace__V1__TracesData,
                     n_resource_spans),
            offsetof(Opentelemetry__Proto__Trace__V1__TracesData,
                     resource_spans),
            &opentelemetry__proto__trace__v1__resource_spans__descriptor, NULL,
            0,            /* flags */
            0, NULL, NULL /* reserved1,reserved2, etc */
        },
    };
static const unsigned
    opentelemetry__proto__trace__v1__traces_data__field_indices_by_name[] = {
        0, /* field[0] = resource_spans */
    };
static const ProtobufCIntRange
    opentelemetry__proto__trace__v1__traces_data__number_ranges[1 + 1] = {
        { 1, 0 }, { 0, 1 }
    };
const ProtobufCMessageDescriptor
    opentelemetry__proto__trace__v1__traces_data__descriptor = {
        PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
        "opentelemetry.proto.trace.v1.TracesData",
        "TracesData",
        "Opentelemetry__Proto__Trace__V1__TracesData",
        "opentelemetry.proto.trace.v1",
        sizeof(Opentelemetry__Proto__Trace__V1__TracesData),
        1,
        opentelemetry__proto__trace__v1__traces_data__field_descriptors,
        opentelemetry__proto__trace__v1__traces_data__field_indices_by_name,
        1,
        opentelemetry__proto__trace__v1__traces_data__number_ranges,
        (ProtobufCMessageInit)
            opentelemetry__proto__trace__v1__traces_data__init,
        NULL,
        NULL,
        NULL /* reserved[123] */
    };
static const ProtobufCFieldDescriptor
    opentelemetry__proto__trace__v1__resource_spans__field_descriptors[3] = {
        {
            "resource", 1, PROTOBUF_C_LABEL_NONE, PROTOBUF_C_TYPE_MESSAGE,
            0, /* quantifier_offset */
            offsetof(Opentelemetry__Proto__Trace__V1__ResourceSpans, resource),
            &opentelemetry__proto__resource__v1__resource__descriptor, NULL,
            0,            /* flags */
            0, NULL, NULL /* reserved1,reserved2, etc */
        },
        {
            "scope_spans", 2, PROTOBUF_C_LABEL_REPEATED,
            PROTOBUF_C_TYPE_MESSAGE,
            offsetof(Opentelemetry__Proto__Trace__V1__ResourceSpans,
                     n_scope_spans),
            offsetof(Opentelemetry__Proto__Trace__V1__ResourceSpans,
                     scope_spans),
            &opentelemetry__proto__trace__v1__scope_spans__descriptor, NULL,
            0,            /* flags */
            0, NULL, NULL /* reserved1,reserved2, etc */
        },
        {
            "schema_url", 3, PROTOBUF_C_LABEL_NONE, PROTOBUF_C_TYPE_STRING,
            0, /* quantifier_offset */
            offsetof(Opentelemetry__Proto__Trace__V1__ResourceSpans,
                     schema_url),
            NULL, &protobuf_c_empty_string, 0, /* flags */
            0, NULL, NULL                      /* reserved1,reserved2, etc */
        },
    };
static const unsigned
    opentelemetry__proto__trace__v1__resource_spans__field_indices_by_name[] = {
        0, /* field[0] = resource */
        2, /* field[2] = schema_url */
        1, /* field[1] = scope_spans */
    };
static const ProtobufCIntRange
    opentelemetry__proto__trace__v1__resource_spans__number_ranges[1 + 1] = {
        { 1, 0 }, { 0, 3 }
    };
const ProtobufCMessageDescriptor
    opentelemetry__proto__trace__v1__resource_spans__descriptor = {
        PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
        "opentelemetry.proto.trace.v1.ResourceSpans",
        "ResourceSpans",
        "Opentelemetry__Proto__Trace__V1__ResourceSpans",
        "opentelemetry.proto.trace.v1",
        sizeof(Opentelemetry__Proto__Trace__V1__ResourceSpans),
        3,
        opentelemetry__proto__trace__v1__resource_spans__field_descriptors,
        opentelemetry__proto__trace__v1__resource_spans__field_indices_by_name,
        1,
        opentelemetry__proto__trace__v1__resource_spans__number_ranges,
        (ProtobufCMessageInit)
            opentelemetry__proto__trace__v1__resource_spans__init,
        NULL,
        NULL,
        NULL /* reserved[123] */
    };
static const ProtobufCFieldDescriptor
    opentelemetry__proto__trace__v1__scope_spans__field_descriptors[3] = {
        {
            "scope", 1, PROTOBUF_C_LABEL_NONE, PROTOBUF_C_TYPE_MESSAGE,
            0, /* quantifier_offset */
            offsetof(Opentelemetry__Proto__Trace__V1__ScopeSpans, scope),
            &opentelemetry__proto__common__v1__instrumentation_scope__descriptor,
            NULL, 0,      /* flags */
            0, NULL, NULL /* reserved1,reserved2, etc */
        },
        {
            "spans", 2, PROTOBUF_C_LABEL_REPEATED, PROTOBUF_C_TYPE_MESSAGE,
            offsetof(Opentelemetry__Proto__Trace__V1__ScopeSpans, n_spans),
            offsetof(Opentelemetry__Proto__Trace__V1__ScopeSpans, spans),
            &opentelemetry__proto__trace__v1__span__descriptor, NULL,
            0,            /* flags */
            0, NULL, NULL /* reserved1,reserved2, etc */
        },
        {
            "schema_url", 3, PROTOBUF_C_LABEL_NONE, PROTOBUF_C_TYPE_STRING,
            0, /* quantifier_offset */
            offsetof(Opentelemetry__Proto__Trace__V1__ScopeSpans, schema_url),
            NULL, &protobuf_c_empty_string, 0, /* flags */
            0, NULL, NULL                      /* reserved1,reserved2, etc */
        },
    };
static const unsigned
    opentelemetry__proto__trace__v1__scope_spans__field_indices_by_name[] = {
        2, /* field[2] = schema_url */
        0, /* field[0] = scope */
        1, /* field[1] = spans */
    };
static const ProtobufCIntRange
    opentelemetry__proto__trace__v1__scope_spans__number_ranges[1 + 1] = {
        { 1, 0 }, { 0, 3 }
    };
const ProtobufCMessageDescriptor
    opentelemetry__proto__trace__v1__scope_spans__descriptor = {
        PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
        "opentelemetry.proto.trace.v1.ScopeSpans",
        "ScopeSpans",
        "Opentelemetry__Proto__Trace__V1__ScopeSpans",
        "opentelemetry.proto.trace.v1",
        sizeof(Opentelemetry__Proto__Trace__V1__ScopeSpans),
        3,
        opentelemetry__proto__trace__v1__scope_spans__field_descriptors,
        opentelemetry__proto__trace__v1__scope_spans__field_indices_by_name,
        1,
        opentelemetry__proto__trace__v1__scope_spans__number_ranges,
        (ProtobufCMessageInit)
            opentelemetry__proto__trace__v1__scope_spans__init,
        NULL,
        NULL,
        NULL /* reserved[123] */
    };
static const ProtobufCFieldDescriptor
    opentelemetry__proto__trace__v1__span__event__field_descriptors[4] = {
        {
            "time_unix_nano", 1, PROTOBUF_C_LABEL_NONE, PROTOBUF_C_TYPE_FIXED64,
            0, /* quantifier_offset */
            offsetof(Opentelemetry__Proto__Trace__V1__Span__Event,
                     time_unix_nano),
            NULL, NULL, 0, /* flags */
            0, NULL, NULL  /* reserved1,reserved2, etc */
        },
        {
            "name", 2, PROTOBUF_C_LABEL_NONE, PROTOBUF_C_TYPE_STRING,
            0, /* quantifier_offset */
            offsetof(Opentelemetry__Proto__Trace__V1__Span__Event, name), NULL,
            &protobuf_c_empty_string, 0, /* flags */
            0, NULL, NULL                /* reserved1,reserved2, etc */
        },
        {
            "attributes", 3, PROTOBUF_C_LABEL_REPEATED, PROTOBUF_C_TYPE_MESSAGE,
            offsetof(Opentelemetry__Proto__Trace__V1__Span__Event,
                     n_attributes),
            offsetof(Opentelemetry__Proto__Trace__V1__Span__Event, attributes),
            &opentelemetry__proto__common__v1__key_value__descriptor, NULL,
            0,            /* flags */
            0, NULL, NULL /* reserved1,reserved2, etc */
        },
        {
            "dropped_attributes_count", 4, PROTOBUF_C_LABEL_NONE,
            PROTOBUF_C_TYPE_UINT32, 0, /* quantifier_offset */
            offsetof(Opentelemetry__Proto__Trace__V1__Span__Event,
                     dropped_attributes_count),
            NULL, NULL, 0, /* flags */
            0, NULL, NULL  /* reserved1,reserved2, etc */
        },
    };
static const unsigned
    opentelemetry__proto__trace__v1__span__event__field_indices_by_name[] = {
        2, /* field[2] = attributes */
        3, /* field[3] = dropped_attributes_count */
        1, /* field[1] = name */
        0, /* field[0] = time_unix_nano */
    };
static const ProtobufCIntRange
    opentelemetry__proto__trace__v1__span__event__number_ranges[1 + 1] = {
        { 1, 0 }, { 0, 4 }
    };
const ProtobufCMessageDescriptor
    opentelemetry__proto__trace__v1__span__event__descriptor = {
        PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
        "opentelemetry.proto.trace.v1.Span.Event",
        "Event",
        "Opentelemetry__Proto__Trace__V1__Span__Event",
        "opentelemetry.proto.trace.v1",
        sizeof(Opentelemetry__Proto__Trace__V1__Span__Event),
        4,
        opentelemetry__proto__trace__v1__span__event__field_descriptors,
        opentelemetry__proto__trace__v1__span__event__field_indices_by_name,
        1,
        opentelemetry__proto__trace__v1__span__event__number_ranges,
        (ProtobufCMessageInit)
            opentelemetry__proto__trace__v1__span__event__init,
        NULL,
        NULL,
        NULL /* reserved[123] */
    };
static const ProtobufCFieldDescriptor
    opentelemetry__proto__trace__v1__span__link__field_descriptors[6] = {
        {
            "trace_id", 1, PROTOBUF_C_LABEL_NONE, PROTOBUF_C_TYPE_BYTES,
            0, /* quantifier_offset */
            offsetof(Opentelemetry__Proto__Trace__V1__Span__Link, trace_id),
            NULL, NULL, 0, /* flags */
            0, NULL, NULL  /* reserved1,reserved2, etc */
        },
        {
            "span_id", 2, PROTOBUF_C_LABEL_NONE, PROTOBUF_C_TYPE_BYTES,
            0, /* quantifier_offset */
            offsetof(Opentelemetry__Proto__Trace__V1__Span__Link, span_id),
            NULL, NULL, 0, /* flags */
            0, NULL, NULL  /* reserved1,reserved2, etc */
        },
        {
            "trace_state", 3, PROTOBUF_C_LABEL_NONE, PROTOBUF_C_TYPE_STRING,
            0, /* quantifier_offset */
            offsetof(Opentelemetry__Proto__Trace__V1__Span__Link, trace_state),
            NULL, &protobuf_c_empty_string, 0, /* flags */
            0, NULL, NULL                      /* reserved1,reserved2, etc */
        },
        {
            "attributes", 4, PROTOBUF_C_LABEL_REPEATED, PROTOBUF_C_TYPE_MESSAGE,
            offsetof(Opentelemetry__Proto__Trace__V1__Span__Link, n_attributes),
            offsetof(Opentelemetry__Proto__Trace__V1__Span__Link, attributes),
            &opentelemetry__proto__common__v1__key_value__descriptor, NULL,
            0,            /* flags */
            0, NULL, NULL /* reserved1,reserved2, etc */
        },
        {
            "dropped_attributes_count", 5, PROTOBUF_C_LABEL_NONE,
            PROTOBUF_C_TYPE_UINT32, 0, /* quantifier_offset */
            offsetof(Opentelemetry__Proto__Trace__V1__Span__Link,
                     dropped_attributes_count),
            NULL, NULL, 0, /* flags */
            0, NULL, NULL  /* reserved1,reserved2, etc */
        },
        {
            "flags", 6, PROTOBUF_C_LABEL_NONE, PROTOBUF_C_TYPE_FIXED32,
            0, /* quantifier_offset */
            offsetof(Opentelemetry__Proto__Trace__V1__Span__Link, flags), NULL,
            NULL, 0,      /* flags */
            0, NULL, NULL /* reserved1,reserved2, etc */
        },
    };
static const unsigned
    opentelemetry__proto__trace__v1__span__link__field_indices_by_name[] = {
        3, /* field[3] = attributes */
        4, /* field[4] = dropped_attributes_count */
        5, /* field[5] = flags */
        1, /* field[1] = span_id */
        0, /* field[0] = trace_id */
        2, /* field[2] = trace_state */
    };
static const ProtobufCIntRange
    opentelemetry__proto__trace__v1__span__link__number_ranges[1 + 1] = {
        { 1, 0 }, { 0, 6 }
    };
const ProtobufCMessageDescriptor
    opentelemetry__proto__trace__v1__span__link__descriptor = {
        PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
        "opentelemetry.proto.trace.v1.Span.Link",
        "Link",
        "Opentelemetry__Proto__Trace__V1__Span__Link",
        "opentelemetry.proto.trace.v1",
        sizeof(Opentelemetry__Proto__Trace__V1__Span__Link),
        6,
        opentelemetry__proto__trace__v1__span__link__field_descriptors,
        opentelemetry__proto__trace__v1__span__link__field_indices_by_name,
        1,
        opentelemetry__proto__trace__v1__span__link__number_ranges,
        (ProtobufCMessageInit)
            opentelemetry__proto__trace__v1__span__link__init,
        NULL,
        NULL,
        NULL /* reserved[123] */
    };
static const ProtobufCEnumValue
    opentelemetry__proto__trace__v1__span__span_kind__enum_values_by_number
        [6] = {
            { "SPAN_KIND_UNSPECIFIED",
              "OPENTELEMETRY__PROTO__TRACE__V1__SPAN__SPAN_KIND__SPAN_KIND_"
              "UNSPECIFIED",
              0 },
            { "SPAN_KIND_INTERNAL",
              "OPENTELEMETRY__PROTO__TRACE__V1__SPAN__SPAN_KIND__SPAN_KIND_"
              "INTERNAL",
              1 },
            { "SPAN_KIND_SERVER",
              "OPENTELEMETRY__PROTO__TRACE__V1__SPAN__SPAN_KIND__SPAN_KIND_"
              "SERVER",
              2 },
            { "SPAN_KIND_CLIENT",
              "OPENTELEMETRY__PROTO__TRACE__V1__SPAN__SPAN_KIND__SPAN_KIND_"
              "CLIENT",
              3 },
            { "SPAN_KIND_PRODUCER",
              "OPENTELEMETRY__PROTO__TRACE__V1__SPAN__SPAN_KIND__SPAN_KIND_"
              "PRODUCER",
              4 },
            { "SPAN_KIND_CONSUMER",
              "OPENTELEMETRY__PROTO__TRACE__V1__SPAN__SPAN_KIND__SPAN_KIND_"
              "CONSUMER",
              5 },
        };
static const ProtobufCIntRange
    opentelemetry__proto__trace__v1__span__span_kind__value_ranges[] = {
        { 0, 0 }, { 0, 6 }
    };
static const ProtobufCEnumValueIndex
    opentelemetry__proto__trace__v1__span__span_kind__enum_values_by_name[6] = {
        { "SPAN_KIND_CLIENT", 3 },   { "SPAN_KIND_CONSUMER", 5 },
        { "SPAN_KIND_INTERNAL", 1 }, { "SPAN_KIND_PRODUCER", 4 },
        { "SPAN_KIND_SERVER", 2 },   { "SPAN_KIND_UNSPECIFIED", 0 },
    };
const ProtobufCEnumDescriptor
    opentelemetry__proto__trace__v1__span__span_kind__descriptor = {
        PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
        "opentelemetry.proto.trace.v1.Span.SpanKind",
        "SpanKind",
        "Opentelemetry__Proto__Trace__V1__Span__SpanKind",
        "opentelemetry.proto.trace.v1",
        6,
        opentelemetry__proto__trace__v1__span__span_kind__enum_values_by_number,
        6,
        opentelemetry__proto__trace__v1__span__span_kind__enum_values_by_name,
        1,
        opentelemetry__proto__trace__v1__span__span_kind__value_ranges,
        NULL,
        NULL,
        NULL,
        NULL /* reserved[1234] */
    };
static const ProtobufCFieldDescriptor
    opentelemetry__proto__trace__v1__span__field_descriptors[16] = {
        {
            "trace_id", 1, PROTOBUF_C_LABEL_NONE, PROTOBUF_C_TYPE_BYTES,
            0, /* quantifier_offset */
            offsetof(Opentelemetry__Proto__Trace__V1__Span, trace_id), NULL,
            NULL, 0,      /* flags */
            0, NULL, NULL /* reserved1,reserved2, etc */
        },
        {
            "span_id", 2, PROTOBUF_C_LABEL_NONE, PROTOBUF_C_TYPE_BYTES,
            0, /* quantifier_offset */
            offsetof(Opentelemetry__Proto__Trace__V1__Span, span_id), NULL,
            NULL, 0,      /* flags */
            0, NULL, NULL /* reserved1,reserved2, etc */
        },
        {
            "trace_state", 3, PROTOBUF_C_LABEL_NONE, PROTOBUF_C_TYPE_STRING,
            0, /* quantifier_offset */
            offsetof(Opentelemetry__Proto__Trace__V1__Span, trace_state), NULL,
            &protobuf_c_empty_string, 0, /* flags */
            0, NULL, NULL                /* reserved1,reserved2, etc */
        },
        {
            "parent_span_id", 4, PROTOBUF_C_LABEL_NONE, PROTOBUF_C_TYPE_BYTES,
            0, /* quantifier_offset */
            offsetof(Opentelemetry__Proto__Trace__V1__Span, parent_span_id),
            NULL, NULL, 0, /* flags */
            0, NULL, NULL  /* reserved1,reserved2, etc */
        },
        {
            "name", 5, PROTOBUF_C_LABEL_NONE, PROTOBUF_C_TYPE_STRING,
            0, /* quantifier_offset */
            offsetof(Opentelemetry__Proto__Trace__V1__Span, name), NULL,
            &protobuf_c_empty_string, 0, /* flags */
            0, NULL, NULL                /* reserved1,reserved2, etc */
        },
        {
            "kind", 6, PROTOBUF_C_LABEL_NONE, PROTOBUF_C_TYPE_ENUM,
            0, /* quantifier_offset */
            offsetof(Opentelemetry__Proto__Trace__V1__Span, kind),
            &opentelemetry__proto__trace__v1__span__span_kind__descriptor, NULL,
            0,            /* flags */
            0, NULL, NULL /* reserved1,reserved2, etc */
        },
        {
            "start_time_unix_nano", 7, PROTOBUF_C_LABEL_NONE,
            PROTOBUF_C_TYPE_FIXED64, 0, /* quantifier_offset */
            offsetof(Opentelemetry__Proto__Trace__V1__Span,
                     start_time_unix_nano),
            NULL, NULL, 0, /* flags */
            0, NULL, NULL  /* reserved1,reserved2, etc */
        },
        {
            "end_time_unix_nano", 8, PROTOBUF_C_LABEL_NONE,
            PROTOBUF_C_TYPE_FIXED64, 0, /* quantifier_offset */
            offsetof(Opentelemetry__Proto__Trace__V1__Span, end_time_unix_nano),
            NULL, NULL, 0, /* flags */
            0, NULL, NULL  /* reserved1,reserved2, etc */
        },
        {
            "attributes", 9, PROTOBUF_C_LABEL_REPEATED, PROTOBUF_C_TYPE_MESSAGE,
            offsetof(Opentelemetry__Proto__Trace__V1__Span, n_attributes),
            offsetof(Opentelemetry__Proto__Trace__V1__Span, attributes),
            &opentelemetry__proto__common__v1__key_value__descriptor, NULL,
            0,            /* flags */
            0, NULL, NULL /* reserved1,reserved2, etc */
        },
        {
            "dropped_attributes_count", 10, PROTOBUF_C_LABEL_NONE,
            PROTOBUF_C_TYPE_UINT32, 0, /* quantifier_offset */
            offsetof(Opentelemetry__Proto__Trace__V1__Span,
                     dropped_attributes_count),
            NULL, NULL, 0, /* flags */
            0, NULL, NULL  /* reserved1,reserved2, etc */
        },
        {
            "events", 11, PROTOBUF_C_LABEL_REPEATED, PROTOBUF_C_TYPE_MESSAGE,
            offsetof(Opentelemetry__Proto__Trace__V1__Span, n_events),
            offsetof(Opentelemetry__Proto__Trace__V1__Span, events),
            &opentelemetry__proto__trace__v1__span__event__descriptor, NULL,
            0,            /* flags */
            0, NULL, NULL /* reserved1,reserved2, etc */
        },
        {
            "dropped_events_count", 12, PROTOBUF_C_LABEL_NONE,
            PROTOBUF_C_TYPE_UINT32, 0, /* quantifier_offset */
            offsetof(Opentelemetry__Proto__Trace__V1__Span,
                     dropped_events_count),
            NULL, NULL, 0, /* flags */
            0, NULL, NULL  /* reserved1,reserved2, etc */
        },
        {
            "links", 13, PROTOBUF_C_LABEL_REPEATED, PROTOBUF_C_TYPE_MESSAGE,
            offsetof(Opentelemetry__Proto__Trace__V1__Span, n_links),
            offsetof(Opentelemetry__Proto__Trace__V1__Span, links),
            &opentelemetry__proto__trace__v1__span__link__descriptor, NULL,
            0,            /* flags */
            0, NULL, NULL /* reserved1,reserved2, etc */
        },
        {
            "dropped_links_count", 14, PROTOBUF_C_LABEL_NONE,
            PROTOBUF_C_TYPE_UINT32, 0, /* quantifier_offset */
            offsetof(Opentelemetry__Proto__Trace__V1__Span,
                     dropped_links_count),
            NULL, NULL, 0, /* flags */
            0, NULL, NULL  /* reserved1,reserved2, etc */
        },
        {
            "status", 15, PROTOBUF_C_LABEL_NONE, PROTOBUF_C_TYPE_MESSAGE,
            0, /* quantifier_offset */
            offsetof(Opentelemetry__Proto__Trace__V1__Span, status),
            &opentelemetry__proto__trace__v1__status__descriptor, NULL,
            0,            /* flags */
            0, NULL, NULL /* reserved1,reserved2, etc */
        },
        {
            "flags", 16, PROTOBUF_C_LABEL_NONE, PROTOBUF_C_TYPE_FIXED32,
            0, /* quantifier_offset */
            offsetof(Opentelemetry__Proto__Trace__V1__Span, flags), NULL, NULL,
            0,            /* flags */
            0, NULL, NULL /* reserved1,reserved2, etc */
        },
    };
static const unsigned
    opentelemetry__proto__trace__v1__span__field_indices_by_name[] = {
        8,  /* field[8] = attributes */
        9,  /* field[9] = dropped_attributes_count */
        11, /* field[11] = dropped_events_count */
        13, /* field[13] = dropped_links_count */
        7,  /* field[7] = end_time_unix_nano */
        10, /* field[10] = events */
        15, /* field[15] = flags */
        5,  /* field[5] = kind */
        12, /* field[12] = links */
        4,  /* field[4] = name */
        3,  /* field[3] = parent_span_id */
        1,  /* field[1] = span_id */
        6,  /* field[6] = start_time_unix_nano */
        14, /* field[14] = status */
        0,  /* field[0] = trace_id */
        2,  /* field[2] = trace_state */
    };
static const ProtobufCIntRange
    opentelemetry__proto__trace__v1__span__number_ranges[1 + 1] = { { 1, 0 },
                                                                    { 0, 16 } };
const ProtobufCMessageDescriptor
    opentelemetry__proto__trace__v1__span__descriptor = {
        PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
        "opentelemetry.proto.trace.v1.Span",
        "Span",
        "Opentelemetry__Proto__Trace__V1__Span",
        "opentelemetry.proto.trace.v1",
        sizeof(Opentelemetry__Proto__Trace__V1__Span),
        16,
        opentelemetry__proto__trace__v1__span__field_descriptors,
        opentelemetry__proto__trace__v1__span__field_indices_by_name,
        1,
        opentelemetry__proto__trace__v1__span__number_ranges,
        (ProtobufCMessageInit) opentelemetry__proto__trace__v1__span__init,
        NULL,
        NULL,
        NULL /* reserved[123] */
    };
static const ProtobufCEnumValue
    opentelemetry__proto__trace__v1__status__status_code__enum_values_by_number
        [3] = {
            { "STATUS_CODE_UNSET",
              "OPENTELEMETRY__PROTO__TRACE__V1__STATUS__STATUS_CODE__STATUS_"
              "CODE_UNSET",
              0 },
            { "STATUS_CODE_OK",
              "OPENTELEMETRY__PROTO__TRACE__V1__STATUS__STATUS_CODE__STATUS_"
              "CODE_OK",
              1 },
            { "STATUS_CODE_ERROR",
              "OPENTELEMETRY__PROTO__TRACE__V1__STATUS__STATUS_CODE__STATUS_"
              "CODE_ERROR",
              2 },
        };
static const ProtobufCIntRange
    opentelemetry__proto__trace__v1__status__status_code__value_ranges[] = {
        { 0, 0 }, { 0, 3 }
    };
static const ProtobufCEnumValueIndex
    opentelemetry__proto__trace__v1__status__status_code__enum_values_by_name
        [3] = {
            { "STATUS_CODE_ERROR", 2 },
            { "STATUS_CODE_OK", 1 },
            { "STATUS_CODE_UNSET", 0 },
        };
const ProtobufCEnumDescriptor
    opentelemetry__proto__trace__v1__status__status_code__descriptor = {
        PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
        "opentelemetry.proto.trace.v1.Status.StatusCode",
        "StatusCode",
        "Opentelemetry__Proto__Trace__V1__Status__StatusCode",
        "opentelemetry.proto.trace.v1",
        3,
        opentelemetry__proto__trace__v1__status__status_code__enum_values_by_number,
        3,
        opentelemetry__proto__trace__v1__status__status_code__enum_values_by_name,
        1,
        opentelemetry__proto__trace__v1__status__status_code__value_ranges,
        NULL,
        NULL,
        NULL,
        NULL /* reserved[1234] */
    };
static const ProtobufCFieldDescriptor
    opentelemetry__proto__trace__v1__status__field_descriptors[2] = {
        {
            "message", 2, PROTOBUF_C_LABEL_NONE, PROTOBUF_C_TYPE_STRING,
            0, /* quantifier_offset */
            offsetof(Opentelemetry__Proto__Trace__V1__Status, message), NULL,
            &protobuf_c_empty_string, 0, /* flags */
            0, NULL, NULL                /* reserved1,reserved2, etc */
        },
        {
            "code", 3, PROTOBUF_C_LABEL_NONE, PROTOBUF_C_TYPE_ENUM,
            0, /* quantifier_offset */
            offsetof(Opentelemetry__Proto__Trace__V1__Status, code),
            &opentelemetry__proto__trace__v1__status__status_code__descriptor,
            NULL, 0,      /* flags */
            0, NULL, NULL /* reserved1,reserved2, etc */
        },
    };
static const unsigned
    opentelemetry__proto__trace__v1__status__field_indices_by_name[] = {
        1, /* field[1] = code */
        0, /* field[0] = message */
    };
static const ProtobufCIntRange
    opentelemetry__proto__trace__v1__status__number_ranges[1 + 1] = {
        { 2, 0 }, { 0, 2 }
    };
const ProtobufCMessageDescriptor
    opentelemetry__proto__trace__v1__status__descriptor = {
        PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
        "opentelemetry.proto.trace.v1.Status",
        "Status",
        "Opentelemetry__Proto__Trace__V1__Status",
        "opentelemetry.proto.trace.v1",
        sizeof(Opentelemetry__Proto__Trace__V1__Status),
        2,
        opentelemetry__proto__trace__v1__status__field_descriptors,
        opentelemetry__proto__trace__v1__status__field_indices_by_name,
        1,
        opentelemetry__proto__trace__v1__status__number_ranges,
        (ProtobufCMessageInit) opentelemetry__proto__trace__v1__status__init,
        NULL,
        NULL,
        NULL /* reserved[123] */
    };
static const ProtobufCEnumValue
    opentelemetry__proto__trace__v1__span_flags__enum_values_by_number[4] = {
        { "SPAN_FLAGS_DO_NOT_USE",
          "OPENTELEMETRY__PROTO__TRACE__V1__SPAN_FLAGS__SPAN_FLAGS_DO_NOT_USE",
          0 },
        { "SPAN_FLAGS_TRACE_FLAGS_MASK",
          "OPENTELEMETRY__PROTO__TRACE__V1__SPAN_FLAGS__SPAN_FLAGS_TRACE_FLAGS_"
          "MASK",
          255 },
        { "SPAN_FLAGS_CONTEXT_HAS_IS_REMOTE_MASK",
          "OPENTELEMETRY__PROTO__TRACE__V1__SPAN_FLAGS__SPAN_FLAGS_CONTEXT_HAS_"
          "IS_REMOTE_MASK",
          256 },
        { "SPAN_FLAGS_CONTEXT_IS_REMOTE_MASK",
          "OPENTELEMETRY__PROTO__TRACE__V1__SPAN_FLAGS__SPAN_FLAGS_CONTEXT_IS_"
          "REMOTE_MASK",
          512 },
    };
static const ProtobufCIntRange
    opentelemetry__proto__trace__v1__span_flags__value_ranges[] = {
        { 0, 0 }, { 255, 1 }, { 512, 3 }, { 0, 4 }
    };
static const ProtobufCEnumValueIndex
    opentelemetry__proto__trace__v1__span_flags__enum_values_by_name[4] = {
        { "SPAN_FLAGS_CONTEXT_HAS_IS_REMOTE_MASK", 2 },
        { "SPAN_FLAGS_CONTEXT_IS_REMOTE_MASK", 3 },
        { "SPAN_FLAGS_DO_NOT_USE", 0 },
        { "SPAN_FLAGS_TRACE_FLAGS_MASK", 1 },
    };
const ProtobufCEnumDescriptor
    opentelemetry__proto__trace__v1__span_flags__descriptor = {
        PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
        "opentelemetry.proto.trace.v1.SpanFlags",
        "SpanFlags",
        "Opentelemetry__Proto__Trace__V1__SpanFlags",
        "opentelemetry.proto.trace.v1",
        4,
        opentelemetry__proto__trace__v1__span_flags__enum_values_by_number,
        4,
        opentelemetry__proto__trace__v1__span_flags__enum_values_by_name,
        3,
        opentelemetry__proto__trace__v1__span_flags__value_ranges,
        NULL,
        NULL,
        NULL,
        NULL /* reserved[1234] */
    };
