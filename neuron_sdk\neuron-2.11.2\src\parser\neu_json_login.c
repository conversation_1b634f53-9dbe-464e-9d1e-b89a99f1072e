/**
 * NEURON IIoT System for Industry 4.0
 * Copyright (C) 2020-2022 EMQ Technologies Co., Ltd All rights reserved.
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 3 of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with this program; if not, write to the Free Software Foundation,
 * Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.
 **/

/*
 * DO NOT EDIT THIS FILE MANUALLY!
 * It was automatically generated by `json-autotype`.
 */

#include <stdlib.h>
#include <string.h>

#include "utils/utarray.h"
#include "utils/utextend.h"
#include "json/json.h"

#include "neu_json_login.h"

int neu_json_decode_login_req(char *buf, neu_json_login_req_t **result)
{
    int ret = 0;

    neu_json_login_req_t *req         = calloc(1, sizeof(neu_json_login_req_t));
    neu_json_elem_t       req_elems[] = { {
                                        .name = "pass",
                                        .t    = NEU_JSON_STR,
                                    },
                                    {
                                        .name = "name",
                                        .t    = NEU_JSON_STR,
                                    } };
    ret = neu_json_decode(buf, NEU_JSON_ELEM_SIZE(req_elems), req_elems);
    if (ret != 0) {
        goto decode_fail;
    }

    req->pass = req_elems[0].v.val_str;
    req->name = req_elems[1].v.val_str;

    *result = req;
    return ret;

decode_fail:
    free(req);
    return -1;
}

void neu_json_decode_login_req_free(neu_json_login_req_t *req)
{

    free(req->pass);
    free(req->name);

    free(req);
}

int neu_json_decode_password_req(char *buf, neu_json_password_req_t **result)
{
    int ret = 0;

    neu_json_password_req_t *req = calloc(1, sizeof(neu_json_password_req_t));
    neu_json_elem_t          req_elems[] = {
        {
            .name = "name",
            .t    = NEU_JSON_STR,
        },
        {
            .name = "old_pass",
            .t    = NEU_JSON_STR,
        },
        {
            .name = "new_pass",
            .t    = NEU_JSON_STR,
        },
    };
    ret = neu_json_decode(buf, NEU_JSON_ELEM_SIZE(req_elems), req_elems);
    if (ret != 0) {
        goto decode_fail;
    }

    req->name     = req_elems[0].v.val_str;
    req->old_pass = req_elems[1].v.val_str;
    req->new_pass = req_elems[2].v.val_str;

    *result = req;
    return ret;

decode_fail:
    free(req);
    return -1;
}

void neu_json_decode_password_req_free(neu_json_password_req_t *req)
{
    if (req) {
        free(req->name);
        free(req->old_pass);
        free(req->new_pass);

        free(req);
    }
}

int neu_json_encode_login_resp(void *json_object, void *param)
{
    int                    ret  = 0;
    neu_json_login_resp_t *resp = (neu_json_login_resp_t *) param;

    neu_json_elem_t resp_elems[] = { {
        .name      = "token",
        .t         = NEU_JSON_STR,
        .v.val_str = resp->token,
    } };
    ret = neu_json_encode_field(json_object, resp_elems,
                                NEU_JSON_ELEM_SIZE(resp_elems));

    return ret;
}

int neu_json_decode_add_user_req(char *buf, neu_json_add_user_req_t **result)
{
    int ret = 0;

    neu_json_add_user_req_t *req = calloc(1, sizeof(neu_json_add_user_req_t));
    neu_json_elem_t          req_elems[] = {
        {
            .name = "name",
            .t    = NEU_JSON_STR,
        },
        {
            .name = "password",
            .t    = NEU_JSON_STR,
        },

    };
    ret = neu_json_decode(buf, NEU_JSON_ELEM_SIZE(req_elems), req_elems);
    if (ret != 0) {
        goto decode_fail;
    }

    req->name = req_elems[0].v.val_str;
    req->pass = req_elems[1].v.val_str;

    *result = req;
    return ret;

decode_fail:
    free(req);
    return -1;
}

void neu_json_decode_add_user_req_free(neu_json_add_user_req_t *req)
{
    if (req) {
        free(req->name);
        free(req->pass);
        free(req);
    }
}

int neu_json_decode_update_user_req(char *buf, neu_json_password_req_t **result)
{
    int                      ret = 0;
    neu_json_password_req_t *req = calloc(1, sizeof(neu_json_password_req_t));
    neu_json_elem_t          req_elems[] = {
        {
            .name = "name",
            .t    = NEU_JSON_STR,
        },
        {
            .name = "new_password",
            .t    = NEU_JSON_STR,
        },
    };
    ret = neu_json_decode(buf, NEU_JSON_ELEM_SIZE(req_elems), req_elems);
    if (ret != 0) {
        goto decode_fail;
    }

    req->name     = req_elems[0].v.val_str;
    req->new_pass = req_elems[1].v.val_str;

    *result = req;
    return ret;

decode_fail:
    free(req);
    return -1;
}

void neu_json_decode_update_user_req_free(neu_json_password_req_t *req)
{
    if (req) {
        free(req->name);
        free(req->new_pass);
        free(req);
    }
}

int neu_json_decode_delete_user_req(char *                       buf,
                                    neu_json_delete_user_req_t **result)
{
    int                         ret = 0;
    neu_json_delete_user_req_t *req =
        calloc(1, sizeof(neu_json_delete_user_req_t));
    neu_json_elem_t req_elems[] = {
        {
            .name = "name",
            .t    = NEU_JSON_STR,
        },
    };
    ret = neu_json_decode(buf, NEU_JSON_ELEM_SIZE(req_elems), req_elems);
    if (ret != 0) {
        goto decode_fail;
    }

    req->name = req_elems[0].v.val_str;

    *result = req;
    return ret;

decode_fail:
    free(req);
    return -1;
}

void neu_json_decode_delete_user_req_free(neu_json_delete_user_req_t *req)
{
    if (req) {
        free(req->name);
        free(req);
    }
}

int neu_json_encode_user_list_resp(void *json_object, void *param)
{
    int       ret        = 0;
    UT_array *user_list  = (UT_array *) param;
    void *    user_array = neu_json_array();

    if (NULL != user_list) {
        utarray_foreach(user_list, neu_json_user_resp_t *, p_user)
        {
            if (NULL != p_user) {
                neu_json_elem_t user_elems[] = {
                    {
                        .name      = "name",
                        .t         = NEU_JSON_STR,
                        .v.val_str = p_user->name,
                    },
                };

                neu_json_encode_array(user_array, user_elems,
                                      NEU_JSON_ELEM_SIZE(user_elems));
            }
        }
    }

    neu_json_elem_t resp_elems[] = { {
        .name         = "users",
        .t            = NEU_JSON_OBJECT,
        .v.val_object = user_array,
    } };

    ret = neu_json_encode_field(json_object, resp_elems,
                                NEU_JSON_ELEM_SIZE(resp_elems));

    return ret;
}
