
openssl
$ echo "Installing openssl (1.1.1)"
$ git clone -b OpenSSL_1_1_1 https://github.com/openssl/openssl.git         
$ cd openssl         
$ sudo mkdir -p /opt/externs/libs/arm-linux-gnueabihf/openssl/ssl           
$ ./Configure linux-generic32 no-asm shared --prefix=/opt/externs/libs/arm-linux-gnueabihf --openssldir=/opt/externs/libs/arm-linux-gnueabihf/openssl/ssl --cross-compile-prefix=arm-none-linux-gnueabihf-       
$ make clean         
$ make      
$ sudo make install_sw         
$ make clean  


jansson
cmake .. -DCMAKE_C_COMPILER=arm-none-linux-gnueabihf-gcc -DCMAKE_CXX_COMPILER=arm-none-linux-gnueabihf-g++ -DCMAKE_STAGING_PREFIX=/opt/externs/libs/arm-linux-gnueabihf -DCMAKE_PREFIX_PATH=/opt/externs/libs/arm-linux-gnueabihf -DJANSSON_BUILD_DOCS=OFF -DJANSSON_EXAMPLES=OFF

NanoSDK
cmake .. -DCMAKE_C_COMPILER=arm-none-linux-gnueabihf-gcc -DCMAKE_CXX_COMPILER=arm-none-linux-gnueabihf-g++ -DCMAKE_STAGING_PREFIX=/opt/externs/libs/arm-linux-gnueabihf -DCMAKE_PREFIX_PATH=/opt/externs/libs/arm-linux-gnueabihf -DBUILD_SHARED_LIBS=OFF -DNNG_TESTS=OFF -DNNG_ENABLE_SQLITE=ON -DNNG_ENABLE_TLS=ON -DCMAKE_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu -DMBEDTLS_INCLUDE_DIRS=/opt/externs/libs/arm-linux-gnueabihf/include/mbedtls -DBUILD_SUPPLEMENTAL=ON


mebedTLS
cmake .. -DCMAKE_C_COMPILER=arm-none-linux-gnueabihf-gcc -DCMAKE_CXX_COMPILER=arm-none-linux-gnueabihf-g++ -DCMAKE_STAGING_PREFIX=/opt/externs/libs/arm-linux-gnueabihf -DCMAKE_PREFIX_PATH=/opt/externs/libs/arm-linux-gnueabihf -DBUILD_SHARED_LIBS=OFF  -DUSE_SHARED_MBEDTLS_LIBRARY=OFF -DENABLE_TESTING=OFF -DCMAKE_POSITION_INDEPENDENT_CODE=ON



jwt
cmake .. -DCMAKE_C_COMPILER=arm-none-linux-gnueabihf-gcc -DCMAKE_CXX_COMPILER=arm-none-linux-gnueabihf-g++ -DCMAKE_STAGING_PREFIX=/opt/externs/libs/arm-linux-gnueabihf -DCMAKE_PREFIX_PATH=/opt/externs/libs/arm-linux-gnueabihf -DENABLE_PIC=ON -DBUILD_SHARED_LIBS=OFF

googletest
cmake .. -DCMAKE_C_COMPILER=arm-none-linux-gnueabihf-gcc -DCMAKE_CXX_COMPILER=arm-none-linux-gnueabihf-g++ -DCMAKE_STAGING_PREFIX=/opt/externs/libs/arm-linux-gnueabihf -DCMAKE_PREFIX_PATH=/opt/externs/libs/arm-linux-gnueabihf -DBUILD_SHARED_LIBS=OFF

sqlite3
./configure --prefix=/opt/externs/libs/arm-linux-gnueabihf --disable-shared --disable-readline --host armv7 CC=arm-none-linux-gnueabihf-gcc


libxml2
cmake .. -DCMAKE_C_COMPILER=arm-none-linux-gnueabihf-gcc -DCMAKE_CXX_COMPILER=arm-none-linux-gnueabihf-g++ -DCMAKE_STAGING_PREFIX=/opt/externs/libs/arm-linux-gnueabihf -DCMAKE_PREFIX_PATH=/opt/externs/libs/arm-linux-gnueabihf  -DBUILD_SHARED_LIBS=OFF -DCMAKE_BUILD_TYPE=Release -DLIBXML2_WITH_ICONV=OFF -DLIBXML2_WITH_LZMA=OFF -DLIBXML2_WITH_PYTHON=OFF -DLIBXML2_WITH_ZLIB=OFF -DLIBXML2_WITH_HTTP=OFF -DCMAKE_POSITION_INDEPENDENT_CODE=ON


protobuf
./configure --prefix=/opt/externs/libs/arm-linux-gnueabihf --enable-shared=no --host=armv7 CFLAGS=-fPIC CXXFLAGS=-fPIC CC=arm-none-linux-gnueabihf-gcc CXX=arm-none-linux-gnueabihf-g++


protobuf-c
./configure --prefix=/opt/externs/libs/arm-linux-gnueabihf --disable-protoc --enable-shared=no --host=armv4 CFLAGS=-fPIC CXXFLAGS=-fPIC CC=arm-none-linux-gnueabihf-gcc




Arrow 
git clone https://github.com/apache/arrow.git
cd arrow/cpp
mkdir build && cd build

#
cmake .. -DCMAKE_TOOLCHAIN_FILE=./arm-none-linux-gnueabihf.cmake -DCMAKE_C_COMPILER=arm-none-linux-gnueabihf-gcc -DCMAKE_CXX_COMPILER=arm-none-linux-gnueabihf-g++ -DCMAKE_INSTALL_PREFIX=/opt/externs/libs/arm-linux-gnueabihf -DARROW_BUILD_STATIC=ON -DARROW_BUILD_SHARED=OFF -DARROW_SIMD_LEVEL=NONE -DARROW_FLIGHT=ON  -DARROW_JEMALLOC=OFF -DARROW_FLIGHT_SQL=ON

cmake .. -DCMAKE_C_COMPILER=arm-none-linux-gnueabihf-gcc -DCMAKE_CXX_COMPILER=arm-none-linux-gnueabihf-g++ -DCMAKE_INSTALL_PREFIX=/opt/externs/libs/arm-linux-gnueabihf -DARROW_BUILD_STATIC=ON -DARROW_BUILD_SHARED=OFF -DARROW_SIMD_LEVEL=NONE -DARROW_FLIGHT=ON  -DARROW_JEMALLOC=OFF -DARROW_FLIGHT_SQL=ON -DCMAKE_CXX_FLAGS="-march=armv7-a" -DARROW_RUNTIME_SIMD_LEVEL=NONE -DARROW_SIMD_LEVEL=NONE -DARROW_CPU_FLAG=ARMv7

make && sudo make install


gRPC 
git clone --recurse-submodules https://github.com/grpc/grpc
mkdir -p grpc/build && cd grpc/build
cmake -DgRPC_INSTALL=ON -DCMAKE_INSTALL_PREFIX=/usr/local ..
make -j$(nproc) && sudo make install

neuron
$ git clone https://github.com/emqx/neuron
$ cd neuron
$ git submodule update --init
$ mkdir build && cd build
$ cmake .. -DCMAKE_TOOLCHAIN_FILE=../cmake/arm-none-linux-gnueabihf.cmake -DCMAKE_BUILD_TYPE=Release -DDISABLE_UT=ON
$ make



移植到开发板：
neuron 交叉编译依赖的库 路径：/opt/externs/libs/arm-linux-gnueabihf/
依赖的相关库源码在：/home/<USER>/work/SPT/install

neuron SDK目录：neuron-2.11.2  （注意install 目录下 有neuron sdk 2.6.7版本，这个版本交叉编译到 RK3506 运行后存在网页登录认证失败的问题，但是其他功能都正常，建议还是选用SDK v2.11.2版本，测试验证通过了的）

/******************* 移植 neuron 到 设备上 ******************************************/
1、拷贝web 前端:neuron-dashboard 网页看板 直接解压neuron-dashboard.zip 	得到 dist 目录，拷贝到 开发版本 neuron 执行程序目录就可以。
2、拷贝neuron-2.11.2/build下的 libneuron-base.so、neuron、plugins、simulator、config、persistence 文件和文件夹到 板上 /opt/neuron 目录下
3、拷贝依赖库和bin(可选)*************** 服务器 /opt/externs/libs/arm-linux-gnueabihf/下的bin和lib 到板上 /opt/neuron 目录下
4、板上 /opt/neuron 目录给权限: chmod -R +x /opt/neuron
5、设置环境变量:export PATH=$PATH:/opt/neuron/bin
			export LD_LIBRARY_PATH=/opt/neuron/lib:$LD_LIBRARY_PATH
6、运行neuron 软件 ./neuron --log --log_level DEBUG &
				或者 ./neuron -d          (守护进程方式运行)



开发板 neuron 目录：/opt/neuron
root@myd-yr3506:/opt/neuron# ls
bin  config  dist  lib  libneuron-base.so  logs  neuron  persistence  plugins  simulator  tests
root@myd-yr3506:/opt/neuron# 


bin 目录 是 交叉编译移植库***************服务器目录的 /opt/externs/libs/arm-linux-gnueabihf/bin
lib 目录是 交叉编译移植库的目录/opt/externs/libs/arm-linux-gnueabihf/lib  (只需要拷贝so 动态库即可，.a静态库已经链接到可执行程序 neuron 内 所以不用拷贝)
config  logs  persistence  plugins  simulator  tests 直接拷贝 SDK 编译后的build 生成的文件夹即可
/************************************************************************************************************/

运行测试:
环境变量设置
export PATH=$PATH:/opt/neuron/bin
export LD_LIBRARY_PATH=/opt/neuron/lib:$LD_LIBRARY_PATH

运行:
./neuron --log --log_level DEBUG &
或者 ./neuron -d          (守护进程方式运行)

netstat -tuln | grep 7000

网页登录:如开发板ip：************** 则网页登录: http://**************:7000/

重置默认密码(admin  0000):
./neuron --reset-password 


/********************** 业务插件应用 开发编译 ******************************/
acme_server 编译命令:
新建build 目录下:
cmake .. -DCMAKE_TOOLCHAIN_FILE=../../cmake/arm-none-linux-gnueabihf.cmake

make 


