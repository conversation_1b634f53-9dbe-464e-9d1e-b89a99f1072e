# LoRa环形缓存内存泄漏分析报告

## 🔍 问题描述
`acme_lora_msg_recv` 函数从串口读取LoRa模块数据，通过环形缓存 `pMeshRcv` 存储，并使用 `at_ringbuffer_parser` 解析。运行几天后内存持续增长，疑似存在内存泄漏。

## 📊 代码分析结果

### 1. 环形缓存本身 - ✅ 无泄漏
环形缓存的实现是正确的：
- **初始化**: `RingBuffer_Malloc(BUFF_CASH_LEN)` - 2048字节固定大小
- **释放**: `RingBuffer_Free()` 在 `lora_tty_stop()` 中正确调用
- **操作**: `RingBuffer_In()` 和 `RingBuffer_Out()` 只是移动指针，不分配新内存

### 2. 🚨 发现的内存泄漏问题

#### 问题1: `loraRecvIoStatusHandle` 中的内存泄漏
**位置**: `app/acme_driver/acme_gw/lora_protocol/lora_protocol.c:341-345`

```c
cmd.data = calloc(1, pack->DataLength);
if(cmd.data == NULL){
    nlog_debug("calloc error.");
    return -1;  // ❌ 如果后续发送失败，cmd.data未释放
}
```

**问题**: 如果 `neu_plugin_op()` 发送失败，`cmd.data` 内存未释放。

#### 问题2: 解析函数中的潜在问题
**位置**: `at_ringbuffer_parser` 函数

```c
static void at_ringbuffer_parser(neu_plugin_t *plugin, RingBuffer *pRingBuff)
{
    // ...
    for (i=0; i<blen; i++) {
        RingBuffer_Out(pRingBuff, &reg, 1);  // ✅ 正确从缓存取出数据
        // ... 状态机处理
    }
}
```

**分析**: 解析函数本身没有内存分配，不会直接导致泄漏。

#### 问题3: 数据处理链中的泄漏
**数据流**: 
```
串口数据 → 环形缓存 → 解析器 → 协议处理 → 业务逻辑 → 释放
```

在 `loraRecvIoStatusHandle` 中：
```c
// 第341行：分配内存
cmd.data = calloc(1, pack->DataLength);

// 第353-355行：发送失败时未释放
if (0 != neu_plugin_op(plugin, header, &cmd)) {
    plog_error(plugin, "neu_plugin_op(NEU_REQ_ACME_DEV_DATA_POP) fail");
    return -1;  // ❌ cmd.data 泄漏！
}
```

## 🔧 修复方案

### 修复1: 修复 `loraRecvIoStatusHandle` 内存泄漏

```c
int loraRecvIoStatusHandle(neu_plugin_t *plugin, lpwan_wavemesh_t *pack)
{
    int ret = 0;
    char *name = NULL;
    char eui[20] = {0};
    if(pack == NULL || plugin == NULL) return -1;

    Hex2Str(pack->EuiAdr, eui, 8); 
    name = neu_device_manager_get_name_by_eui(plugin->subDevice_manager, eui);
    if(name == NULL){
        nlog_debug("eui:%s dev not found.", eui);
        return -1;
    }

    nlog_debug("eui:%s dev:%s found.", eui, name);

    // 发送数据给设备层
    neu_reqresp_head_t header = {
        .type = NEU_REQ_ACME_DEV_DATA_POP,
        .ctx  = plugin,
    };

    neu_acme_dev_data_pop_t cmd = {
        .rsp    = pack->RSP,
        .len    = pack->DataLength,
        .data   = NULL,
    };

    cmd.data = calloc(1, pack->DataLength);
    if(cmd.data == NULL){
        nlog_debug("calloc error.");
        return -1;
    }

    memcpy(cmd.data, pack->SubData, pack->DataLength-1);
    strcpy(cmd.node, name);
    strcpy(cmd.eui, eui);

    if (0 != neu_plugin_op(plugin, header, &cmd)) {
        plog_error(plugin, "neu_plugin_op(NEU_REQ_ACME_DEV_DATA_POP) fail");
        // 🔧 修复：发送失败时释放内存
        if (cmd.data != NULL) {
            free(cmd.data);
            cmd.data = NULL;
        }
        ret = -1;
    }
    // 注意：发送成功时不释放cmd.data，由接收方负责释放

    return ret;
}
```

### 修复2: 添加内存监控到关键函数

```c
int acme_lora_msg_recv(enum neu_event_io_type type, int fd, void *usr_data)
{
    MEM_CHECK_ENTER("acme_lora_msg_recv");
    
    neu_plugin_t *plugin = (neu_plugin_t *)usr_data;
    if(plugin == NULL){
        nlog_warn("tty recv parameter error.");
        MEM_CHECK_EXIT("acme_lora_msg_recv");
        return -1;
    }

    int ret = 0;
    char recv_buff[512] = {0};
    int nByte = 0;

    if (type != NEU_EVENT_IO_READ) {
        nlog_warn("read close!");
        MEM_CHECK_EXIT("acme_lora_msg_recv");
        return -1;
    }

    nByte = read(fd, recv_buff, sizeof(recv_buff));
    if (nByte > 0) {
        // 收到数据加入环形队列
        RingBuffer_In(plugin->pMeshRcv, recv_buff, nByte);   

        printf("Lora tty recv data(%d): *********** -->", nByte);
        for(int i=0; i<nByte; i++){
            printf("%02x ", recv_buff[i]);
        }
        printf(" ----< *******************\r\n");
    }

    at_ringbuffer_parser(plugin, plugin->pMeshRcv);     // 串口数据解析

    MEM_CHECK_EXIT("acme_lora_msg_recv");
    return ret;
}
```

### 修复3: 优化环形缓存使用

```c
static void at_ringbuffer_parser(neu_plugin_t *plugin, RingBuffer *pRingBuff)
{
    uint8_t i;
    uint8_t blen = 0;
    uint8_t reg;
    if(plugin == NULL || pRingBuff == NULL) return;

    int beMasterFlag = 1;
    
    blen = RingBuffer_Len(pRingBuff);
    
    // 🔧 优化：避免处理过大的数据包，防止内存异常
    if (blen > PACK_LEN_MAX) {
        nlog_warn("Ring buffer data too large (%d bytes), resetting", blen);
        RingBuffer_Reset(pRingBuff);
        return;
    }
    
    for (i = 0; i < blen; i++) {
        RingBuffer_Out(pRingBuff, &reg, 1);
        
        printf("%02x ", reg);
        
        // ... 原有的状态机处理逻辑
        if((plugin->tty_pack_sta->_state == PCK_HED) && (reg == GWMP_HEADER_STX)){
            plugin->tty_pack_sta->_state = PCK_DATA;
            plugin->tty_pack_sta->_pos  = 0;
            plugin->tty_pack_sta->lora_raw_data[plugin->tty_pack_sta->_pos++] = reg;
        }
        // ... 其他处理逻辑保持不变
    }
}
```

## 📈 内存泄漏量估算

### 泄漏源分析
假设每次LoRa数据包平均大小为50字节：

1. **`cmd.data` 泄漏**: 每次发送失败泄漏50字节
2. **发送失败率**: 假设1%的发送失败率
3. **数据频率**: 每30秒一次数据上报

**计算**:
- 每天数据包数: 24 × 60 × 2 = 2880个
- 每天失败数: 2880 × 1% = 28.8个
- 每天泄漏: 28.8 × 50字节 = 1440字节
- 几天累积: 1440字节 × 天数

这个泄漏量看似不大，但如果：
- 数据包更大（100-200字节）
- 发送失败率更高（网络不稳定）
- 其他未发现的泄漏点

累积效应可能导致明显的内存增长。

## 🎯 验证方法

### 1. 使用内存监控工具
```bash
# 启用内存监控
tail -f /tmp/lora_memory_*.log | grep "acme_lora_msg_recv"
```

### 2. 添加调试日志
```c
// 在关键位置添加内存使用日志
nlog_debug("Memory before data processing: %lu KB", get_process_memory_usage());
nlog_debug("Memory after data processing: %lu KB", get_process_memory_usage());
```

### 3. 压力测试
```bash
# 模拟高频数据发送，观察内存变化
watch -n 1 'ps aux | grep neuron | grep -v grep'
```

## 🔍 建议的排查步骤

1. **立即修复**: 应用上述修复方案，特别是 `loraRecvIoStatusHandle` 的内存泄漏
2. **监控验证**: 部署修复版本，使用内存监控工具观察
3. **长期测试**: 运行7天以上，确认内存使用稳定
4. **扩展排查**: 如果问题仍存在，检查其他可能的泄漏点

## 📋 总结

**主要问题**: `loraRecvIoStatusHandle` 函数中 `cmd.data` 在发送失败时未释放，这是最可能的内存泄漏源。

**环形缓存**: 本身实现正确，不是泄漏源。

**解析函数**: `at_ringbuffer_parser` 没有内存分配，不会直接导致泄漏。

**修复优先级**:
1. 🔥 高优先级：修复 `cmd.data` 泄漏
2. 🔧 中优先级：添加内存监控
3. 📊 低优先级：优化缓存使用

通过这些修复，应该能够解决内存持续增长的问题。
