#!/bin/bash

# ACME LoRa 内存监控分析脚本
# 使用方法: ./analyze_memory.sh [log_file]

LOG_FILE=${1:-"/tmp/lora_memory_*.log"}

echo "=== ACME LoRa 内存监控分析报告 ==="
echo "分析文件: $LOG_FILE"
echo "分析时间: $(date)"
echo

# 1. 基本统计
echo "1. 基本统计信息"
echo "----------------------------------------"
if [ -f $LOG_FILE ]; then
    TOTAL_LINES=$(wc -l < $LOG_FILE)
    START_TIME=$(head -1 $LOG_FILE | grep -o '[0-9]\{10\}')
    END_TIME=$(tail -1 $LOG_FILE | grep -o '[0-9]\{10\}')
    
    if [ ! -z "$START_TIME" ] && [ ! -z "$END_TIME" ]; then
        DURATION=$((END_TIME - START_TIME))
        HOURS=$((DURATION / 3600))
        MINUTES=$(((DURATION % 3600) / 60))
        
        echo "日志条目总数: $TOTAL_LINES"
        echo "监控时长: ${HOURS}小时${MINUTES}分钟"
        echo "平均记录频率: $((TOTAL_LINES * 3600 / DURATION))条/小时"
    fi
else
    echo "日志文件不存在: $LOG_FILE"
    exit 1
fi
echo

# 2. 内存使用趋势
echo "2. 内存使用趋势"
echo "----------------------------------------"
echo "初始内存使用:"
grep -m 1 "RSS:" $LOG_FILE | grep -o "RSS: [0-9]* KB"

echo "当前内存使用:"
grep "RSS:" $LOG_FILE | tail -1 | grep -o "RSS: [0-9]* KB"

echo "峰值内存使用:"
grep "Peak RSS:" $LOG_FILE | tail -1 | grep -o "Peak RSS: [0-9]* KB"

echo "最大单次增长:"
grep "delta: +" $LOG_FILE | sed 's/.*delta: +\([0-9]*\) KB.*/\1/' | sort -n | tail -1 | xargs -I {} echo "{}KB"
echo

# 3. 内存泄漏检测
echo "3. 内存泄漏检测"
echo "----------------------------------------"
LEAK_COUNT=$(grep "Potential Leak:" $LOG_FILE | wc -l)
if [ $LEAK_COUNT -gt 0 ]; then
    echo "发现 $LEAK_COUNT 次潜在泄漏记录"
    echo "最新泄漏情况:"
    grep "Potential Leak:" $LOG_FILE | tail -3
else
    echo "未发现明显的内存泄漏记录"
fi
echo

# 4. 函数调用分析
echo "4. 函数调用内存影响分析"
echo "----------------------------------------"
echo "fcm_dev_message_handle 调用次数:"
grep "fcm_dev_message_handle" $LOG_FILE | wc -l

echo "内存增长最多的函数调用:"
grep "ENTER_\|EXIT_" $LOG_FILE | grep "delta: +" | \
    sed 's/.*\(ENTER_\|EXIT_\)\([^[:space:]]*\).*delta: +\([0-9]*\).*/\2 \3/' | \
    sort -k2 -n | tail -5
echo

# 5. 异常检测
echo "5. 异常情况检测"
echo "----------------------------------------"
LARGE_GROWTH=$(grep "delta: +" $LOG_FILE | sed 's/.*delta: +\([0-9]*\).*/\1/' | awk '$1 > 1024 {print $1}' | wc -l)
echo "大于1MB的内存增长次数: $LARGE_GROWTH"

if [ $LARGE_GROWTH -gt 0 ]; then
    echo "异常增长详情:"
    grep "delta: +" $LOG_FILE | awk '/delta: \+[0-9]{4,}/ {print}' | tail -3
fi
echo

# 6. 建议
echo "6. 分析建议"
echo "----------------------------------------"
CURRENT_RSS=$(grep "RSS:" $LOG_FILE | tail -1 | sed 's/.*RSS: \([0-9]*\) KB.*/\1/')
INITIAL_RSS=$(grep -m 1 "RSS:" $LOG_FILE | sed 's/.*RSS: \([0-9]*\) KB.*/\1/')

if [ ! -z "$CURRENT_RSS" ] && [ ! -z "$INITIAL_RSS" ]; then
    GROWTH=$((CURRENT_RSS - INITIAL_RSS))
    if [ $GROWTH -gt 5120 ]; then  # 5MB
        echo "⚠️  内存增长过大 (${GROWTH}KB)，建议检查内存泄漏"
    elif [ $GROWTH -gt 1024 ]; then  # 1MB
        echo "⚠️  内存有一定增长 (${GROWTH}KB)，建议持续监控"
    else
        echo "✅ 内存使用基本稳定 (增长${GROWTH}KB)"
    fi
fi

echo "建议监控周期: 建议运行7天以上观察长期趋势"
echo "日志清理: 定期清理 /tmp/lora_memory_*.log 避免占用磁盘空间"
echo

echo "=== 分析完成 ==="
