CC = gcc
CFLAGS = -Wall -Wextra -std=c99 -g
TARGET = test_ac_parser
SOURCES = ac_data_parser.c test_ac_parser.c
OBJECTS = $(SOURCES:.c=.o)
HEADERS = ac_data_parser.h

# 默认目标
all: $(TARGET)

# 编译可执行文件
$(TARGET): $(OBJECTS)
	$(CC) $(OBJECTS) -o $(TARGET)

# 编译目标文件
%.o: %.c $(HEADERS)
	$(CC) $(CFLAGS) -c $< -o $@

# 运行测试
test: $(TARGET)
	./$(TARGET)

# 清理编译文件
clean:
	rm -f $(OBJECTS) $(TARGET)

# 安装（可选）
install: $(TARGET)
	cp $(TARGET) /usr/local/bin/

# 显示帮助
help:
	@echo "Available targets:"
	@echo "  all     - Build the program"
	@echo "  test    - Build and run the test"
	@echo "  clean   - Remove compiled files"
	@echo "  install - Install to /usr/local/bin"
	@echo "  help    - Show this help message"

.PHONY: all test clean install help
