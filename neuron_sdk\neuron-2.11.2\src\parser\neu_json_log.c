/**
 * NEURON IIoT System for Industry 4.0
 * Copyright (C) 2020-2022 EMQ Technologies Co., Ltd All rights reserved.
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 3 of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with this program; if not, write to the Free Software Foundation,
 * Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.
 **/

/*
 * DO NOT EDIT THIS FILE MANUALLY!
 * It was automatically generated by `json-autotype`.
 */

#include <stdlib.h>
#include <string.h>

#include "json/json.h"

#include "neu_json_log.h"
#include "utils/log.h"
#include "utils/utarray.h"
#include "utils/utextend.h"

int neu_json_decode_update_log_level_req(
    char *buf, neu_json_update_log_level_req_t **result)
{
    int   ret      = 0;
    void *json_obj = neu_json_decode_new(buf);

    neu_json_update_log_level_req_t *req =
        calloc(1, sizeof(neu_json_update_log_level_req_t));
    if (req == NULL) {
        return -1;
    }

    req->node_name = NULL;
    req->core      = true;

    neu_json_elem_t req_elems[] = {
        {
            .name      = "node",
            .t         = NEU_JSON_STR,
            .attribute = NEU_JSON_ATTRIBUTE_OPTIONAL,
        },
        {
            .name = "level",
            .t    = NEU_JSON_STR,
        },
        {
            .name      = "core",
            .attribute = NEU_JSON_ATTRIBUTE_OPTIONAL,
        }
    };

    ret = neu_json_decode_by_json(json_obj, NEU_JSON_ELEM_SIZE(req_elems),
                                  req_elems);
    if (ret != 0) {
        goto decode_fail;
    }

    if (req_elems[0].v.val_str != NULL) {
        req->node_name = req_elems[0].v.val_str;
    }
    req->log_level = req_elems[1].v.val_str;
    if (req_elems[2].t == NEU_JSON_BOOL) {
        req->core = req_elems[2].v.val_bool;
    }
    *result = req;
    goto decode_exit;

decode_fail:
    free(req);
    ret = -1;
decode_exit:
    if (json_obj != NULL) {
        neu_json_decode_free(json_obj);
    }
    return ret;
}

void neu_json_decode_update_log_level_req_free(
    neu_json_update_log_level_req_t *req)
{
    if (req->node_name != NULL) {
        free(req->node_name);
    }
    if (req->log_level != NULL) {
        free(req->log_level);
    }
    if (req != NULL) {
        free(req);
    }
}

int neu_json_encode_log_list_resp(void *json_object, void *param)
{
    int       ret       = 0;
    UT_array *log_files = (UT_array *) param;
    void *    log_array = neu_json_array();

    if (NULL != log_files) {
        utarray_foreach(log_files, neu_resp_log_file_t *, p_log)
        {
            if (NULL != p_log) {
                neu_json_elem_t log_elems[] = {
                    {
                        .name      = "file",
                        .t         = NEU_JSON_STR,
                        .v.val_str = p_log->file,
                    },
                    {
                        .name      = "size",
                        .t         = NEU_JSON_INT,
                        .v.val_int = p_log->size,
                    },
                };

                neu_json_encode_array(log_array, log_elems,
                                      NEU_JSON_ELEM_SIZE(log_elems));
            }
        }
    }

    neu_json_elem_t resp_elems[] = { {
        .name         = "files",
        .t            = NEU_JSON_OBJECT,
        .v.val_object = log_array,
    } };

    ret = neu_json_encode_field(json_object, resp_elems,
                                NEU_JSON_ELEM_SIZE(resp_elems));

    return ret;
}
