#include "pthread.h"
#include <nng/nng.h>
#include <nng/supplemental/http/http.h>
#include "utils/http.h"
#include "rsa.h"
#include <jansson.h>
#include <memory.h>
#include <signal.h>
#include <stdbool.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "neuron.h"
#include "http.h"
#include "mqtt_config.h"
#include "mqtt_plugin_intf.h"
#include "acme_mqtt_plugin.h"
#include "neuron/msg.h"
#include "parser/neu_json_node.h"
#include "parser/neu_json_tag.h"
#include "connect_server.h"
#include "connect_comman.h"

static nng_aio* aio_body = NULL;
extern acme_plugin_t *acme_plugin;

static void split_to_ll(char *src, const char *separator, long long *dest, int *num) 
{
    char *pNext;
    int count = 0;
    
    if (src == NULL || strlen(src) == 0)
        return;
        
    if (separator == NULL || strlen(separator) == 0)
        return;   
        
    pNext = strtok(src,separator);
    while(pNext != NULL) {
        //*dest++ = pNext;
        dest[count] = atoll(pNext);
        //printf("%lld \n",dest[count]);
        ++count;
        pNext = strtok(NULL,separator);  
    }  
    
    *num = count;
 }

void body_callback(void* arg) {
    nng_aio* aio = aio_body;
    if(aio == NULL) {nlog_debug("body_callback arg is null"); return;}

    if (nng_aio_result(aio) != 0) {
        nlog_debug("Read body error: %s\n", nng_strerror(nng_aio_result(aio)));
        return;
    }
    nlog_debug("body_callback result:%d",nng_aio_result(aio));
    
    /*nng_iov* iov = nng_aio_get_input(aio, 0);
    if (iov != NULL && iov->iov_len > 0) {
        nlog_debug("Response Body:\n%.*s\n", (int)iov->iov_len, (char*)iov->iov_buf);
    }else{
        nlog_debug("Response Body:is null");
    }*/
   /*void* data = nng_aio_get_output(aio, 0);
    size_t len = nng_aio_count(aio);
    nlog_debug("Received %zd bytes: %.*s\n", len, (int)len, (char*)data);*/
    size_t len = nng_aio_count(aio);
    void *data = nng_aio_get_input(aio, 0);
    printf("Received %zu bytes: %.*s\n", len, (int)len, (char*)data);
}

int acme_http_request(char * url_buff,char *data, int len)
{
    if(url_buff == NULL || data == NULL) return -1;

    nng_http_client *client = NULL;
    nng_http_req *req = NULL;
    nng_aio *aio = NULL;
    //nng_aio* aio_body = NULL;
    nng_url *url = NULL;
    char buffer[4096] = {0};
    
    if(nng_url_parse(&url, url_buff) != 0){
        nlog_debug("acme_http_request->nng_url_parse error.");
        return -1;
    }
    nng_http_client_alloc(&client, url);

    //设定http 请求注册超时时间5秒
    nng_aio_alloc(&aio, NULL, NULL);
    nng_aio_set_output(aio, 0, buffer);

    int rv = -1;
   /* rv = nng_aio_alloc(&aio_body, body_callback, NULL);
    if(rv != 0){
        nlog_error("AIO allocation failed: %s\n", nng_strerror(rv));
    }*/
    
    nng_aio_set_timeout(aio, 5000);
    //nng_aio_set_timeout(aio_body, 5000);

    //1、客户端连接
    nng_http_client_connect(client, aio);

    //等待http 连接成功，5秒超时
    nng_aio_wait(aio);    

    rv = -1;
    if ((rv = nng_aio_result(aio)) != 0) {
        nlog_error("(%s)nng error: %s", url, nng_strerror(rv));
        nng_url_free(url);
        nng_http_client_free(client);
        nng_aio_free(aio);
        //nng_aio_free(aio_body);   
        return -1;
    }

    //2、连接成功后  开始写入post 数据
    nng_http_conn *conn = nng_aio_get_output(aio, 0);
    nng_http_res * res  = NULL;
    nng_http_req_alloc(&req, url);
    nng_http_req_set_method(req, "POST");
    nng_http_req_add_header(req, "Content-Type", "application/json");
    nng_http_req_add_header(req, "Connection", "keep-alive");
    char buf_len[32] = { 0 };
    sprintf(buf_len, "%d", len);
    nng_http_req_add_header(req, "Content-Length", buf_len);
    nng_http_req_set_data(req, data, len);

    //nng_http_req_copy_data(req,data,len);

    nlog_debug("[http send] Content-Type:%s ",nng_http_req_get_header(req,"Content-Type"));
    nlog_debug("[http send] Content-Length:%s ",nng_http_req_get_header(req,"Content-Length"));
    nlog_debug("[http send] Connection:%s ",nng_http_req_get_header(req,"Connection"));

    /*void *w_data;
    size_t  w_len = 0;
    nng_http_req_get_data(req,&w_data,&w_len);
    nlog_debug("[http send] data (%.*s) ",(int)w_len,(char *)w_data);
*/
    nng_http_conn_write_req(conn, req, aio);
    nng_aio_wait(aio);
    if ((rv = nng_aio_result(aio)) != 0) {
        nlog_debug("nng error: %s",nng_strerror(rv));
        nng_url_free(url);
        nng_http_client_free(client);
        nng_http_req_free(req);
        nng_http_conn_close(conn);
        nng_aio_free(aio);
        //nng_aio_free(aio_body);   
        return -1;
    }

     

    //3、写入成功，开始读服务端返回
    nng_http_res_alloc(&res);
    
       /* // 4. 读取整个响应体
        nng_http_conn_read_all(conn, aio);
        nng_aio_wait(aio);
        if ((rv = nng_aio_result(aio)) != 0) {
            nlog_debug("Body read error: %s",nng_strerror(rv));
           
            nng_url_free(url);
            nng_http_client_free(client);
            nng_http_req_free(req);
            nng_http_res_free(res);
            nng_http_conn_close(conn);
            nng_aio_free(aio);

            return -1;
        }

        nlog_debug("[http ack] read body ok...");

        // 获取数据指针和长度
        void *r_data;
        size_t  r_len = 0;
            
        r_data = nng_aio_get_output(aio, 0);
        r_len = nng_aio_count(aio);

        if(r_len > 0){
            nlog_debug("[register Success] ACME server ACK(len=%d): %s",r_len,r_data);
            //printf("Body data (%.*s)\n", (int)r_len, (char *)r_data);
            //free(r_data);
        }else{
            nlog_debug("[register error] ACME server ACK is null");
        }
*/


    nng_http_conn_read_res(conn, res, aio);
    nng_aio_wait(aio);
    if ((rv = nng_aio_result(aio)) != 0) {
        nlog_error("nng error: %s", nng_strerror(rv));
        nng_url_free(url);
        nng_http_client_free(client);
        nng_http_req_free(req);
        nng_http_res_free(res);
        nng_http_conn_close(conn);
        nng_aio_free(aio);
        //nng_aio_free(aio_body);       
        return -1;
    }

    /*nlog_debug("[http ack] read body start...");
    nng_http_conn_read_all(conn,aio);
    nng_aio_wait(aio);  
    if ((rv = nng_aio_result(aio)) != 0) {
        nlog_error("nng error: %s", nng_strerror(rv));
        nng_url_free(url);
        nng_http_client_free(client);
        nng_http_req_free(req);
        nng_http_res_free(res);
        nng_http_conn_close(conn);
        nng_aio_free(aio);
        //nng_aio_free(aio_body);       
        return -1;
    } 
    nlog_debug("[http ack] read body end...");   */  


    // 获取数据指针和长度
    void *r_data;
    size_t  r_len = 0;
    //r_len = nng_aio_count(aio);
    //r_data = nng_aio_get_output(aio, 0);

    nng_http_res_get_data(res,&r_data,&r_len);

    /*nng_iov* iov = nng_aio_get_input(aio, 0);
    if (iov != NULL && iov->iov_len > 0) {
        nlog_debug("Response Body:\n%.*s\n", (int)iov->iov_len, (char*)iov->iov_buf);
    }else{
        nlog_debug("Response Body:is null");
    }*/

    //r_data = nng_aio_get_input(aio, 0);
    //len = nng_aio_count(aio);

    nlog_debug("[register Success] ACME server ACK(len=%d): %s",r_len,r_data);
    nlog_debug("[register Success] buffer ACK: %s",buffer);

    // 跳过Content-Length校验
    //nng_http_res_del_header(res, "Content-Length");
    //nng_http_res_set_header(res, "Transfer-Encoding", "chunked");

    uint16_t status = nng_http_res_get_status(res);
    if(status >= NNG_HTTP_STATUS_OK && status < NNG_HTTP_STATUS_MULTIPLE_CHOICES)
    {
        nlog_debug("[http ok] acme server http status:%d",status);
        
        nlog_debug("[http ack] version:%s ",nng_http_res_get_version(res));
        nlog_debug("[http ack] reason:%s ",nng_http_res_get_reason(res));
        nlog_debug("[http ack] Content-Length:%s ",nng_http_res_get_header(res, "Content-Length"));
        nlog_debug("[http ack] Content-Type:%s ",nng_http_res_get_header(res, "Content-Type"));
        nlog_debug("[http ack] Content-Encoding:%s ",nng_http_res_get_header(res, "Content-Encoding"));
        nlog_debug("[http ack] Transfer-Encoding:%s ",nng_http_res_get_header(res, "Transfer-Encoding"));
        nlog_debug("[http ack] Date:%s ",nng_http_res_get_header(res, "Date"));
        nlog_debug("[http ack] Server:%s ",nng_http_res_get_header(res, "Server"));

     
    
    }else{
        nlog_debug("[http error] acme server http status:%d",status);
    }

    nng_url_free(url);
    nng_http_client_free(client);
    nng_http_req_free(req);
    nng_http_res_free(res);
    nng_http_conn_close(conn);
    nng_aio_free(aio);
    //nng_aio_free(aio_body);
    return status;
}

// mqttaddr decrypt
int rsa_decrypt_str(long long *encrypted, int msg_len, char *real_data)
{

  struct public_key_class pub[1];
  struct private_key_class priv[1];

    pub->modulus = 1265242567;
    pub->exponent = 131073;

    priv->modulus = 1265242567;
    priv->exponent = 63086657;

  int i;

  char *decrypted = rsa_decrypt((const long long *)encrypted, 8*msg_len, priv);
  if (!decrypted){
    fprintf(stderr, "Error in decryption!\n");
    return 1;
  }
  printf("Decrypted:%s\n",decrypted);
  for(i=0; i < msg_len; i++){
     real_data[i] = decrypted[i];
  }  

  free(decrypted);
  return 0;

}

// veriyCode 加密
int rsa_encrypt_eui(long long *encrypted_buff,char *uid)
{
    int i;
    struct public_key_class pub[1];
    struct private_key_class priv[1];
    //rsa_gen_keys(pub, priv, PRIME_SOURCE_FILE);
    pub->modulus = 1265242567;
    pub->exponent = 131073;

    priv->modulus = 1265242567;
    priv->exponent = 63086657;

    long long *encrypted = rsa_encrypt(uid, 16, pub);
    if (!encrypted){
        fprintf(stderr, "Error in encryption!\n");
        return 1;
    }

    for(i=0; i < 16; i++){
        encrypted_buff[i] = (long long)encrypted[i];        
    }  

    free(encrypted);
    return 0;
}

/*
*
* 设备注册应答解析
*/
int neu_json_decode_gw_register_req(const char* rcv_body, gw_register_resp_t *resp)
{
    if(rcv_body == NULL || resp == NULL) return -1;

    void *json_body =  neu_json_decode_new(rcv_body);
    if(NULL == json_body){
        nlog_debug("Body parser error !!!");
        return -1;
    }

    neu_json_elem_t data_elems[] = {
            {
                .name      = "username",
                .t         = NEU_JSON_STR,
            },
            {
                .name      = "password",
                .t         = NEU_JSON_STR,               
            },
            {
                .name      = "mqttAddr",
                .t         = NEU_JSON_STR,
            },
            {
                .name      = "time",
                .t         = NEU_JSON_STR,
            }
    };

    neu_json_elem_t resp_elems[] = { 
        {
            .name = "success",
            .t    = NEU_JSON_BOOL,
        },
        {
            .name = "code",
            .t    = NEU_JSON_INT,
        },
        {
            .name = "msg",
            .t    = NEU_JSON_STR,
        }
    };

    int ret = neu_json_decode_by_json(json_body, NEU_JSON_ELEM_SIZE(resp_elems), resp_elems);
    if (ret != 0) {
        goto error;
    }

    resp->success   = resp_elems[0].v.val_bool;
    resp->code      = resp_elems[1].v.val_int;
    resp->msg       = resp_elems[2].v.val_str;

    nlog_debug("GW register resp: ***** success=%d || code=%d || msg=%s *****\r\n",resp->success,resp->code,resp->msg);

    if(resp->success == false || resp->code != 200){
        nlog_debug("gw register error !");   
        neu_json_decode_free(json_body);     
        return 0;
    }

    //data 字段解析
    json_t * j_data = json_object_get(json_body, "data");
    if(j_data == NULL){
        nlog_debug("Body data is null !!!");
        goto error;
    }

    /*char *printStr = NULL; 
     neu_json_encode(j_data, &printStr);   
     int len = strlen(printStr);                        

    nlog_debug("data info:  dataLen:%d ,data:%s",len, printStr);
    free(printStr);*/
    
    ret = neu_json_decode_by_json(j_data, NEU_JSON_ELEM_SIZE(data_elems), data_elems);
    if (ret != 0) {
        goto error1;
    }

    
    resp->username   = data_elems[0].v.val_str;
    resp->password   = data_elems[1].v.val_str;
    resp->mqttAddr   = data_elems[2].v.val_str;
    resp->time       = data_elems[3].v.val_str;

    nlog_debug("GW register info: ***** success=%d || code=%d || msg=%s || \
                                        username=%s || password=%s || \
                                        mqttAddr=%s || time=%s *****\r\n",resp->success,resp->code,resp->msg,\
                                        resp->username,resp->password,\
                                        resp->mqttAddr,resp->time);

    neu_json_decode_free(json_body);
    return 0;

error1:
    data_elems[0].v.val_str;
    data_elems[1].v.val_str;
    data_elems[2].v.val_str;
    data_elems[3].v.val_str;
    

error:
    resp_elems[2].v.val_str;
    neu_json_decode_free(json_body);
    return -1;
}


/*
*
* 销毁注册返回信息结构(字符串类的字段json 内存占用空间需要销毁)
*/
int destory_gw_register_info(gw_register_resp_t *reg)
{
    int ret = 0;
    if(reg == NULL) return -1;

    if(reg->username) free(reg->username);
    if(reg->password) free(reg->password);
    if(reg->mqttAddr) free(reg->mqttAddr);
    if(reg->time) free(reg->time);
    if(reg->msg) free(reg->msg);

    free(reg);
    return ret;
}

/*
* 向平台发起设备注册 http post -> 
* 测试平台: http://192.168.109.102/bec/v3/device/register
* 项目id: 1001000098
*/
void * request_gw_register(gw_register_reg_t *req, ft_http_client_t* http ,char *url)
{
    if(req == NULL || url == NULL || http == NULL) return NULL;
   
    void *root = neu_json_encode_new();
    const char* body = 0;

    neu_json_elem_t req_elems[] = { 
        {
            .name = "uid",
            .t    = NEU_JSON_STR,
            .v.val_str = req->uid,
        },
        {
            .name = "mName",
            .t    = NEU_JSON_STR,
            .v.val_str = req->mName,
        },
        {
            .name = "key",
            .t    = NEU_JSON_STR,
            .v.val_str = req->appkey,
        },
        {
            .name = "projectId",
            .t    = NEU_JSON_STR,
            .v.val_str = req->g_projectID,
        },
        {
            .name = "pointNum",
            .t    = NEU_JSON_INT,
            .v.val_int = req->pointNum,
        },
        {
            .name = "fw_version",
            .t    = NEU_JSON_STR,
            .v.val_str = req->gw_fw_ver,
        },
        {
            .name = "veriyCode",
            .t    = NEU_JSON_STR,
            .v.val_str = req->p_veriyCode,
        },
        {
            .name = "registerToken",
            .t    = NEU_JSON_STR,
            .v.val_str = req->registerToken,
        }
    };
    neu_json_encode_field(root, req_elems,NEU_JSON_ELEM_SIZE(req_elems));
     char *printStr = NULL; 
     neu_json_encode(root, &printStr);   
     int len = strlen(printStr);                        

    nlog_debug("request server url:%s",url);
    nlog_debug("request_gw_register dataLen:%d ,data:%s",len, printStr);

#if 0
    //TODO: nng 关于 http post 接口 发送设备注册报文
    //...此处接口为 NNG http 封装接口，目前测试post 能收到应答，但是body 内容取不到的问题，暂时先用http 接口，后续再调试 NNG HTTP 
    acme_http_request(url, printStr, len);
#else
    body = ft_http_request(http, url, M_POST, printStr, len, 0, 0);
    nlog_debug("Recv body:%s",body);
    if(body != NULL){
        gw_register_resp_t *gw_register_resp = (gw_register_resp_t *)calloc(1, sizeof(gw_register_resp_t));
        if(gw_register_resp == NULL){
            nlog_debug("gw_register_info calloc error.");
            goto end;
        }
        int ret = neu_json_decode_gw_register_req(body,gw_register_resp);  
        if(ret < 0){
            nlog_debug("register resp decode faild !!!");
            destory_gw_register_info(gw_register_resp);
            goto end;
        }else{
            nlog_debug("register resp decode ok .");
            free(printStr);
            neu_json_encode_free(root);
            return gw_register_resp;
        }
    }
#endif

end:
    free(printStr);
    neu_json_encode_free(root);
    
    return NULL;
}

/*
*    销毁请求数据结构
 */
int destory_req_content(gw_register_reg_t *req)
{
    if(req == NULL) return -1;

    if(req->p_veriyCode != NULL) free(req->p_veriyCode);

    free(req);

    return 0;
}

/*
*    创建请求数据结构
 */
gw_register_reg_t * create_req_content()
{
    gw_register_reg_t *req = (gw_register_reg_t *) calloc(1, sizeof(gw_register_reg_t));
    if(req == NULL){
        nlog_debug("gw_register_reg_t calloc error !");
        return NULL;    
    }
    /*
    char uid[22];
    char mName[50];
    char appkey[36];
    char g_projectID[32];
    int pointNum;
    char gw_fw_ver[36];
    char *p_veriyCode;
    char registerToken[22];
    */

    //TODO: 后续关于请求内容和服务器连接数据都需要在 json 配置文件中解析
    memset(req,0,sizeof(gw_register_reg_t));
    strncpy(req->uid, "183496B8A2FACB75", sizeof("183496B8A2FACB75"));
    strncpy(req->mName, "IGW", sizeof("IGW"));
    strncpy(req->appkey, "183496B8A2FACB75183496B8A2FACB75", sizeof("183496B8A2FACB75183496B8A2FACB75"));
    strncpy(req->g_projectID, "1001000086", sizeof("1001000086"));
    strncpy(req->gw_fw_ver, "H210S310.20240904115204", sizeof("H210S310.20240904115204"));

    req->pointNum = 93;
    req->p_veriyCode = NULL;
    strncpy(req->registerToken, "183496B8A2FACB75", sizeof("183496B8A2FACB75"));

    char * p_veriyCode = (char *) calloc(1, 512);
    if(p_veriyCode == NULL){
       nlog_debug("gw_register_reg_t calloc error !");
       free(req);
       return NULL;
    }
    req->p_veriyCode = p_veriyCode;

    long long encry_eui_buff[26]={0};
    char temp[30] = {0};
    rsa_encrypt_eui(encry_eui_buff, req->uid);
    for(int i=0;i<16;i++){
        if(i==0){
            sprintf(p_veriyCode,"%lld",encry_eui_buff[0]);
        }else{
            memset(temp, 0, 30);
            strcat(p_veriyCode, " ");
            sprintf(temp, "%lld", encry_eui_buff[i]);
            strcat(p_veriyCode, temp);
        }
    }

    nlog_debug("veriyCode : %s",req->p_veriyCode);

    return req;
}

/*
* 从服务器返回的 MQTT 地址解析出 ip 和 端口号
*/
int parse_mqtt_address(char* mqttAddr, char* ip, int* port) 
{
    if(mqttAddr == NULL || ip == NULL || port == NULL) return -1;
   
    //MQTT addr rsa_decrypt
    long long encrypted[100] = {0};
    char url[100] = {0};
    int num = 0;
    split_to_ll(mqttAddr, " ", encrypted, &num);
    rsa_decrypt_str(encrypted, num, url);
    nlog_debug("ACME MQTT addr: %s", url);

    // 跳过协议部分（如"tcp://"）
    const char* ip_start = strstr(url, "://");
    if (ip_start == NULL) {
        strcpy(ip, "invalid_url");
        *port = -1;
        return -1;
    }
    ip_start += 3; // 跳过"://"

    // 复制IP部分
    const char* port_start = strchr(ip_start, ':');
    if (port_start == NULL) {
        strcpy(ip, "invalid_port");
        *port = -1;
        return -1;
    }

    int ip_len = port_start - ip_start;
    strncpy(ip, ip_start, ip_len);
    ip[ip_len] = '\0';

    // 解析端口号
    *port = atoi(port_start + 1);

    return 0;
}

/*
* 请求适配器刷新config 参数
*/
int acme_mqtt_setting_refresh_req(acme_plugin_t *acme_info)
{
    if(acme_info == NULL)  return -1;

    neu_reqresp_head_t     header = { 0 };
    neu_req_node_setting_t cmd    = { 0 };

    header.ctx  = acme_info->plugin;
    header.type = NEU_REQ_NODE_SETTING;
    strcpy(cmd.node, acme_info->plugin->common.name);

    void * setting_json = acme_mqtt_setting_get();
    neu_json_encode(setting_json, &cmd.setting); 
    neu_json_encode_free(setting_json);
    
    nlog_debug("---> acme mqtt  send setting msg.%s",cmd.setting);
    neu_plugin_op(acme_info->plugin, header, &cmd);
    nlog_debug("acme mqtt  send setting msg ok.");

    return 0;
}


/*
* 启动acme mqtt 任务
*/
int acme_mqtt_process_start(gw_register_resp_t * resp, acme_plugin_t *acme_info)
{
    int ret = 0;
    if(resp == NULL || acme_info == NULL) return -1;

    //TODO：
    //1、根据获取到的 MQTT 信息，重新配置 MQTT Config 参考 mqtt_plugin_config
    //2、启动 MQTT 任务 acme_mqtt_plugin_start
    
    //MQTT addr rsa_decrypt
    char url[100] = {0};
    int port;
    ret = parse_mqtt_address(resp->mqttAddr,url,&port);
    if(ret < 0) { nlog_debug("recv mqttAddr parse error."); return -1;}

    nlog_debug("Recv MQTT addr:%s || port:%d ",url,port);

    /* 业务线程直接操作插件接口 ，不合法。容易引起资源冲突或者内存越界，需要遵循Neuron core 规范，发送消息到适配器进行MQTT 的配置重连
    mqtt_config_t config      = { 0 };
    memmove(&config, &plugin->config, sizeof(mqtt_config_t));    
    if(config.host) { free(config.host); }
    
    config.host = strdup(url);     //拷贝一份 host 内存
    config.port = port;

    ret = mqtt_plugin_config_reload(plugin,&config);      //重新加载MQTT 配置并启动 MQTT 任务
    if(ret != 0){
        nlog_debug("acme mqtt reload error. ");
        mqtt_config_fini(&config);
        return -1;
    }
    */

     //TODO: 修改MQTT 地址、用户名、密码，发往适配器
    //...
    //acme_mqtt_setting_info 中关于 MQTT 地址和端口 修改后 发送config 配置消息到适配器
    acme_mqtt_setting_modify(resp->username, resp->password,url,port,resp->time);
    acme_mqtt_setting_refresh_req(acme_info);       //请求适配器进行 MQTT 参数配置刷新
    
    acme_mqtt_start_req();  //启动 MQTT 连接
   

    return ret;
}



/*
*函数:acme_server_connect_thread
*功能: acme mqtt 平台连接注册线程
*输入: 插件句柄
*输出：无
*/
void * acme_server_connect_thread(void *arg)
{
    neu_plugin_t *plugin = (neu_plugin_t *)arg;
   
    nlog_debug("acme_server_connect_thread start.");
    ft_http_client_t* http = 0;
    gw_register_resp_t * resp = NULL;

    gw_register_reg_t *req = create_req_content();
    if(req == NULL){
        nlog_debug("create_req_content error!");
        return NULL;
    }
    http = ft_http_new();
    
    neu_msleep(1000);   //等待插件初始化完成后 
    /*int ret = get_node_setting_req(plugin);   
    if(ret < 0) {
         nlog_debug("setting set error.");
    } */

    while(1){

        if(http == NULL){
            http = ft_http_new();
        }

        if(resp != NULL) {            
            neu_msleep(20000);
            continue;
        }

        resp = request_gw_register(req, http, "http://192.168.109.102/bec/v3/device/register");
        if(resp != NULL){
            //TODO： 进行 code 验证，并取MQTT 信息启动连接任务
            //...
            if(resp->code == 200 && resp->mqttAddr != NULL){
                nlog_debug("ACME MQTT connect task start...");
                //启动 MQTT 任务
                acme_mqtt_process_start(resp, acme_plugin);
                continue;

            }else{
                nlog_debug("please check error msg :%s",resp->msg);
            }

            destory_gw_register_info(resp);
            resp = NULL;            
        }

        //下面是 公网 http 测试链接，发送数据原样返回
        //request_gw_register(req, http, "http://httpbin.org/post");
       
        neu_msleep(5000); 
    }

    if (http) {
        ft_http_destroy(http);
    }
    ft_http_deinit(); 
    destory_req_content(req);

    return NULL;
}