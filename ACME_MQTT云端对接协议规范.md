# ACME_MQTT 云端对接协议规范 v1.0

## 📋 协议概述

### 设计原则
- **简洁性**: 主题结构简单，易于理解和维护
- **通用性**: 与具体业务解耦，支持多种设备类型
- **扩展性**: 支持未来功能扩展，向后兼容
- **标准化**: 遵循MQTT协议标准，支持QoS和retain机制

### 协议版本
- **当前版本**: v1.0
- **兼容性**: 向后兼容，支持版本协商

## 🌐 主题结构设计

### 基础主题格式
```
/acme/{gateway_id}/{device_id}/{message_type}
```

### 主题分类

#### 1. 数据读取主题
```
下行 (云端→网关): /acme/{gateway_id}/{device_id}/read/request 
上行 (网关→云端): /acme/{gateway_id}/{device_id}/read/response
```

#### 2. 数据写入主题
```
下行 (云端→网关): /acme/{gateway_id}/{device_id}/write/request
上行 (网关→云端): /acme/{gateway_id}/{device_id}/write/response
```

#### 3. 事件通知主题
```
上行 (网关→云端): /acme/{gateway_id}/{device_id}/event/notify
下行 (云端→网关): /acme/{gateway_id}/{device_id}/event/ack
```

#### 4. 系统响应主题
```
双向通用响应: /acme/{gateway_id}/{device_id}/response/{request_id}
```

### 主题示例
```
/acme/SPT_GW_001/FCM_001/read/request
/acme/SPT_GW_001/FCM_001/write/response
/acme/SPT_GW_001/FCM_001/event/notify
/acme/SPT_GW_001/ALL/read/request        # 广播读取
```

## 📊 消息格式规范

### 通用消息结构
```json
{
    "version": "1.0",
    "timestamp": 1703123456789,
    "request_id": "req_20231221_001",
    "message_type": "read_request|write_request|event_notify|response",
    "source": "gateway|cloud",
    "target": "device_id|ALL",
    "data": { ... }
}
```

### 字段说明
- **version**: 协议版本号
- **timestamp**: Unix时间戳(毫秒)
- **request_id**: 请求唯一标识符
- **message_type**: 消息类型
- **source**: 消息来源
- **target**: 目标设备(ALL表示广播)
- **data**: 具体数据内容

## 🔍 读取协议

### 读取请求 (云端→网关)
**主题**: `/acme/{gateway_id}/{device_id}/read/request`
**QoS**: 1
**Retain**: false

```json
{
    "version": "1.0",
    "timestamp": 1703123456789,
    "request_id": "read_req_001",
    "message_type": "read_request",
    "source": "cloud",
    "target": "FCM_001",
    "data": {
        "tags": ["ONOFF", "STEMP", "RTEMP", "SMODE"],
        "group": "default_group"
    }
}
```

### 读取响应 (网关→云端)
**主题**: `/acme/{gateway_id}/{device_id}/read/response`
**QoS**: 1
**Retain**: false

```json
{
    "version": "1.0",
    "timestamp": 1703123456790,
    "request_id": "read_req_001",
    "message_type": "read_response",
    "source": "gateway",
    "target": "cloud",
    "data": {
        "result": "success|error",
        "error_code": 0,
        "error_message": "",
        "tags": [
            {
                "name": "ONOFF",
                "value": 1,
                "type": "int32",
                "quality": "good",
                "timestamp": 1703123456789
            },
            {
                "name": "STEMP",
                "value": 25.5,
                "type": "float",
                "quality": "good",
                "timestamp": 1703123456789
            }
        ]
    }
}
```

## ✏️ 写入协议

### 写入请求 (云端→网关)
**主题**: `/acme/{gateway_id}/{device_id}/write/request`
**QoS**: 1
**Retain**: false

```json
{
    "version": "1.0",
    "timestamp": 1703123456789,
    "request_id": "write_req_001",
    "message_type": "write_request",
    "source": "cloud",
    "target": "FCM_001",
    "data": {
        "tags": [
            {
                "name": "ONOFF",
                "value": 1,
                "type": "int32"
            },
            {
                "name": "STEMP",
                "value": 26.0,
                "type": "float"
            }
        ]
    }
}
```

### 写入响应 (网关→云端)
**主题**: `/acme/{gateway_id}/{device_id}/write/response`
**QoS**: 1
**Retain**: false

```json
{
    "version": "1.0",
    "timestamp": 1703123456790,
    "request_id": "write_req_001",
    "message_type": "write_response",
    "source": "gateway",
    "target": "cloud",
    "data": {
        "result": "success|error",
        "error_code": 0,
        "error_message": "",
        "tags": [
            {
                "name": "ONOFF",
                "result": "success",
                "error_code": 0
            },
            {
                "name": "STEMP",
                "result": "success",
                "error_code": 0
            }
        ]
    }
}
```

## 📢 事件协议

### 事件通知 (网关→云端)
**主题**: `/acme/{gateway_id}/{device_id}/event/notify`
**QoS**: 1
**Retain**: false

```json
{
    "version": "1.0",
    "timestamp": 1703123456789,
    "request_id": "event_001",
    "message_type": "event_notify",
    "source": "gateway",
    "target": "cloud",
    "data": {
        "event_type": "data_change|alarm|status_change|device_online|device_offline",
        "severity": "info|warning|error|critical",
        "description": "设备FCM_001温度超过阈值",
        "tags": [
            {
                "name": "RTEMP",
                "value": 35.5,
                "type": "float",
                "threshold": 30.0,
                "timestamp": 1703123456789
            }
        ],
        "metadata": {
            "device_type": "FCM",
            "location": "Room_A01",
            "group": "default_group"
        }
    }
}
```

### 事件确认 (云端→网关)
**主题**: `/acme/{gateway_id}/{device_id}/event/ack`
**QoS**: 1
**Retain**: false

```json
{
    "version": "1.0",
    "timestamp": 1703123456790,
    "request_id": "event_001",
    "message_type": "event_ack",
    "source": "cloud",
    "target": "gateway",
    "data": {
        "result": "acknowledged",
        "action": "none|escalate|suppress",
        "message": "事件已接收并处理"
    }
}
```

## 🔄 响应协议

### 通用响应格式
**主题**: `/acme/{gateway_id}/{device_id}/response/{request_id}`
**QoS**: 1
**Retain**: false

```json
{
    "version": "1.0",
    "timestamp": 1703123456790,
    "request_id": "original_request_id",
    "message_type": "response",
    "source": "gateway|cloud",
    "target": "cloud|gateway",
    "data": {
        "result": "success|error|timeout|not_found",
        "error_code": 0,
        "error_message": "",
        "response_data": { ... }
    }
}
```

## 🚨 错误码定义

### 系统错误码 (1000-1999)
- **1000**: 成功
- **1001**: 未知错误
- **1002**: 参数错误
- **1003**: 权限不足
- **1004**: 请求超时
- **1005**: 协议版本不支持

### 设备错误码 (2000-2999)
- **2000**: 设备不存在
- **2001**: 设备离线
- **2002**: 设备忙碌
- **2003**: 设备故障
- **2004**: 设备不支持该操作

### 点位错误码 (3000-3999)
- **3000**: 点位不存在
- **3001**: 点位只读
- **3002**: 点位类型错误
- **3003**: 点位值超出范围
- **3004**: 点位质量不良

### 通信错误码 (4000-4999)
- **4000**: 网络连接失败
- **4001**: MQTT连接断开
- **4002**: 消息发送失败
- **4003**: 消息格式错误
- **4004**: 消息超时

## 🔧 QoS和Retain策略

### QoS级别选择
- **QoS 0**: 系统状态、心跳等非关键数据
- **QoS 1**: 数据读写、事件通知等重要数据
- **QoS 2**: 关键控制命令、安全相关数据

### Retain策略
- **Retain = true**: 设备状态、配置信息
- **Retain = false**: 实时数据、事件通知、命令响应

## 📈 协议优势

### 1. 简洁性
- 主题结构清晰，只有4个基本类型
- 消息格式统一，易于解析和处理
- 减少了主题数量，降低了复杂度

### 2. 通用性
- 与具体业务逻辑解耦
- 支持多种设备类型和协议
- 标准化的错误处理机制

### 3. 扩展性
- 支持协议版本升级
- 预留扩展字段
- 支持自定义事件类型

### 4. 可靠性
- 完整的错误码体系
- 请求-响应机制保证消息可达
- 支持消息重传和超时处理

## 🎯 实施建议

### 阶段1: 基础实现
1. 实现基本的读写协议
2. 支持标准数据类型
3. 基础错误处理

### 阶段2: 功能完善
1. 实现事件通知机制
2. 支持批量操作
3. 添加设备状态管理

### 阶段3: 高级特性
1. 支持协议版本协商
2. 实现消息缓存和重传
3. 添加安全认证机制

这套协议设计既保持了简洁性，又具备了良好的扩展性，能够满足ACME系统当前和未来的需求。

---

# ACME_MQTT模块代码修改建议

## 📋 修改概述

基于新的协议规范，需要对现有ACME_MQTT模块进行以下主要修改：

1. **主题管理重构**: 统一主题格式和路由机制
2. **消息处理增强**: 支持新的消息类型和格式
3. **协议版本管理**: 添加版本协商和兼容性处理
4. **错误处理完善**: 实现标准化错误码体系

## 🔧 具体修改方案

### 1. 配置结构扩展

**文件**: `mqtt_config.h`

```c
// 新增协议配置结构
typedef struct {
    char *protocol_version;           // 协议版本
    char *gateway_id;                 // 网关ID
    bool enable_read_write;           // 启用读写功能
    bool enable_events;               // 启用事件通知
    uint32_t request_timeout;         // 请求超时时间(ms)
    uint32_t max_retry_count;         // 最大重试次数
} acme_protocol_config_t;

// 扩展MQTT配置结构
typedef struct {
    // ... 原有字段
    acme_protocol_config_t protocol;  // 协议配置

    // 新增主题配置
    char *read_request_topic_template;    // 读请求主题模板
    char *read_response_topic_template;   // 读响应主题模板
    char *write_request_topic_template;   // 写请求主题模板
    char *write_response_topic_template;  // 写响应主题模板
    char *event_notify_topic_template;    // 事件通知主题模板
    char *event_ack_topic_template;       // 事件确认主题模板
} mqtt_config_t;
```

### 2. 消息类型定义

**文件**: `acme_mqtt_plugin.h`

```c
// 新增消息类型枚举
typedef enum {
    ACME_MSG_READ_REQUEST = 0,
    ACME_MSG_READ_RESPONSE,
    ACME_MSG_WRITE_REQUEST,
    ACME_MSG_WRITE_RESPONSE,
    ACME_MSG_EVENT_NOTIFY,
    ACME_MSG_EVENT_ACK,
    ACME_MSG_RESPONSE
} acme_message_type_e;

// 新增协议消息结构
typedef struct {
    char version[16];                 // 协议版本
    int64_t timestamp;                // 时间戳
    char request_id[64];              // 请求ID
    acme_message_type_e message_type; // 消息类型
    char source[32];                  // 消息来源
    char target[64];                  // 目标设备
    char *data_json;                  // JSON数据
} acme_protocol_message_t;

// 新增主题生成器结构
typedef struct {
    char *(*generate_read_request_topic)(const char *gateway_id, const char *device_id);
    char *(*generate_read_response_topic)(const char *gateway_id, const char *device_id);
    char *(*generate_write_request_topic)(const char *gateway_id, const char *device_id);
    char *(*generate_write_response_topic)(const char *gateway_id, const char *device_id);
    char *(*generate_event_notify_topic)(const char *gateway_id, const char *device_id);
    char *(*generate_event_ack_topic)(const char *gateway_id, const char *device_id);
    char *(*generate_response_topic)(const char *gateway_id, const char *device_id, const char *request_id);
} acme_topic_generator_t;
```

### 3. 主题管理重构

**新增文件**: `acme_topic_manager.c`

```c
#include "acme_mqtt_plugin.h"

// 主题模板
static const char *TOPIC_TEMPLATES[] = {
    [ACME_MSG_READ_REQUEST]   = "/acme/%s/%s/read/request",
    [ACME_MSG_READ_RESPONSE]  = "/acme/%s/%s/read/response",
    [ACME_MSG_WRITE_REQUEST]  = "/acme/%s/%s/write/request",
    [ACME_MSG_WRITE_RESPONSE] = "/acme/%s/%s/write/response",
    [ACME_MSG_EVENT_NOTIFY]   = "/acme/%s/%s/event/notify",
    [ACME_MSG_EVENT_ACK]      = "/acme/%s/%s/event/ack",
    [ACME_MSG_RESPONSE]       = "/acme/%s/%s/response/%s"
};

// 生成主题
char *acme_generate_topic(acme_message_type_e type, const char *gateway_id,
                         const char *device_id, const char *request_id)
{
    char *topic = NULL;

    switch (type) {
    case ACME_MSG_RESPONSE:
        if (request_id) {
            asprintf(&topic, TOPIC_TEMPLATES[type], gateway_id, device_id, request_id);
        }
        break;
    default:
        asprintf(&topic, TOPIC_TEMPLATES[type], gateway_id, device_id);
        break;
    }

    return topic;
}

// 解析主题
int acme_parse_topic(const char *topic, char **gateway_id, char **device_id,
                    acme_message_type_e *type, char **request_id)
{
    // 实现主题解析逻辑
    // 格式: /acme/{gateway_id}/{device_id}/{message_type}[/{request_id}]

    if (!topic || strncmp(topic, "/acme/", 6) != 0) {
        return -1;
    }

    // 使用strtok或正则表达式解析主题
    // ... 解析实现

    return 0;
}
```

### 4. 消息处理增强

**文件**: `mqtt_handle.c`

```c
// 新增协议消息处理函数
int handle_acme_protocol_message(neu_plugin_t *plugin, const char *topic,
                                const char *payload, size_t payload_len)
{
    int ret = NEU_ERR_SUCCESS;
    acme_protocol_message_t msg = {0};
    char *gateway_id = NULL, *device_id = NULL, *request_id = NULL;
    acme_message_type_e msg_type;

    // 解析主题
    ret = acme_parse_topic(topic, &gateway_id, &device_id, &msg_type, &request_id);
    if (ret != 0) {
        plog_error(plugin, "Failed to parse topic: %s", topic);
        return NEU_ERR_PARAM_IS_WRONG;
    }

    // 解析消息内容
    ret = acme_parse_protocol_message(payload, payload_len, &msg);
    if (ret != 0) {
        plog_error(plugin, "Failed to parse message payload");
        goto cleanup;
    }

    // 根据消息类型处理
    switch (msg_type) {
    case ACME_MSG_READ_REQUEST:
        ret = handle_read_request(plugin, gateway_id, device_id, &msg);
        break;
    case ACME_MSG_WRITE_REQUEST:
        ret = handle_write_request(plugin, gateway_id, device_id, &msg);
        break;
    case ACME_MSG_EVENT_ACK:
        ret = handle_event_ack(plugin, gateway_id, device_id, &msg);
        break;
    default:
        plog_warn(plugin, "Unsupported message type: %d", msg_type);
        ret = NEU_ERR_PARAM_IS_WRONG;
        break;
    }

cleanup:
    // 清理资源
    free(gateway_id);
    free(device_id);
    free(request_id);
    acme_free_protocol_message(&msg);

    return ret;
}

// 处理读取请求
static int handle_read_request(neu_plugin_t *plugin, const char *gateway_id,
                              const char *device_id, acme_protocol_message_t *msg)
{
    // 解析读取请求
    json_t *data = json_loads(msg->data_json, 0, NULL);
    if (!data) {
        return NEU_ERR_PARAM_IS_WRONG;
    }

    json_t *tags_array = json_object_get(data, "tags");
    json_t *group = json_object_get(data, "group");

    // 构造Neuron读取请求
    neu_reqresp_head_t header = {
        .type = NEU_REQ_READ_GROUP,
        .ctx  = plugin,
    };

    neu_req_read_group_t req = {0};
    strcpy(req.driver, device_id);
    strcpy(req.group, json_string_value(group));

    // 发送读取请求
    int ret = neu_plugin_op(plugin, header, &req);

    json_decref(data);
    return ret;
}

// 处理写入请求
static int handle_write_request(neu_plugin_t *plugin, const char *gateway_id,
                               const char *device_id, acme_protocol_message_t *msg)
{
    // 类似读取请求的处理逻辑
    // 解析写入数据，构造Neuron写入请求
    // ...

    return NEU_ERR_SUCCESS;
}

// 发送事件通知
int acme_send_event_notify(neu_plugin_t *plugin, const char *device_id,
                          const char *event_type, const char *description,
                          neu_resp_tag_value_t *tags, int n_tags)
{
    char *topic = NULL;
    char *payload = NULL;
    int ret = NEU_ERR_SUCCESS;

    // 生成主题
    topic = acme_generate_topic(ACME_MSG_EVENT_NOTIFY,
                               plugin->config.protocol.gateway_id, device_id, NULL);
    if (!topic) {
        return NEU_ERR_EINTERNAL;
    }

    // 构造消息
    acme_protocol_message_t msg = {
        .timestamp = neu_time_ms(),
        .message_type = ACME_MSG_EVENT_NOTIFY,
    };
    strcpy(msg.version, plugin->config.protocol.protocol_version);
    strcpy(msg.source, "gateway");
    strcpy(msg.target, "cloud");
    snprintf(msg.request_id, sizeof(msg.request_id), "event_%ld", msg.timestamp);

    // 构造事件数据
    json_t *event_data = json_object();
    json_object_set_new(event_data, "event_type", json_string(event_type));
    json_object_set_new(event_data, "description", json_string(description));

    // 添加标签数据
    json_t *tags_array = json_array();
    for (int i = 0; i < n_tags; i++) {
        json_t *tag_obj = json_object();
        json_object_set_new(tag_obj, "name", json_string(tags[i].tag));
        // 根据类型设置值
        switch (tags[i].value.type) {
        case NEU_TYPE_INT32:
            json_object_set_new(tag_obj, "value", json_integer(tags[i].value.i32));
            json_object_set_new(tag_obj, "type", json_string("int32"));
            break;
        case NEU_TYPE_FLOAT:
            json_object_set_new(tag_obj, "value", json_real(tags[i].value.f32));
            json_object_set_new(tag_obj, "type", json_string("float"));
            break;
        // ... 其他类型
        }
        json_object_set_new(tag_obj, "timestamp", json_integer(msg.timestamp));
        json_array_append_new(tags_array, tag_obj);
    }
    json_object_set_new(event_data, "tags", tags_array);

    msg.data_json = json_dumps(event_data, JSON_COMPACT);

    // 构造完整消息
    payload = acme_encode_protocol_message(&msg);
    if (!payload) {
        ret = NEU_ERR_EINTERNAL;
        goto cleanup;
    }

    // 发布消息
    ret = publish(plugin, NEU_MQTT_QOS1, topic, payload, strlen(payload));
    if (ret != 0) {
        plog_error(plugin, "Failed to publish event notify to topic: %s", topic);
    } else {
        plog_info(plugin, "Event notify sent: %s", event_type);
    }

cleanup:
    free(topic);
    free(payload);
    free(msg.data_json);
    json_decref(event_data);

    return ret;
}
```

### 5. 订阅管理更新

**文件**: `mqtt_plugin_intf.c`

```c
// 更新订阅函数
static int subscribe(neu_plugin_t *plugin, const mqtt_config_t *config)
{
    int ret = 0;
    char *topic = NULL;

    if (!plugin->client || !neu_mqtt_client_is_open(plugin->client)) {
        return NEU_ERR_MQTT_IS_NULL;
    }

    // 订阅读取请求主题
    topic = acme_generate_topic(ACME_MSG_READ_REQUEST,
                               config->protocol.gateway_id, "+", NULL);
    if (topic) {
        ret = neu_mqtt_client_subscribe(plugin->client, topic, NEU_MQTT_QOS1);
        plog_info(plugin, "Subscribed to read requests: %s", topic);
        free(topic);
    }

    // 订阅写入请求主题
    topic = acme_generate_topic(ACME_MSG_WRITE_REQUEST,
                               config->protocol.gateway_id, "+", NULL);
    if (topic) {
        ret = neu_mqtt_client_subscribe(plugin->client, topic, NEU_MQTT_QOS1);
        plog_info(plugin, "Subscribed to write requests: %s", topic);
        free(topic);
    }

    // 订阅事件确认主题
    topic = acme_generate_topic(ACME_MSG_EVENT_ACK,
                               config->protocol.gateway_id, "+", NULL);
    if (topic) {
        ret = neu_mqtt_client_subscribe(plugin->client, topic, NEU_MQTT_QOS1);
        plog_info(plugin, "Subscribed to event acks: %s", topic);
        free(topic);
    }

    return ret;
}

// 更新消息接收回调
static void message_cb(void *data, char *topic, char *payload, size_t len)
{
    neu_plugin_t *plugin = (neu_plugin_t *)data;

    plog_debug(plugin, "Received message on topic: %s", topic);

    // 使用新的协议消息处理函数
    int ret = handle_acme_protocol_message(plugin, topic, payload, len);
    if (ret != NEU_ERR_SUCCESS) {
        plog_error(plugin, "Failed to handle protocol message: %d", ret);
    }
}
```

## 📊 协议实现流程图

```mermaid
sequenceDiagram
    participant Cloud as 云端平台
    participant MQTT as MQTT Broker
    participant GW as ACME网关
    participant Device as FCM设备

    Note over Cloud,Device: 新协议数据读取流程

    Cloud->>MQTT: Publish读取请求
    Note right of Cloud: /acme/SPT_GW_001/FCM_001/read/request

    MQTT->>GW: 转发读取请求
    GW->>GW: 解析协议消息
    GW->>Device: 读取设备数据
    Device-->>GW: 返回数据

    GW->>GW: 构造响应消息
    GW->>MQTT: Publish读取响应
    Note right of GW: /acme/SPT_GW_001/FCM_001/read/response

    MQTT->>Cloud: 转发读取响应

    Note over Cloud,Device: 事件通知流程

    Device->>GW: 设备状态变化
    GW->>GW: 检测到事件
    GW->>MQTT: Publish事件通知
    Note right of GW: /acme/SPT_GW_001/FCM_001/event/notify

    MQTT->>Cloud: 转发事件通知
    Cloud->>MQTT: Publish事件确认
    Note right of Cloud: /acme/SPT_GW_001/FCM_001/event/ack

    MQTT->>GW: 转发事件确认
```

## 🎯 实施步骤

### 第一阶段: 基础框架
1. 实现新的主题管理器
2. 添加协议消息结构定义
3. 更新配置管理

### 第二阶段: 核心功能
1. 实现读写请求处理
2. 添加事件通知机制
3. 完善错误处理

### 第三阶段: 优化完善
1. 添加协议版本协商
2. 实现消息缓存和重传
3. 性能优化和测试

这套修改方案将现有的ACME_MQTT模块升级为支持新协议规范的版本，既保持了向后兼容性，又提供了更好的扩展性和维护性。
