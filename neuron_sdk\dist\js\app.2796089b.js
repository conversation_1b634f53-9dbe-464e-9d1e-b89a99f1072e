(function(e){function n(n){for(var a,o,i=n[0],s=n[1],u=n[2],l=0,d=[];l<i.length;l++)o=i[l],Object.prototype.hasOwnProperty.call(r,o)&&r[o]&&d.push(r[o][0]),r[o]=0;for(a in s)Object.prototype.hasOwnProperty.call(s,a)&&(e[a]=s[a]);h&&h(n);while(d.length)d.shift()();return c.push.apply(c,u||[]),t()}function t(){for(var e,n=0;n<c.length;n++){for(var t=c[n],a=!0,o=1;o<t.length;o++){var i=t[o];0!==r[i]&&(a=!1)}a&&(c.splice(n--,1),e=s(s.s=t[0]))}return e}var a={},o={app:0},r={app:0},c=[];function i(e){return s.p+"js/"+({}[e]||e)+"."+{"chunk-2d0c4d7b":"4c70d279","chunk-371400a5":"8d30356c","chunk-3b72ccb7":"45afaea0","chunk-5d2e48d2":"83383ef6","chunk-3e4b8485":"4c4475cc","chunk-321d270e":"5b093744","chunk-a1b3580c":"fdb27f47","chunk-be9e2788":"a8c07a61","chunk-5cfaa5c2":"5338f827","chunk-5e9a9aa7":"05158fef","chunk-6fb67a99":"81a65446","chunk-325e06e2":"25a1332d","chunk-d5975f04":"88515472","chunk-575dac77":"cf40f684","chunk-92f4f370":"37f44bf2","chunk-618f7912":"e82fdc43","chunk-67de72a8":"78550711","chunk-7b4ab6ba":"da42bdf6","chunk-c1b1e2a2":"dbc72ed0","chunk-11202032":"7809fd76","chunk-24713b76":"7f3e3534","chunk-18d731da":"f34356e0","chunk-032317e3":"cd8cf633","chunk-3030e2a2":"99fceb2a"}[e]+".js"}function s(n){if(a[n])return a[n].exports;var t=a[n]={i:n,l:!1,exports:{}};return e[n].call(t.exports,t,t.exports,s),t.l=!0,t.exports}s.e=function(e){var n=[],t={"chunk-371400a5":1,"chunk-3b72ccb7":1,"chunk-321d270e":1,"chunk-a1b3580c":1,"chunk-be9e2788":1,"chunk-5cfaa5c2":1,"chunk-5e9a9aa7":1,"chunk-6fb67a99":1,"chunk-325e06e2":1,"chunk-d5975f04":1,"chunk-575dac77":1,"chunk-92f4f370":1,"chunk-618f7912":1,"chunk-67de72a8":1,"chunk-7b4ab6ba":1,"chunk-11202032":1,"chunk-24713b76":1,"chunk-18d731da":1,"chunk-032317e3":1,"chunk-3030e2a2":1};o[e]?n.push(o[e]):0!==o[e]&&t[e]&&n.push(o[e]=new Promise((function(n,t){for(var a="css/"+({}[e]||e)+"."+{"chunk-2d0c4d7b":"31d6cfe0","chunk-371400a5":"b1aad3ec","chunk-3b72ccb7":"57d8da89","chunk-5d2e48d2":"31d6cfe0","chunk-3e4b8485":"31d6cfe0","chunk-321d270e":"2362a63b","chunk-a1b3580c":"be953b78","chunk-be9e2788":"be953b78","chunk-5cfaa5c2":"cd1e1fb7","chunk-5e9a9aa7":"25e0dcdb","chunk-6fb67a99":"8e581796","chunk-325e06e2":"65876b9d","chunk-d5975f04":"1af03f4f","chunk-575dac77":"24f21624","chunk-92f4f370":"838f5894","chunk-618f7912":"e269e1b7","chunk-67de72a8":"8a363a58","chunk-7b4ab6ba":"032f40e1","chunk-c1b1e2a2":"31d6cfe0","chunk-11202032":"98816b51","chunk-24713b76":"1e87ed30","chunk-18d731da":"1f260924","chunk-032317e3":"7a1a057c","chunk-3030e2a2":"644e9f3e"}[e]+".css",r=s.p+a,c=document.getElementsByTagName("link"),i=0;i<c.length;i++){var u=c[i],l=u.getAttribute("data-href")||u.getAttribute("href");if("stylesheet"===u.rel&&(l===a||l===r))return n()}var d=document.getElementsByTagName("style");for(i=0;i<d.length;i++){u=d[i],l=u.getAttribute("data-href");if(l===a||l===r)return n()}var h=document.createElement("link");h.rel="stylesheet",h.type="text/css",h.onload=n,h.onerror=function(n){var a=n&&n.target&&n.target.src||r,c=new Error("Loading CSS chunk "+e+" failed.\n("+a+")");c.code="CSS_CHUNK_LOAD_FAILED",c.request=a,delete o[e],h.parentNode.removeChild(h),t(c)},h.href=r;var m=document.getElementsByTagName("head")[0];m.appendChild(h)})).then((function(){o[e]=0})));var a=r[e];if(0!==a)if(a)n.push(a[2]);else{var c=new Promise((function(n,t){a=r[e]=[n,t]}));n.push(a[2]=c);var u,l=document.createElement("script");l.charset="utf-8",l.timeout=120,s.nc&&l.setAttribute("nonce",s.nc),l.src=i(e);var d=new Error;u=function(n){l.onerror=l.onload=null,clearTimeout(h);var t=r[e];if(0!==t){if(t){var a=n&&("load"===n.type?"missing":n.type),o=n&&n.target&&n.target.src;d.message="Loading chunk "+e+" failed.\n("+a+": "+o+")",d.name="ChunkLoadError",d.type=a,d.request=o,t[1](d)}r[e]=void 0}};var h=setTimeout((function(){u({type:"timeout",target:l})}),12e4);l.onerror=l.onload=u,document.head.appendChild(l)}return Promise.all(n)},s.m=e,s.c=a,s.d=function(e,n,t){s.o(e,n)||Object.defineProperty(e,n,{enumerable:!0,get:t})},s.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},s.t=function(e,n){if(1&n&&(e=s(e)),8&n)return e;if(4&n&&"object"===typeof e&&e&&e.__esModule)return e;var t=Object.create(null);if(s.r(t),Object.defineProperty(t,"default",{enumerable:!0,value:e}),2&n&&"string"!=typeof e)for(var a in e)s.d(t,a,function(n){return e[n]}.bind(null,a));return t},s.n=function(e){var n=e&&e.__esModule?function(){return e["default"]}:function(){return e};return s.d(n,"a",n),n},s.o=function(e,n){return Object.prototype.hasOwnProperty.call(e,n)},s.p="/web/",s.oe=function(e){throw console.error(e),e};var u=window["webpackJsonp"]=window["webpackJsonp"]||[],l=u.push.bind(u);u.push=n,u=u.slice();for(var d=0;d<u.length;d++)n(u[d]);var h=l;c.push([1,"chunk-vendors"]),t()})({0:function(e,n){},"0038":function(e,n){!function(e){var n,t,a,o,r,c='<svg><symbol id="iconkuiper" viewBox="0 0 1078 1024"><path d="M673.917313 251.730243H286.958278a17.380903 17.380903 0 1 1-1.705314-34.719054q0.855032-0.042752 1.705314 0h386.968535a17.380903 17.380903 0 1 1 1.705314 34.723804q-0.855032 0.038001-1.710064 0zM568.259094 417.183707H192.866744a17.380903 17.380903 0 0 1 0-34.723804h375.397101a17.380903 17.380903 0 0 1 1.705314 34.723804q-0.855032 0.038001-1.705314 0zM770.307934 417.183707h-107.29228a17.380903 17.380903 0 0 1-1.700564-34.723804q0.850282-0.038001 1.700564 0h107.29228a17.380903 17.380903 0 0 1 0 34.723804zM770.307934 748.000383h-375.397101a17.380903 17.380903 0 0 1 0-34.723804h375.397101a17.380903 17.380903 0 0 1 0 34.723804zM300.197026 748.000383H192.861994a17.380903 17.380903 0 0 1 0-34.723804h107.335032a17.380903 17.380903 0 0 1 0 34.723804zM673.917313 913.449098H286.958278a17.380903 17.380903 0 1 1 0-34.761806h386.968535a17.380903 17.380903 0 0 1 0 34.761806zM864.019453 582.58967H97.901177a17.380903 17.380903 0 0 1-1.705314-34.719054q0.855032-0.042752 1.710064 0H864.019453a17.380903 17.380903 0 1 1 1.700564 34.719054q-0.850282 0.042752-1.700564 0z" fill="#00B173" ></path></symbol><symbol id="iconidle" viewBox="0 0 1024 1024"><path d="M512 0C230.4 0 0 230.4 0 512s230.4 512 512 512 512-230.4 512-512S793.6 0 512 0zM409.6 716.8c0 15.36-10.24 25.6-25.6 25.6S358.4 732.16 358.4 716.8V307.2c0-15.36 10.24-25.6 25.6-25.6s25.6 10.24 25.6 25.6v409.6z m256 0c0 15.36-10.24 25.6-25.6 25.6s-25.6-10.24-25.6-25.6V307.2c0-15.36 10.24-25.6 25.6-25.6s25.6 10.24 25.6 25.6v409.6z" fill="#189BFE" ></path></symbol><symbol id="iconinit" viewBox="0 0 1024 1024"><path d="M614.4 522.24c0-56.32-46.08-102.4-102.4-102.4s-102.4 46.08-102.4 102.4 46.08 102.4 102.4 102.4 102.4-46.08 102.4-102.4z" fill="#314AE5" ></path><path d="M512 0C230.4 0 0 230.4 0 512s230.4 512 512 512 512-230.4 512-512S793.6 0 512 0z m-5.12 906.24c-174.08 0-327.68-117.76-378.88-281.6-5.12-15.36 5.12-25.6 15.36-30.72 15.36-5.12 25.6 5.12 30.72 15.36 40.96 143.36 174.08 245.76 327.68 245.76 189.44 0 337.92-148.48 337.92-332.8s-153.6-332.8-337.92-332.8c-87.04 0-168.96 30.72-230.4 87.04-10.24 10.24-20.48 10.24-30.72 0-10.24-5.12-10.24-15.36-10.24-25.6L286.72 102.4c5.12-15.36 20.48-20.48 30.72-15.36s20.48 20.48 15.36 30.72L307.2 189.44c61.44-35.84 128-51.2 194.56-51.2 215.04 0 389.12 174.08 389.12 384 5.12 209.92-168.96 384-384 384zM358.4 522.24c0-87.04 66.56-153.6 153.6-153.6s153.6 66.56 153.6 153.6-66.56 153.6-153.6 153.6-153.6-71.68-153.6-153.6z" fill="#314AE5" ></path></symbol><symbol id="iconready" viewBox="0 0 1024 1024"><path d="M358.4 378.88l153.6 97.28 158.72-97.28V271.36H358.4zM358.4 645.12v107.52h312.32v-107.52L512 547.84z" fill="#24273C" ></path><path d="M512 0C230.4 0 0 230.4 0 512s230.4 512 512 512 512-230.4 512-512S793.6 0 512 0z m209.92 619.52v153.6H819.2c15.36 0 25.6 10.24 25.6 25.6s-10.24 20.48-25.6 20.48H204.8c-15.36 0-25.6-10.24-25.6-25.6s10.24-25.6 25.6-25.6h102.4v-153.6l163.84-102.4L307.2 404.48V256H204.8c-15.36 0-25.6-15.36-25.6-25.6S189.44 204.8 204.8 204.8h614.4c15.36 0 25.6 10.24 25.6 25.6s-10.24 25.6-25.6 25.6h-97.28v153.6l-168.96 102.4 168.96 107.52z" fill="#24273C" ></path></symbol><symbol id="iconstopped" viewBox="0 0 1024 1024"><path d="M512 0C230.4 0 0 230.4 0 512s230.4 512 512 512 512-230.4 512-512S793.6 0 512 0z m66.56 204.8l-20.48 414.72H491.52L471.04 204.8h107.52z m-10.24 568.32c-15.36 10.24-25.6 15.36-46.08 15.36s-30.72-5.12-46.08-20.48c-10.24-10.24-15.36-20.48-15.36-40.96s5.12-30.72 20.48-46.08c10.24-10.24 25.6-15.36 46.08-15.36s35.84 5.12 46.08 15.36c10.24 10.24 20.48 25.6 20.48 46.08-5.12 20.48-10.24 35.84-25.6 46.08z" fill="#E85135" ></path></symbol><symbol id="iconrunning" viewBox="0 0 1024 1024"><path d="M512 0C230.4 0 0 230.4 0 512s230.4 512 512 512 512-230.4 512-512S793.6 0 512 0z m291.84 399.36L512 727.04c-5.12 5.12-10.24 10.24-20.48 10.24-5.12 0-15.36-5.12-20.48-10.24l-184.32-194.56c-10.24-10.24-10.24-25.6 0-35.84 10.24-10.24 25.6-10.24 35.84 0l163.84 174.08 271.36-307.2c10.24-10.24 25.6-10.24 35.84 0 20.48 10.24 20.48 25.6 10.24 35.84z" fill="#00B173" ></path></symbol><symbol id="iconsetting" viewBox="0 0 1024 1024"><path d="M603.1 892.9c-9.2 0-17.8-5.7-21.1-14.8-10.4-29-38.6-48.4-70-48.4-31.2 0-59.1 19.3-71 49.1-4.1 10.3-15.1 16.3-25.9 13.5-34.4-8.1-69.1-22.2-103.1-42.1-9.8-5.7-13.7-17.8-9.3-28.1 12.6-29.4 6.3-62.7-15.8-84.9-22.6-22.6-55.8-28.8-84.5-16-10.3 4.8-22.8 0.7-28.5-9.1-18.6-31.9-32.8-66.4-42-102.8-2.8-11 3.1-22.4 13.7-26.5 29.6-11.2 48.8-38.5 48.8-69.4 0-31.2-19.3-59.1-49.1-71.1-10.5-4.2-16.2-15.4-13.4-26.3 9.8-38.4 24.2-73.1 43.1-103.3 5.9-9.4 17.6-13 27.8-8.8 29 12.5 62.3 6.1 85-16 22.3-21.7 28.6-55.6 15.8-84.2-4.6-10.3-0.9-22.4 8.8-28.3 31.2-19 65.9-33.5 103.1-43 10.9-2.9 22.4 3.1 26.5 13.7 10.9 28.7 39.4 48 70.9 48 30.9 0 59-19.6 70.1-48.8 4.1-10.6 15.6-16.6 26.5-13.7 35 8.9 69.5 23.3 102.8 42.8 9.7 5.7 13.7 17.8 9.3 28.1-12.6 29.4-6.3 62.7 15.8 84.9 22.6 22.6 55.8 28.8 84.5 16 10.5-4.9 23-0.6 28.7 9.5 19.1 34.1 33.2 68.6 41.8 102.4 2.8 10.9-2.9 22.1-13.4 26.3-29.8 11.9-49.1 39.9-49.1 71.1 0 30.8 19.3 59 48.1 70.2 10.4 4 16.3 15.1 13.7 26-8.3 35.4-22.9 71.1-42.3 103.5-5.8 9.6-17.9 13.4-28 9.1-29.5-12.6-62.7-6.3-84.9 15.8-22.6 22.6-28.8 55.8-16 84.5 4.7 10.5 0.7 22.8-9.3 28.6-32.3 18.6-67.7 33-102.6 41.9-1.8 0.4-3.6 0.6-5.5 0.6z m-252.8-72.6c19.3 10.1 38.7 18.1 58 23.9 21.9-36.5 60.7-59.3 103.7-59.3s82.3 23 103.2 58.9c19.4-6.1 38.9-14 57.5-23.5-10.3-40.6 1.3-83.9 32.1-114.7 30.4-30.3 73.8-41.8 114.9-31.8 9.8-18.7 17.9-38.3 23.8-58-35.7-21.5-58.5-60.7-58.5-103 0-42.8 22.6-81.7 59-103.6-6-19.2-13.9-38.3-23.8-57.8-40.6 10.3-83.8-1.4-114.6-32.1-30.3-30.4-41.9-73.7-31.8-114.7-19-10-38.5-18.1-58-24.3-21.4 35.9-60.6 58.9-103 58.9-42.9 0-82.4-22.8-103.7-58.2-20.5 6.3-39.6 14.3-57.9 24 10.4 41-1.4 85.3-32.2 115.3-30.8 30-74.2 41.5-114.9 31.5-9.6 17.8-17.6 37.3-24.1 58.2 36.4 21.9 59 60.7 59 103.6 0 42.8-22.6 81-58.9 102.3 6.2 20 14 39.3 23.5 57.7 40.6-10.4 83.9 1.2 114.8 32 30.4 30.3 42 73.6 31.9 114.7zM512 633.6c-66.6 0-120.8-54.2-120.8-120.8 0-66.7 54.2-120.9 120.8-120.9s120.8 54.2 120.8 120.9c0 66.6-54.2 120.8-120.8 120.8z m0-196.9c-41.9 0-76 34.1-76 76.1s34.1 76 76 76 76-34.1 76-76-34.1-76.1-76-76.1z"  ></path></symbol><symbol id="iconconfig" viewBox="0 0 1024 1024"><path d="M847.5 903.4h-671c-30.8 0-55.9-25.1-55.9-55.9v-671c0-30.8 25.1-55.9 55.9-55.9h671c30.8 0 55.9 25.1 55.9 55.9v671c0 30.8-25.1 55.9-55.9 55.9z m-671-745.5c-10.3 0-18.6 8.4-18.6 18.6v671c0 10.3 8.4 18.6 18.6 18.6h671c10.3 0 18.6-8.4 18.6-18.6v-671c0-10.3-8.4-18.6-18.6-18.6h-671z"  ></path><path d="M393.4 666.2c-57 0-103.4-46.4-103.4-103.4 0-57 46.4-103.4 103.4-103.4s103.4 46.4 103.4 103.4c-0.1 57-46.4 103.4-103.4 103.4z m0-169.4c-36.4 0-66.1 29.6-66.1 66.1S357 629 393.4 629s66.1-29.6 66.1-66.1-29.7-66.1-66.1-66.1zM630.6 564.5c-57 0-103.4-46.4-103.4-103.4s46.4-103.4 103.4-103.4S734 404.2 734 461.2s-46.4 103.3-103.4 103.3z m0-169.4c-36.4 0-66.1 29.7-66.1 66.1s29.6 66.1 66.1 66.1 66.1-29.6 66.1-66.1-29.7-66.1-66.1-66.1z"  ></path><path d="M393.4 767.9c-10.3 0-18.6-8.3-18.6-18.6v-90.8c0-10.3 8.3-18.6 18.6-18.6s18.6 8.3 18.6 18.6v90.8c0 10.2-8.3 18.6-18.6 18.6z m0-285.4c-10.3 0-18.6-8.3-18.6-18.6v-189c0-10.3 8.3-18.6 18.6-18.6s18.6 8.3 18.6 18.6v189c0 10.2-8.3 18.6-18.6 18.6zM630.6 767.9c-10.3 0-18.6-8.3-18.6-18.6V546.7c0-10.3 8.3-18.6 18.6-18.6s18.6 8.3 18.6 18.6v202.6c0 10.2-8.3 18.6-18.6 18.6z m0-374.4c-10.3 0-18.6-8.3-18.6-18.6V274.8c0-10.3 8.3-18.6 18.6-18.6s18.6 8.3 18.6 18.6v100.1c0 10.2-8.3 18.6-18.6 18.6z"  ></path></symbol><symbol id="iconhome" viewBox="0 0 1024 1024"><path d="M857.6 904.3H586.5V651.2H439v253.1H166.4V420.8H79.5L512 115.5l432.5 305.3h-86.9v483.5zM623.8 867h196.4V383.4h6.5L512 161.2 197.2 383.4h6.5V867h197.9V613.8h222.2V867z"  ></path></symbol><symbol id="iconstatus" viewBox="0 0 1024 1024"><path d="M847.8 709.8H176.2c-30.9 0-56-25.1-56-56V176.2c0-30.9 25.1-56 56-56h671.7c30.9 0 56 25.1 56 56v477.7c-0.1 30.8-25.2 55.9-56.1 55.9zM176.2 157.5c-10.3 0-18.7 8.4-18.7 18.7v477.7c0 10.3 8.4 18.7 18.7 18.7h671.7c10.3 0 18.7-8.4 18.7-18.7V176.2c0-10.3-8.4-18.7-18.7-18.7H176.2z"  ></path><path d="M647.7 882.4H376.3c-10.3 0-18.7-8.3-18.7-18.7V691.1c0-10.3 8.3-18.7 18.7-18.7h271.4c10.3 0 18.7 8.3 18.7 18.7v172.6c0 10.4-8.4 18.7-18.7 18.7zM395 845.1h234V709.8H395v135.3z"  ></path><path d="M770 886.5H227.2c-10.3 0-18.7-8.3-18.7-18.7s8.3-18.7 18.7-18.7H770c10.3 0 18.7 8.3 18.7 18.7s-8.4 18.7-18.7 18.7zM287 595.3c-3.7 0-7.4-1.1-10.6-3.4-8.5-5.9-10.6-17.5-4.7-25.9l125.7-180.8c5.4-7.8 15.9-10.3 24.3-5.7l153.2 83.7L722 294.5c6.7-7.8 18.5-8.6 26.3-1.8 7.8 6.8 8.6 18.6 1.8 26.3L593.2 499c-5.8 6.6-15.3 8.3-23 4.1l-151.6-82.9-116.1 167a18.9 18.9 0 0 1-15.5 8.1z"  ></path></symbol><symbol id="iconAdministration1" viewBox="0 0 1024 1024"><path d="M512 479.12c-113.2 0-205.2-92.1-205.2-205.2s92-205.2 205.2-205.2c113.1 0 205.2 92.1 205.2 205.2s-92.1 205.2-205.2 205.2z m0-373.1c-92.6 0-167.9 75.3-167.9 167.9s75.3 167.9 167.9 167.9 167.9-75.3 167.9-167.9-75.3-167.9-167.9-167.9z"  ></path><path d="M512 926.92c-109.2 0-211.2-11.9-287.3-33.5-117.2-33.3-141.8-81.6-141.8-116.3 0-184.9 192.5-335.3 429.1-335.3 236.6 0 429.1 150.4 429.1 335.3 0 110.6-231.2 149.8-429.1 149.8z m0-447.8c-216 0-391.8 133.6-391.8 298 0 63.1 172.1 112.5 391.8 112.5s391.8-49.4 391.8-112.5c0-164.3-175.7-298-391.8-298z"  ></path></symbol><symbol id="icondownload" viewBox="0 0 1024 1024"><path d="M512 698.193c-11.44 0-20.69-9.25-20.69-20.69v-441.37c0-11.44 9.25-20.69 20.69-20.69s20.69 9.25 20.69 20.69v441.37c0 11.44-9.25 20.69-20.69 20.69zM843.02 843.013H180.98c-11.44 0-20.69-9.25-20.69-20.69 0-11.44 9.25-20.69 20.69-20.69h662.04c11.44 0 20.69 9.25 20.69 20.69 0 11.44-9.26 20.69-20.69 20.69z"  ></path><path d="M512 698.2c-5.29 0-10.59-2.02-14.63-6.06L341.32 536.08c-8.08-8.08-8.08-21.17 0-29.25 8.08-8.08 21.17-8.08 29.26 0L512 648.25l141.43-141.43c8.08-8.08 21.17-8.08 29.26 0 8.08 8.08 8.08 21.17 0 29.25L526.63 692.14A20.634 20.634 0 0 1 512 698.2z"  ></path></symbol><symbol id="iconlanguage" viewBox="0 0 1024 1024"><path d="M512 131.77c-209.67 0-380.23 170.57-380.23 380.23 0 209.64 170.57 380.23 380.23 380.23 209.64 0 380.23-170.59 380.23-380.23 0-209.67-170.59-380.23-380.23-380.23z m0 715.73c-32.32 0-66.97-53.77-89-139.53 28.46 3.49 58.25 5.33 89 5.33s60.53-1.84 88.99-5.33C578.96 793.73 544.3 847.5 512 847.5z m0-178.93c-34.32 0-67.64-2.56-99.11-7.27-7.99-44.44-12.72-94.91-12.72-149.3 0-54.38 4.74-104.86 12.73-149.29 31.47-4.71 64.78-7.27 99.11-7.27 34.32 0 67.64 2.56 99.1 7.27 7.99 44.44 12.73 94.92 12.73 149.29 0 54.38-4.74 104.86-12.73 149.3-31.47 4.71-64.79 7.27-99.11 7.27zM366.1 652.4C254.96 626.56 176.5 572.65 176.5 512c0-60.64 78.46-114.55 189.59-140.39-7.06 45.23-10.66 93.19-10.66 140.39 0 47.2 3.6 95.16 10.67 140.4zM512 176.5c32.3 0 66.96 53.77 88.99 139.53-28.46-3.48-58.24-5.33-88.99-5.33-30.75 0-60.53 1.84-88.99 5.33C445.03 230.28 479.68 176.5 512 176.5z m145.91 195.11C769.04 397.45 847.5 451.36 847.5 512c0 60.65-78.46 114.56-189.6 140.4 7.06-45.24 10.66-93.2 10.66-140.4 0.01-47.2-3.59-95.16-10.65-140.39z m169.75 26.71c-43.03-33.83-105.13-60.04-178.79-74.66-12.1-54.24-29.69-102.07-52.48-136.41 107.36 27.92 193.97 107.8 231.27 211.07zM427.61 187.25c-22.79 34.34-40.38 82.18-52.48 136.41-73.66 14.62-135.76 40.83-178.79 74.66 37.3-103.27 123.91-183.15 231.27-211.07zM196.34 625.68c43.03 33.83 105.13 60.04 178.79 74.66 12.1 54.23 29.69 102.07 52.48 136.41-107.36-27.92-193.97-107.8-231.27-211.07z m400.05 211.07c22.79-34.35 40.38-82.18 52.48-136.41 73.66-14.62 135.76-40.83 178.79-74.66-37.3 103.27-123.91 183.15-231.27 211.07z"  ></path></symbol><symbol id="iconLicense" viewBox="0 0 1024 1024"><path d="M825.13 173.7H198.87c-37 0-67.1 30.1-67.1 67.1v581.53c0 37 30.1 67.1 67.1 67.1h626.27c37 0 67.1-30.1 67.1-67.1V240.8c-0.01-37-30.11-67.1-67.11-67.1z m22.37 648.64c0 12.32-10.05 22.37-22.37 22.37H198.87c-12.34 0-22.37-10.05-22.37-22.37V240.8c0-12.34 10.03-22.37 22.37-22.37h626.27c12.32 0 22.37 10.03 22.37 22.37v581.54z"  ></path><path d="M355.43 309.3h-89.47c-12.36 0-22.37 10-22.37 22.37 0 12.36 10 22.37 22.37 22.37h89.47c12.36 0 22.37-10 22.37-22.37 0-12.36-10-22.37-22.37-22.37zM646.2 265.97c-86.32 0-156.57 70.25-156.57 156.57 0 42.87 17.33 81.77 45.34 110.07-0.4 1.67-0.61 3.4-0.61 5.19v197.87c0 7.86 4.13 15.16 10.88 19.18 6.73 4.06 15.16 4.24 22.02 0.57l78.94-42.2 78.94 42.2a22.35 22.35 0 0 0 10.53 2.62c3.98 0 7.95-1.05 11.49-3.19 6.77-4.02 10.88-11.31 10.88-19.18V537.8c0-1.79-0.22-3.52-0.61-5.19 28.01-28.3 45.34-67.2 45.34-110.07 0-86.33-70.25-156.57-156.57-156.57z m0 44.73c61.68 0 111.83 50.17 111.83 111.83S707.88 534.37 646.2 534.37 534.37 484.2 534.37 422.54 584.52 310.7 646.2 310.7z m10.53 357.39c-6.55-3.49-14.5-3.49-21.06 0l-56.57 30.27V563.97c20.35 9.69 43.1 15.13 67.1 15.13 24 0 46.75-5.44 67.1-15.13v134.39l-56.57-30.27zM355.43 667.17h-89.47c-12.36 0-22.37 10-22.37 22.37s10 22.37 22.37 22.37h89.47c12.36 0 22.37-10 22.37-22.37s-10-22.37-22.37-22.37zM355.43 488.24h-89.47c-12.36 0-22.37 10-22.37 22.37 0 12.36 10 22.37 22.37 22.37h89.47c12.36 0 22.37-10 22.37-22.37 0-12.37-10-22.37-22.37-22.37z"  ></path></symbol><symbol id="iconlogout" viewBox="0 0 1024 1024"><path d="M825.85 534.42H287.82c-12.39 0-22.42-10.03-22.42-22.42s10.03-22.42 22.42-22.42h538.03c12.39 0 22.42 10.03 22.42 22.42s-10.03 22.42-22.42 22.42z"  ></path><path d="M601.67 848.27H183.2c-29.34 0-52.3-27.06-52.3-61.65V237.38c0-34.57 22.97-61.65 52.3-61.65h418.47c12.39 0 22.42 10.03 22.42 22.42s-10.03 22.42-22.42 22.42H183.2c-1.86 0-7.47 5.95-7.47 16.81v549.23c0 10.86 5.6 16.81 7.47 16.81h418.47c12.39 0 22.42 10.03 22.42 22.42s-10.03 22.43-22.42 22.43zM723.04 692.92c-5.74 0-11.47-2.19-15.85-6.57-8.76-8.76-8.76-22.94 0-31.7L849.84 512 707.19 369.33c-8.76-8.76-8.76-22.94 0-31.7 8.76-8.76 22.94-8.76 31.7 0l158.5 158.52c8.76 8.76 8.76 22.94 0 31.7l-158.5 158.5a22.336 22.336 0 0 1-15.85 6.57z"  ></path></symbol><symbol id="iconsubmit" viewBox="0 0 1024 1024"><path d="M342.93 355.08c5.74 0 11.47-2.19 15.85-6.57l130.8-130.81v473.64c0 12.39 10.03 22.42 22.42 22.42s22.42-10.03 22.42-22.42V217.7l130.83 130.81c8.76 8.76 22.94 8.76 31.7 0 8.76-8.76 8.76-22.94 0-31.7l-169.1-169.07c-8.76-8.76-22.94-8.76-31.7 0L327.08 316.81c-8.76 8.76-8.76 22.94 0 31.7 4.37 4.38 10.11 6.57 15.85 6.57zM870.68 825.85H153.32c-12.39 0-22.42 10.03-22.42 22.42s10.03 22.42 22.42 22.42h717.37c12.39 0 22.42-10.03 22.42-22.42s-10.04-22.42-22.43-22.42z"  ></path></symbol><symbol id="iconabout" viewBox="0 0 1024 1024"><path d="M780.67 86.61H243.33c-37.04 0-67.17 30.13-67.17 67.17V915c0 7.61 3.85 14.69 10.23 18.8 3.67 2.36 7.91 3.59 12.16 3.59 3.13 0 6.25-0.66 9.18-1.97l307.28-138.09 301.11 138.01c6.95 3.28 15 2.62 21.43-1.49A22.481 22.481 0 0 0 847.83 915V153.78c0-37.04-30.13-67.17-67.16-67.17z m22.38 793.49L524.44 752.42a22.604 22.604 0 0 0-18.52-0.09L220.95 880.41V153.78c0-12.35 10.04-22.39 22.39-22.39h537.33c12.33 0 22.39 10.04 22.39 22.39V880.1z"  ></path><path d="M691.11 220.95H332.89c-12.38 0-22.39 10.01-22.39 22.39s10.01 22.39 22.39 22.39h358.22c12.38 0 22.39-10.01 22.39-22.39s-10.01-22.39-22.39-22.39zM691.11 579.17H332.89c-12.38 0-22.39 10.01-22.39 22.39s10.01 22.39 22.39 22.39h358.22c12.38 0 22.39-10.01 22.39-22.39s-10.01-22.39-22.39-22.39zM691.11 400.06H332.89c-12.38 0-22.39 10.01-22.39 22.39s10.01 22.39 22.39 22.39h358.22c12.38 0 22.39-10.01 22.39-22.39s-10.01-22.39-22.39-22.39z"  ></path></symbol><symbol id="iconattributed" viewBox="0 0 1024 1024"><path d="M833.4 313.5L523.19 134.39a22.485 22.485 0 0 0-22.39 0L190.58 313.5a22.38 22.38 0 0 0-11.19 19.39v358.22c0 8 4.26 15.39 11.19 19.37L484.89 880.4c3.7 7.25 11.24 12.21 19.94 12.21 1.23 0 2.44-0.1 3.62-0.3 1.17 0.19 2.36 0.3 3.55 0.3 3.87 0 7.74-1.01 11.19-3.02L833.4 710.48a22.364 22.364 0 0 0 11.19-19.37V332.89c0.01-8-4.28-15.39-11.19-19.39zM512 179.62l266.86 154.09-273.75 150.11-259.99-150.11L512 179.62z m-287.84 193.7l258.28 149.12v304.87L224.16 678.17V373.32z m303.06 462.27V522.76l272.6-149.48v304.88l-272.6 157.43z"  ></path></symbol><symbol id="iconuseradmin" viewBox="0 0 1024 1024"><path d="M772.45 810.23c-11.98 4.72-28.38 10.45-48.06 16.88-122.48 32.66-443.27 21.69-531.36-46-11.17-8.61-16.86-17.14-16.86-25.36 0-140.02 150.64-253.91 335.83-253.91 68.74 0 134.81 15.65 191.18 45.28 10.89 5.73 24.49 1.57 30.22-9.38 5.77-10.95 1.57-24.49-9.4-30.24-32.91-17.32-68.76-30.34-106.37-38.82 48.08-33.5 79.61-89.17 79.61-152.07 0-102.13-83.08-185.21-185.23-185.21-102.13 0-185.21 83.08-185.21 185.21 0 62.94 31.57 118.65 79.69 152.14-158.7 35.99-275.09 151.04-275.09 287 0 22.87 11.57 43.33 34.35 60.87 65.18 50.07 228.2 73.07 372.28 73.07 78.14 0 150.69-6.73 199.05-19.63 21.95-7.13 39.22-13.21 51.82-18.19 11.5-4.55 17.14-17.54 12.59-29.04-4.56-11.51-17.59-17.24-29.04-12.6zM371.57 316.6c0-77.44 62.99-140.43 140.43-140.43s140.46 62.99 140.46 140.43S589.44 457.06 512 457.06 371.57 394.04 371.57 316.6z"  ></path><path d="M870.22 646.33H691.11c-12.38 0-22.39 10.01-22.39 22.39s10.01 22.39 22.39 22.39h179.11c12.38 0 22.39-10.01 22.39-22.39 0-12.37-10.01-22.39-22.39-22.39zM668.72 579.17c0 12.38 10.01 22.39 22.39 22.39h179.11c12.38 0 22.39-10.01 22.39-22.39s-10.01-22.39-22.39-22.39H691.11c-12.37 0-22.39 10.01-22.39 22.39zM870.22 735.89H691.11c-12.38 0-22.39 10.01-22.39 22.39s10.01 22.39 22.39 22.39h179.11c12.38 0 22.39-10.01 22.39-22.39s-10.01-22.39-22.39-22.39z"  ></path></symbol><symbol id="iconcreate" viewBox="0 0 1024 1024"><path d="M512 892.91c-12.38 0-22.41-10.02-22.41-22.41V153.49c0-12.38 10.02-22.41 22.41-22.41s22.41 10.02 22.41 22.41V870.5c0 12.39-10.03 22.41-22.41 22.41z"  ></path><path d="M870.51 534.41H153.49c-12.38 0-22.41-10.02-22.41-22.41s10.02-22.41 22.41-22.41H870.5c12.38 0 22.41 10.02 22.41 22.41s-10.02 22.41-22.4 22.41z"  ></path></symbol><symbol id="iconpassword" viewBox="0 0 1024 1024"><path d="M703.11 231.41c-49.63 0-90.02 40.37-90.02 90s40.39 90.02 90.02 90.02 90.02-40.39 90.02-90.02c0-49.62-40.39-90-90.02-90z m0 135.21c-24.94 0-45.21-20.28-45.21-45.21 0-24.92 20.26-45.19 45.21-45.19s45.21 20.26 45.21 45.19c0 24.93-20.26 45.21-45.21 45.21z"  ></path><path d="M650.77 131.09c-132.6 0-240.48 107.9-240.48 240.5 0 22.06 3 43.87 8.95 65.01l-253.3 251.42c-22.47 22.49-34.86 52.38-34.86 84.16s12.38 61.66 34.86 84.16c23.22 23.19 53.72 34.79 84.2 34.79 30.46 0 60.92-11.55 84.09-34.75l11.62-11.51h43.41c24.57 0 44.57-20 44.57-44.55v-29.06h29.04c24.57 0 44.55-20 44.55-44.55v-42.23l81.2-80.57c20.26 5.43 41.14 8.18 62.14 8.18 132.6 0 240.48-107.9 240.48-240.5s-107.87-240.5-240.47-240.5z m0 436.18c-20.87 0-41.53-3.33-61.44-9.93-8.1-2.69-16.8-0.61-22.84 5.38l-97.24 96.54c-4.24 4.2-6.63 9.93-6.63 15.89l0.26 51.29h-51.44c-12.38 0-22.41 10.02-22.41 22.41l0.24 51.2h-52.65c-5.91 0-11.6 2.32-15.78 6.52l-18.23 18.07c-28.93 28.88-76.06 28.88-104.99 0-14-14-21.73-32.65-21.73-52.47s7.72-38.47 21.66-52.43L460.46 458.8c6.13-6.06 8.21-15.1 5.4-23.24-7.16-20.61-10.77-42.14-10.77-63.98 0-107.9 87.77-195.69 195.66-195.69 107.88 0 195.66 87.79 195.66 195.69s-87.76 195.69-195.64 195.69z"  ></path></symbol><symbol id="icondate" viewBox="0 0 1024 1024"><path d="M825.44 173.37H713.5v-19.59c0-12.38-10.01-22.39-22.39-22.39s-22.39 10.01-22.39 22.39v19.59H355.28v-19.59c0-12.38-10.01-22.39-22.39-22.39s-22.39 10.01-22.39 22.39v19.59H198.56c-37.04 0-67.17 30.13-67.17 67.17v582.11c0 37.04 30.13 67.17 67.17 67.17h626.89c37.04 0 67.17-30.13 67.17-67.17V240.54c-0.01-37.04-30.14-67.17-67.18-67.17z m0 44.78c12.33 0 22.39 10.04 22.39 22.39v62.62a66.74 66.74 0 0 0-22.39-3.85H701.96c6.88-3.82 11.54-11.15 11.54-19.59v-61.57h111.94z m-156.72 0v61.57c0 8.44 4.66 15.77 11.54 19.59H343.74c6.88-3.82 11.54-11.15 11.54-19.59v-61.57h313.44z m-470.16 0H310.5v61.57c0 8.44 4.66 15.77 11.54 19.59H198.56a66.99 66.99 0 0 0-22.39 3.85v-62.62c0-12.36 10.03-22.39 22.39-22.39z m649.27 604.5c0 12.33-10.06 22.39-22.39 22.39H198.56c-12.35 0-22.39-10.06-22.39-22.39V366.47c0-12.35 10.04-22.39 22.39-22.39h626.89c12.33 0 22.39 10.04 22.39 22.39v456.18z"  ></path><path d="M400.06 488.21H265.72c-12.38 0-22.39 10.01-22.39 22.39s10.01 22.39 22.39 22.39h134.33c12.38 0 22.39-10.01 22.39-22.39 0-12.37-10.01-22.39-22.38-22.39z"  ></path><path d="M579.17 488.21H444.83c-12.38 0-22.39 10.01-22.39 22.39s10.01 22.39 22.39 22.39h134.33c12.38 0 22.39-10.01 22.39-22.39 0.01-12.37-10.01-22.39-22.38-22.39z"  ></path><path d="M758.28 488.21H623.94c-12.38 0-22.39 10.01-22.39 22.39s10.01 22.39 22.39 22.39h134.33c12.38 0 22.39-10.01 22.39-22.39 0.01-12.37-10.01-22.39-22.38-22.39zM400.06 656.13H265.72c-12.38 0-22.39 10.01-22.39 22.39s10.01 22.39 22.39 22.39h134.33c12.38 0 22.39-10.01 22.39-22.39s-10.01-22.39-22.38-22.39z"  ></path><path d="M579.17 656.13H444.83c-12.38 0-22.39 10.01-22.39 22.39s10.01 22.39 22.39 22.39h134.33c12.38 0 22.39-10.01 22.39-22.39 0.01-12.38-10.01-22.39-22.38-22.39z"  ></path><path d="M758.28 656.13H623.94c-12.38 0-22.39 10.01-22.39 22.39s10.01 22.39 22.39 22.39h134.33c12.38 0 22.39-10.01 22.39-22.39 0.01-12.38-10.01-22.39-22.38-22.39z"  ></path></symbol><symbol id="iconneuronlogo" viewBox="0 0 4812 1024"><path d="M2138.453333 747.861333V371.029333l2.048-0.682666 237.909334 377.514666h66.901333V253.610667h-66.901333v376.490666l-2.048 0.682667-237.909334-377.173333h-66.901333v494.250666h66.901333z m727.04 0v-52.224h-237.568V518.826667h203.093334v-52.565334h-203.093334V306.176h233.813334V253.610667h-300.714667v494.250666h304.469333z m253.952 7.168c55.637333 0 101.034667-15.36 136.533334-45.738666 35.157333-30.378667 52.906667-73.045333 52.906666-127.317334V253.610667h-66.901333v328.362666c0 37.888-11.264 67.242667-34.133333 88.746667-22.869333 21.504-52.224 32.085333-88.405334 32.085333-34.133333 0-62.122667-10.581333-83.626666-32.085333-21.504-21.504-32.426667-50.858667-32.426667-88.746667V253.610667h-66.901333v328.362666c0 53.930667 17.066667 96.597333 51.2 126.976 34.474667 30.72 78.165333 46.08 131.754666 46.08z m362.154667-7.168v-214.016h114.346667c26.282667 0 47.104 8.192 62.805333 24.576 15.701333 16.384 23.552 37.888 23.552 64.512v45.056c0 15.701333 1.365333 31.061333 3.754667 46.08 2.389333 15.018667 8.192 26.282667 17.066666 33.792h68.949334v-8.192c-8.533333-7.850667-14.336-17.749333-17.749334-30.037333-3.413333-12.288-5.12-25.941333-5.12-41.301333v-46.421334c0-29.354667-5.802667-53.589333-17.749333-73.386666-11.946667-19.797333-31.402667-34.133333-58.709333-43.008 25.258667-10.922667 44.714667-25.941333 58.026666-44.714667 13.312-18.773333 19.797333-40.96 19.797334-66.218667 0-45.738667-14.336-80.554667-42.666667-104.448-28.672-23.893333-69.632-35.84-123.904-35.84h-168.96v493.909334H3481.6z m95.573333-266.922666H3481.6V306.176h102.4c34.133333 0 59.392 7.509333 75.434667 22.869333 16.042667 15.36 24.234667 36.864 24.234666 64.512 0 29.354667-8.192 51.2-24.917333 65.536-16.725333 14.677333-43.690667 21.845333-81.578667 21.845334z m455.338667 274.090666c59.733333 0 108.202667-19.797333 145.749333-59.733333 37.546667-39.936 56.32-89.770667 56.32-150.186667v-88.064c0-60.416-18.773333-110.592-56.32-150.528-37.546667-39.936-86.016-59.733333-145.749333-59.733333-57.685333 0-104.448 20.138667-140.629333 60.074667s-54.272 90.112-54.272 150.186666v88.064c0 60.416 18.090667 110.592 54.272 150.186667 36.181333 39.594667 82.944 59.733333 140.629333 59.733333z m0-54.613333c-38.912 0-69.973333-14.336-93.184-43.349333s-34.816-66.218667-34.816-111.957334v-88.746666c0-45.397333 11.605333-82.261333 34.816-111.274667s54.272-43.349333 93.184-43.349333c41.301333 0 74.069333 14.336 98.304 43.349333 24.576 29.013333 36.522667 66.218667 36.522667 111.274667v88.746666c0 46.08-12.288 83.285333-36.522667 112.298667-23.893333 28.330667-56.661333 43.008-98.304 43.008z m365.226667 47.445333V371.029333l2.048-0.682666 237.909333 377.514666h66.901333V253.610667h-66.901333v376.490666l-2.048 0.682667-237.909333-377.173333h-66.901334v494.250666h66.901334z" fill="#FFFFFF" ></path><path d="M351.573333 601.770667c-28.330667 0-51.2-22.869333-51.2-51.2V153.6c0-28.330667 22.869333-51.2 51.2-51.2s51.2 22.869333 51.2 51.2v396.970667c0 28.330667-22.869333 51.2-51.2 51.2M153.6 403.114667c-28.330667 0-51.2-22.869333-51.2-51.2V153.6C102.4 125.269333 125.269333 102.4 153.6 102.4S204.8 125.269333 204.8 153.6v198.314667c0 28.330667-22.869333 51.2-51.2 51.2M1576.277333 918.186667c-28.330667 0-51.2-22.869333-51.2-51.2v-198.314667c0-28.330667 22.869333-51.2 51.2-51.2s51.2 22.869333 51.2 51.2v198.314667c0 27.989333-22.869333 51.2-51.2 51.2M1378.986667 918.186667c-28.330667 0-51.2-22.869333-51.2-51.2V484.693333c0-28.330667 22.869333-51.2 51.2-51.2s51.2 22.869333 51.2 51.2v382.293334c0 27.989333-22.869333 51.2-51.2 51.2" fill="#189BFE" opacity=".6" ></path><path d="M549.546667 914.432c-28.330667 0-51.2-22.869333-51.2-51.2V153.6c0-28.330667 22.869333-51.2 51.2-51.2s51.2 22.869333 51.2 51.2v709.632c0 28.330667-22.869333 51.2-51.2 51.2" fill="#189BFE" ></path><path d="M843.776 469.674667c-12.288 0-24.576-4.437333-34.133333-13.312L515.413333 191.829333c-21.162667-18.773333-22.869333-51.541333-3.754666-72.362666 18.773333-21.162667 51.541333-22.869333 72.362666-3.754667l294.229334 264.874667c21.162667 18.773333 22.869333 51.541333 3.754666 72.362666-10.24 10.922667-24.234667 16.725333-38.229333 16.725334M1186.816 914.432c-28.330667 0-51.2-22.869333-51.2-51.2V153.6c0-28.330667 22.869333-51.2 51.2-51.2s51.2 22.869333 51.2 51.2v709.632c0 28.330667-22.869333 51.2-51.2 51.2" fill="#189BFE" ></path><path d="M1186.816 914.432c-12.288 0-24.576-4.437333-34.133333-13.312l-294.229334-264.874667c-21.162667-19.114667-22.869333-51.541333-3.754666-72.362666 18.773333-21.162667 51.2-22.869333 72.362666-3.754667l294.229334 264.874667c21.162667 19.114667 22.869333 51.541333 3.754666 72.362666-10.24 11.264-24.234667 17.066667-38.229333 17.066667" fill="#189BFE" ></path></symbol><symbol id="iconalarm" viewBox="0 0 1024 1024"><path d="M512 153.78c-197.52 0-358.22 160.7-358.22 358.22S314.48 870.22 512 870.22 870.22 709.52 870.22 512 709.52 153.78 512 153.78z m0 671.66c-172.84 0-313.44-140.63-313.44-313.44 0-172.84 140.61-313.44 313.44-313.44 172.81 0 313.44 140.61 313.44 313.44 0 172.81-140.63 313.44-313.44 313.44z"  ></path><path d="M489.61 282.56h44.78v307.06h-44.78z"  ></path><path d="M512 665.52m-33.86 0a33.86 33.86 0 1 0 67.72 0 33.86 33.86 0 1 0-67.72 0Z"  ></path></symbol><symbol id="iconmanu" viewBox="0 0 1024 1024"><path d="M792.65 574.23l-174.74-86.95c-9.53-3.96-19.37-5.95-29.21-5.95h-6.82V273.81c0-44.06-35.84-79.89-79.87-79.89-44.06 0-79.89 35.84-79.89 79.89v384.02l-108.36-22.61c-17.91-2.97-41.43 3.45-56.06 18.32l-45.94 46.48 205.02 205c14.91 14.91 35.49 23.48 56.47 23.48h260.18c38.79 0 71.28-28.64 77.31-68.3l28.82-202.68c0.52-3.59 0.96-7.13 0.96-10.06 0.01-31.61-19.19-60.95-47.87-73.23z m-26.2 279.46c-2.71 17.71-16.27 30.04-33.01 30.04H473.26c-9.18 0-18.21-3.76-24.79-10.36L274.91 699.86l14.65-14.87c3.76-3.8 8.99-5.99 14.3-5.99l163.04 33.98V273.81c0-19.37 15.74-35.11 35.11-35.11 19.35 0 35.09 15.74 35.09 35.11V526.1h51.6c3.98 0 7.96 0.83 10.67 1.9l173.95 86.58 1.36 0.66c12.59 5.25 21.08 18.19 21.08 32.1l-29.31 206.35z"  ></path><path d="M370.21 418.77c4.22 3.61 9.4 5.38 14.56 5.38 6.3 0 12.59-2.67 17.01-7.83 8.05-9.4 6.93-23.55-2.45-31.57-32.05-27.42-50.44-67.3-50.44-109.43 0-79.37 64.56-143.93 143.93-143.93 79.34 0 143.93 64.56 143.93 143.93 0 36.19-13.47 70.75-37.96 97.36-8.35 9.1-7.78 23.26 1.31 31.64 9.18 8.4 23.31 7.83 31.66-1.33 32.1-34.87 49.76-80.22 49.76-127.66 0-104.05-84.66-188.71-188.71-188.71S304.1 171.28 304.1 275.33c0.02 55.22 24.11 107.52 66.11 143.44z"  ></path></symbol><symbol id="iconmqdisconnect" viewBox="0 0 1024 1024"><path d="M779.53 239.68c-31.97-32.95-74.47-51.07-119.73-51.07-45.26 0-87.76 18.15-119.12 50.51l-107.3 102.89c-8.92 8.55-9.23 22.72-0.66 31.66 8.55 8.88 22.69 9.18 31.66 0.66l107.88-103.46c46.83-48.32 128.25-48.36 175.09 0 48.84 50.31 48.84 132.21 0.61 181.97l-102.89 98.67c-8.92 8.55-9.23 22.72-0.7 31.64 4.42 4.59 10.28 6.91 16.18 6.91 5.55 0 11.15-2.06 15.48-6.21l103.5-99.26c65.46-67.55 65.46-177.39 0-244.91zM552.56 648.17l-107.88 103.5c-46.83 48.28-128.25 48.32-175.09-0.04-48.82-50.29-48.8-132.15-0.57-181.95l102.89-98.65c8.92-8.55 9.23-22.74 0.66-31.66-8.55-8.88-22.74-9.2-31.66-0.66l-103.46 99.24c-65.48 67.54-65.48 177.38 0 244.86 31.92 32.97 74.43 51.07 119.68 51.07s87.76-18.1 119.09-50.46l107.31-102.89c8.92-8.57 9.23-22.74 0.7-31.66-8.56-8.88-22.73-9.27-31.67-0.7zM756.97 606.5l-64.59-5.82c-12.29-0.74-23.22 7.96-24.31 20.29s7.96 23.22 20.29 24.31l64.59 5.82c0.7 0.04 1.36 0.09 2.06 0.09 11.46 0 21.21-8.75 22.26-20.38 1.08-12.33-7.97-23.22-20.3-24.31zM645.94 632.6c-3.28-11.89-15.65-18.72-27.55-15.65-11.94 3.32-18.93 15.61-15.65 27.55l26.67 96.77c2.75 9.93 11.76 16.44 21.6 16.44 1.97 0 3.98-0.26 5.95-0.79 11.94-3.32 18.93-15.61 15.65-27.55l-26.67-96.77zM259.43 418.95l64.65 4.7c0.55 0.04 1.09 0.07 1.64 0.07 11.63 0 21.45-8.99 22.3-20.77 0.9-12.33-8.37-23.04-20.71-23.94l-64.65-4.7c-12.27-1.14-23.04 8.37-23.94 20.71s8.37 23.03 20.71 23.93zM369.99 390.92c2.86 9.73 11.78 16.05 21.47 16.05 2.08 0 4.22-0.28 6.34-0.92 11.85-3.5 18.63-15.94 15.13-27.81l-28.38-96.27c-3.45-11.81-15.81-18.72-27.81-15.13-11.85 3.5-18.63 15.94-15.13 27.81l28.38 96.27z"  ></path></symbol><symbol id="iconrestart" viewBox="0 0 1024 1024"><path d="M831.78 581.58c-12.08-3.37-24.29 3.59-27.66 15.49-35.36 125.82-153.13 213.69-286.36 213.69-163.78 0-297.04-130.68-297.04-291.31 0-160.61 133.26-291.29 297.04-291.29 65.65 0 128.65 21.06 180.26 59.76h-81.46c-12.38 0-22.41 10.02-22.41 22.41s10.02 22.41 22.41 22.41H773.4c7.18 0 13.92-3.44 18.12-9.21 4.2-5.8 5.43-13.26 3.19-20.07l-50.59-156.85c-3.81-11.79-16.41-18.21-28.18-14.46-11.77 3.81-18.25 16.43-14.44 28.21l31.64 98.12c-60.81-48.58-136.44-75.12-215.38-75.12-188.51 0-341.85 150.76-341.85 336.1 0 185.36 153.35 336.12 341.85 336.12 153.24 0 288.73-101.31 329.51-246.34 3.32-11.95-3.59-24.29-15.49-27.66z"  ></path><path d="M502.68 386.88c-8.45 8.49-13.09 19.72-13.09 31.64v231.77c0 11.95 4.66 23.24 13.13 31.68 8.47 8.45 19.72 13.13 31.68 13.13 11.97 0 23.24-4.68 32.25-13.7L695.1 543.2v-8.8c0-11.97-4.68-23.22-13.13-31.68L566.09 386.84c-16.89-16.94-46.39-16.98-63.41 0.04z m31.73 263.41V418.52l111.64 111.64-111.64 120.13z"  ></path></symbol><symbol id="iconshutdown" viewBox="0 0 1024 1024"><path d="M770.13 253.87c-8.75-8.75-22.91-8.75-31.66 0s-8.75 22.91 0 31.66c124.84 124.89 124.84 328.09 0 452.94-124.84 124.89-328.03 124.89-452.94 0-124.87-124.84-124.87-328.05 0-452.94 8.75-8.75 8.75-22.91 0-31.66s-22.91-8.75-31.66 0c-142.31 142.34-142.31 373.92 0 516.26C325.04 841.27 418.53 876.87 512 876.87c93.49 0 186.98-35.59 258.13-106.74 142.33-142.34 142.33-373.92 0-516.26z"  ></path><path d="M512 620.05c12.38 0 22.39-10.01 22.39-22.39V169.35c0-12.38-10.01-22.39-22.39-22.39s-22.39 10.01-22.39 22.39v428.32c0 12.37 10.01 22.38 22.39 22.38z"  ></path></symbol><symbol id="iconstanndby" viewBox="0 0 1024 1024"><path d="M832.66 581.78c-12.11-3.38-24.36 3.6-27.73 15.54C769.47 723.48 651.38 811.6 517.77 811.6c-164.24 0-297.86-131.04-297.86-292.12 0-161.06 133.63-292.09 297.86-292.09 65.83 0 129.01 21.12 180.76 59.92h-81.69c-12.42 0-22.47 10.05-22.47 22.47s10.05 22.47 22.47 22.47h157.28c7.2 0 13.96-3.44 18.17-9.24 4.21-5.81 5.44-13.3 3.2-20.12l-50.73-157.28c-3.82-11.83-16.46-18.26-28.26-14.5-11.8 3.82-18.3 16.48-14.48 28.28l31.73 98.39c-60.97-48.72-136.82-75.33-215.98-75.33-189.03 0-342.8 151.18-342.8 337.03 0 185.87 153.77 337.05 342.8 337.05 153.66 0 289.53-101.59 330.43-247.02 3.33-11.98-3.6-24.35-15.54-27.73z"  ></path><path d="M512 339.73c-12.42 0-22.47 10.05-22.47 22.47v177.47l167.95 103.81c3.69 2.24 7.77 3.34 11.8 3.34 7.5 0 14.88-3.77 19.13-10.66 6.49-10.53 3.25-24.4-7.33-30.94l-146.62-90.6V362.2c0.01-12.42-10.04-22.47-22.46-22.47z"  ></path></symbol><symbol id="iconupdate" viewBox="0 0 1024 1024"><path d="M512 176.5c-185.01 0-335.5 150.49-335.5 335.5S326.99 847.5 512 847.5 847.5 697.01 847.5 512 697.01 176.5 512 176.5z m0 626.27c-160.32 0-290.77-130.44-290.77-290.77S351.68 221.23 512 221.23 802.77 351.68 802.77 512 672.32 802.77 512 802.77z"  ></path><path d="M512 284.37L369.65 426.72c-8.74 8.74-8.74 22.89 0 31.63 4.37 4.37 10.09 6.55 15.81 6.55s11.45-2.18 15.81-6.55L489.62 370v340.94c0 12.36 10 22.37 22.37 22.37s22.37-10 22.37-22.37V370l88.33 88.35c8.74 8.74 22.89 8.74 31.63 0 8.74-8.74 8.74-22.89 0-31.63L512 284.37z"  ></path></symbol><symbol id="iconcomm-down" viewBox="0 0 1024 1024"><path d="M512 153.32c-197.78 0-358.68 160.91-358.68 358.68S314.22 870.68 512 870.68 870.68 709.78 870.68 512 709.78 153.32 512 153.32z m0 672.53c-173.06 0-313.85-140.81-313.85-313.85 0-173.06 140.79-313.85 313.85-313.85 173.04 0 313.85 140.79 313.85 313.85 0 173.04-140.81 313.85-313.85 313.85z"  ></path><path d="M716.96 515.2H307.04c-12.39 0-22.42 10.03-22.42 22.42s10.03 22.42 22.42 22.42h409.91c12.39 0 22.42-10.03 22.42-22.42s-10.02-22.42-22.41-22.42z"  ></path></symbol><symbol id="icondisplay" viewBox="0 0 1024 1024"><path d="M918.3 499.3C797.2 342.1 660.6 262.4 512 262.4s-285.2 79.7-406.3 236.9L95.9 512l9.7 12.7C226.8 681.9 363.4 761.6 512 761.6s285.2-79.7 406.3-236.9L928 512l-9.7-12.7zM512 720c-131 0-253.2-70-363.4-208C258.8 374 381 304 512 304s253.2 70 363.4 208C765.2 650 643 720 512 720z"  ></path><path d="M512 345.6c-91.8 0-166.4 74.6-166.4 166.4S420.2 678.4 512 678.4 678.4 603.8 678.4 512 603.8 345.6 512 345.6z m0 291.2c-68.8 0-124.8-56-124.8-124.8s56-124.8 124.8-124.8 124.8 56 124.8 124.8-56 124.8-124.8 124.8z"  ></path></symbol><symbol id="icondelete" viewBox="0 0 1024 1024"><path d="M844.9 263.4h-74l0.1-0.3H253l0.1 0.3h-74V305h80.2l80.1 539.9h345.3l80-539.9h80.2v-41.6zM649 803.3H375L301.7 305h420.6L649 803.3zM345.5 178.9h332.9v41.6H345.5z"  ></path><path d="M427.9 426.5h41.6v277.7h-41.6zM552.7 426.5h41.6v277.7h-41.6z"  ></path></symbol><symbol id="iconsearch" viewBox="0 0 1024 1024"><path d="M800.6 774.3L664.4 638.1c-1.1-1.1-2.3-2-3.5-2.8 40-45.9 64.3-105.9 64.3-171.4 0-144.1-117.2-261.3-261.3-261.3S202.6 319.8 202.6 463.9s117.2 261.3 261.3 261.3c64.1 0 122.9-23.2 168.4-61.7 0.8 1.3 1.8 2.6 3 3.8l136.1 136.1c4 4 9.3 6 14.6 6 5.3 0 10.6-2 14.6-6 8-8 8-21.1 0-29.1z m-336.7-90.4c-121.3 0-220-98.7-220-220.1 0-121.3 98.7-220 220-220 121.4 0 220.1 98.7 220.1 220-0.1 121.4-98.8 220.1-220.1 220.1z"  ></path></symbol></svg>',i=(i=document.getElementsByTagName("script"))[i.length-1].getAttribute("data-injectcss"),s=function(e,n){n.parentNode.insertBefore(e,n)};if(i&&!e.__iconfont__svg__cssinject__){e.__iconfont__svg__cssinject__=!0;try{document.write("<style>.svgfont {display: inline-block;width: 1em;height: 1em;fill: currentColor;vertical-align: -0.1em;font-size:16px;}</style>")}catch(e){console&&console.log(e)}}function u(){r||(r=!0,a())}function l(){try{o.documentElement.doScroll("left")}catch(e){return void setTimeout(l,50)}u()}n=function(){var e,n=document.createElement("div");n.innerHTML=c,c=null,(n=n.getElementsByTagName("svg")[0])&&(n.setAttribute("aria-hidden","true"),n.style.position="absolute",n.style.width=0,n.style.height=0,n.style.overflow="hidden",n=n,(e=document.body).firstChild?s(n,e.firstChild):e.appendChild(n))},document.addEventListener?~["complete","loaded","interactive"].indexOf(document.readyState)?setTimeout(n,0):(t=function(){document.removeEventListener("DOMContentLoaded",t,!1),n()},document.addEventListener("DOMContentLoaded",t,!1)):document.attachEvent&&(a=n,o=e.document,r=!1,l(),o.onreadystatechange=function(){"complete"==o.readyState&&(o.onreadystatechange=null,u())})}(window)},"0519":function(e,n,t){var a={"./":"55b6","./admin":"4d3f","./admin.ts":"4d3f","./common":"aa74","./common.ts":"aa74","./config":"9c75","./config.ts":"9c75","./data":"47d9","./data.ts":"47d9","./ekuiper":"5d41","./ekuiper.ts":"5d41","./error":"7d7a","./error.ts":"7d7a","./index":"55b6","./index.ts":"55b6","./template":"94ee","./template.ts":"94ee"};function o(e){var n=r(e);return t(n)}function r(e){if(!t.o(a,e)){var n=new Error("Cannot find module '"+e+"'");throw n.code="MODULE_NOT_FOUND",n}return a[e]}o.keys=function(){return Object.keys(a)},o.resolve=r,e.exports=o,o.id="0519"},"0613":function(e,n,t){"use strict";t("caad");var a=t("5502"),o=t("2c8a"),r=t("9613"),c=function(e){return["en","zh"].includes(e)?e:""},i=function(){var e=c(localStorage.getItem("language")||"");return e||r["c"]};n["a"]=Object(a["a"])({state:function(){var e;return{lang:i(),token:null!==(e=Object(o["d"])())&&void 0!==e?e:"",isSubAppLoading:!1,subAppInstances:{ekuiper:void 0},listShowType:"list",paginationData:{pageNum:1,pageSize:30,total:0},nodeGroupMemory:Object(o["c"])(),axiosPromiseCancel:[]}},mutations:{SET_LANG:function(e,n){e.lang=n||r["c"],localStorage.setItem("language",n)},SET_TOKEN:function(e,n){e.token=n,Object(o["g"])(n)},LOGOUT:function(e){e.token="",Object(o["a"])()},SET_SUB_APP_INSTANCE:function(e,n){e.subAppInstances[n.key]=n.instance},SET_SUB_APP_LOADING:function(e,n){e.isSubAppLoading=n},SET_LIST_SHOW_TYPE:function(e,n){e.listShowType=n},SET_PAGINATION:function(e,n){e.paginationData=n},SET_NODE_GROUP:function(e,n){e.nodeGroupMemory=n,Object(o["f"])(n)},ADD_AXIOS_PROMISE_CANCEL:function(e,n){e.axiosPromiseCancel.push(n)},SET_AXIOS_PROMISE_CANCEL:function(e,n){e.axiosPromiseCancel=n}}})},1:function(e,n,t){e.exports=t("cd49")},"1be0":function(e,n,t){},"1c98":function(e,n,t){},"1d4c":function(e,n,t){},"1d89":function(e,n,t){},"1e95":function(e,n,t){"use strict";t.d(n,"a",(function(){return c}));t("4d63"),t("c607"),t("ac1f"),t("2c3e"),t("25f0"),t("466d"),t("d3b7"),t("3ca3"),t("ddb0"),t("2b3d"),t("9861");var a=t("47e2"),o=t("73ec"),r=t("3fd4"),c=function(){var e=Object(a["b"])(),n=e.t,t=function(e,n,t){if(e&&"application/octet-stream"===e["content-type"]){var a=t||"filename",o=new RegExp("".concat(a,"=(.*)")),r=e["content-disposition"],c=r.match(o),i=c?c[1]:"default";if(i=decodeURIComponent(i),"download"in document.createElement("a")){var s=window.URL.createObjectURL(new Blob([n],{type:"application/json"})),u=document.createElement("a");u.href=s,u.setAttribute("download",i),document.body.appendChild(u),u.click(),document.body.removeChild(u)}else navigator.msSaveBlob(new Blob([n]),i)}},c=function(e){return new Promise((function(t,a){if("file"!==Object(o["f"])(e))r["ElMessage"].error(n("common.isNotFile")),a(n("common.isNotFile"));else{var c="",i=new FileReader;i.readAsText(e,"utf-8"),i.onload=function(){c=(null===i||void 0===i?void 0:i.result)||"",t(c)},i.onerror=function(){r["ElMessage"].error(n("common.readFileError")),console.error(i.error),a(i.error)}}}))};return{downloadFile:t,readTextFile:c}}},2:function(e,n){},"2c8a":function(e,n,t){"use strict";t.d(n,"d",(function(){return c})),t.d(n,"g",(function(){return i})),t.d(n,"a",(function(){return s})),t.d(n,"e",(function(){return u})),t.d(n,"b",(function(){return l})),t.d(n,"f",(function(){return d})),t.d(n,"c",(function(){return h}));t("e9c4");var a="token",o="breadcrumbs",r="nodeGroupData",c=function(){return window.localStorage.getItem(a)},i=function(e){window.localStorage.setItem(a,e)},s=function(){return localStorage.clear()},u=function(e){window.localStorage.setItem(o,e)},l=function(){var e=window.localStorage.getItem(o)||"";return e},d=function(e){window.localStorage.setItem(r,JSON.stringify(e))},h=function(){var e=JSON.parse(window.localStorage.getItem(r)||'{"node":"","groupName":""}')||"";return e}},"2de2":function(e,n,t){"use strict";t.d(n,"b",(function(){return a})),t.d(n,"a",(function(){return o}));var a="true"===Object({NODE_ENV:"production",BASE_URL:"/web/"}).VUE_APP_LANG_EN,o=1e4},3:function(e,n){},"34b9":function(e,n,t){},"3fb0":function(e,n,t){"use strict";t("1d89")},"47d9":function(e,n,t){"use strict";t.r(n);t("e6e1"),t("a9e3"),t("aff5");n["default"]={data:{zh:"数据",en:"Data"},monitoring:{zh:"监控",en:"Monitoring"},dataMonitoring:{zh:"数据监控",en:"Data Monitoring"},history:{zh:"历史数据",en:"History"},current:{zh:"当前数据",en:"Current"},write:{zh:"写数据",en:"Write"},value:{zh:"值",en:"Value"},objName:{zh:"Object Name",en:"Object 名称"},attribute:{zh:"属性",en:"Attribute"},selectGroupTip:{zh:"请先选择 group",en:"Please select group first"},updated:{zh:"更新时间",en:"Updated"},writeDataFormattingErrorPrefix:{zh:"类型 ",en:"Type"},writeDataFormattingErrorSuffix:{zh:" 内容格式错误",en:" content format error"},arrayLengthError:{zh:"请输入长度不大于 {length} 的数据",en:"Please enter data no longer than {length}"},writeDataMinimumErrorPrefix:{zh:"小于数据类型 ",en:"Less than the minimum value of data type "},writeDataMinimumErrorSuffix:{zh:" 的最小值",en:""},writeDataMaximumErrorPrefix:{zh:"大于数据类型 ",en:"Greater than the maximum value of data type "},writeDataMaximumErrorSuffix:{zh:" 的最大值",en:""},writeDataSafeMinimumError:{zh:"暂不支持输入小于 ".concat(Number.MIN_SAFE_INTEGER," 的数"),en:"It is not currently supported to enter a number less than ".concat(Number.MIN_SAFE_INTEGER)},writeDataSafeMaximumError:{zh:"暂不支持输入大于 ".concat(Number.MAX_SAFE_INTEGER," 的数"),en:"It is not currently supported to enter a number greater than ".concat(Number.MAX_SAFE_INTEGER,".")},useHexadecimalInput:{zh:"使用 16 进制输入",en:"Use hexadecimal input"},hexadecimalError:{zh:"请输入格式正确的 16 进制数",en:"Please enter a hexadecimal number in the correct format"},displayTheValueInHexadecimal:{zh:"以 16 进制展示值",en:"Display the value in hexadecimal"},nodeDeleted:{zh:"节点不存在",en:"Node not exist"},groupDeleted:{zh:"组不存在",en:"Group not exist"}}},"4d3f":function(e,n,t){"use strict";t.r(n),n["default"]={admin:{zh:"管理",en:"Administration"},log:{zh:"日志",en:"Logs"},debugFiles:{zh:"调试文件",en:"Debug files"},logType:{zh:"日志类型",en:"Log type"},timeRange:{zh:"时间范围",en:"Time range"},timeRangeRequired:{zh:"请先选择时间范围",en:"Please select a time range first"},license:{zh:"软件授权",en:"License"},licenseType:{zh:"软件授权类型",en:"License type"},licenseStatus:{zh:"软件授权状态",en:"License status"},effectiveDate:{zh:"签发时间",en:"Issued At"},expireDate:{zh:"过期时间",en:"Expire At"},maxNodes:{zh:"节点数限制",en:"Limit on the maximum number of nodes"},maxNodeTags:{zh:"点位数目限制",en:"Limit on the maximum number of tags"},enabledPlugins:{zh:"可用插件",en:"Enabled plugins"},licensePlaceholder:{zh:"当前暂无软件授权，请以下列途径获取软件授权",en:"There is currently no license, please get the license in the following ways"},howToGetTheCertificate:{zh:"证书获取方式",en:"How to get the certificate"},getFreeLicense:{zh:"免费{0} 软件授权",en:"{0} for a license for free."},apply:{zh:"申请",en:"Apply"},buyLicense:{zh:"{0}，购买商业软件授权",en:"{0} to purchase a commercial license."},contactUs:{zh:"联系我们",en:"Contact us"},uploadSuccessful:{zh:"上传成功",en:"Upload successful"},uploadFileTypeError:{zh:"请上传 '.lic' 类型文件",en:"Please upload '.lic' type file"},licenseEvaluationTip:{zh:'您现在正在使用 30 个标签评估许可证。 请<a target="_blank" rel="noopener norefferrer" href="https://www.emqx.com/zh/apply-licenses/neuron">升级</a>许可证。',en:'You are now using the 30 tags evaluation license. Please <a target="_blank" rel="noopener norefferrer" href="https://www.emqx.com/zh/apply-licenses/neuron">upgrade</a> license.'},licenseInvalidTip:{zh:'您的 License 无效，\n    请<a target="_blank" rel="noopener norefferrer" href="https://www.emqx.com/zh/apply-licenses/neuron">更新 License</a>\n    或联系销售人员更新 License。',en:'\n    Your license has invalid,\n    please <a target="_blank" rel="noopener norefferrer" href="https://www.emqx.com/zh/apply-licenses/neuron">update the license</a>\n    or contact the sales staff to update the License.'},licenseExpiryTip:{zh:'您的 License 已过期，\n    请<a target="_blank" rel="noopener norefferrer" href="https://www.emqx.com/zh/apply-licenses/neuron">更新 License</a>\n    或联系销售人员更新 License。',en:'Your License has expired,\n    please <a target="_blank" rel="noopener norefferrer" href="https://www.emqx.com/zh/apply-licenses/neuron">update the License</a>\n    or contact the sales staff to update the License.'},licenseOverMaximumNodesTip:{zh:'节点数超过 License 限制，Neuron 无法正常使用，\n    请<a target="_blank" rel="noopener norefferrer" href="https://www.emqx.com/zh/apply-licenses/neuron">更新 License</a>\n    或删除部分驱动节点。',en:'The count of nodes exceeds the license limit, and Neuron cannot be used normally.\n    Please <a target="_blank" rel="noopener norefferrer" href="https://www.emqx.com/zh/apply-licenses/neuron">update the license</a>\n    or delete some driver nodes.'},licenseOverMaximumTagsTip:{zh:'点位数超过 License 限制，Neuron 无法正常使用，\n    请<a target="_blank" rel="noopener norefferrer" href="https://www.emqx.com/zh/apply-licenses/neuron">更新 License</a>\n    或删除部分点位。',en:'The count of data tags exceeds the license limit, and Neuron cannot be used normally.\n    Please <a target="_blank" rel="noopener norefferrer" href="https://www.emqx.com/zh/apply-licenses/neuron">update the license</a>\n    or delete some data tags.'},licenseHardwareMismatchTip:{zh:'License 与该硬件不匹配，Neuron 无法正常使用，\n    请<a target="_blank" rel="noopener norefferrer" href="https://www.emqx.com/zh/apply-licenses/neuron">更新 License</a>\n    或联系销售人员更新 License。',en:'The license does not match the hardware, and Neuron cannot be used normally.\n    Please <a target="_blank" rel="noopener norefferrer" href="https://www.emqx.com/zh/apply-licenses/neuron">update the license</a>\n    or contact the sales staff to update the License.'},licenseReadyExpiryTip:{zh:'您的 License 即将过期，\n    请<a target="_blank" rel="noopener norefferrer" href="https://www.emqx.com/zh/apply-licenses/neuron">更新 License</a>\n    或联系销售人员更新 License。',en:'Your license is about to expire,\n    please <a target="_blank" rel="noopener norefferrer" href="https://www.emqx.com/zh/apply-licenses/neuron">update the License</a>\n    or contact the sales staff to update the License.'},konw:{zh:"知道了",en:"Acknowledge"},noPrompt:{zh:"不再提示",en:"No more prompts"},notYetValid:{zh:"尚未生效",en:"Not yet valid"},inEffect:{zh:"生效中",en:"In effect"},expired:{zh:"已过期",en:"Expired"},version:{zh:"版本",en:"Version"},builtDate:{zh:"项目构建日期",en:"Built date"},hwToken:{zh:"硬件标志",en:"Hardware Token"},systemRunningTime:{zh:"系统运行时长",en:"System Running Time"},systemStatus:{zh:"系统状态",en:"System Status"},usedNodes:{zh:"已使用节点数",en:"Used Nodes"},usedTags:{zh:"已使用点位数",en:"Used Tags"},hardwareToken:{zh:"硬件标识",en:"Hardware Token"},object:{zh:"签发对象",en:"Customer"},emailAddress:{zh:"签发邮箱",en:"Issuing email"},nodeUsage:{zh:"商业节点使用情况",en:"Business node usage"},tagUsage:{zh:"点位使用情况",en:"Tags usage"},memoryUsage:{zh:"内存使用情况",en:"Memory usage"}}},5566:function(e,n,t){"use strict";t.d(n,"b",(function(){return pe})),t.d(n,"a",(function(){return fe}));t("d3b7"),t("3ca3"),t("ddb0");var a=t("7a23"),o={class:"main"};function r(e,n,t,r,c,i){var s=Object(a["resolveComponent"])("Header"),u=Object(a["resolveComponent"])("side-nav"),l=Object(a["resolveComponent"])("Breadcrumb"),d=Object(a["resolveComponent"])("router-view"),h=Object(a["resolveComponent"])("emqx-container");return Object(a["openBlock"])(),Object(a["createElementBlock"])("div",o,[Object(a["createVNode"])(s,{class:"header"}),Object(a["createVNode"])(h,{normal:"","fixed-nav":"",class:"body","page-min-width":1250},{nav:Object(a["withCtx"])((function(){return[Object(a["createVNode"])(u,{class:"sidebar"})]})),"page-content":Object(a["withCtx"])((function(){return[Object(a["createElementVNode"])("div",null,[Object(a["createVNode"])(l),Object(a["createVNode"])(d)])]})),_:1})])}var c=t("1da1"),i=(t("7db0"),t("96cf"),t("9d64")),s=t.n(i),u=t("5502"),l=t("6c02"),d=t("6a4f"),h=t("1e95"),m=t("eb58"),p=t("2de2"),f=Object(a["createElementVNode"])("img",{src:s.a,alt:"neuron-logo",width:"141"},null,-1),b={class:"el-dropdown-link"},z=Object(a["createElementVNode"])("i",{class:"el-icon-arrow-down el-icon--right"},null,-1),g=Object(a["createElementVNode"])("i",{class:"iconfont iconLicense"},null,-1),v=Object(a["createElementVNode"])("span",null,"License",-1),O=Object(a["createElementVNode"])("i",{class:"iconfont iconabout"},null,-1),w=Object(a["createElementVNode"])("i",{class:"iconfont icondownload"},null,-1),y={class:"el-dropdown-link"},j=Object(a["createElementVNode"])("i",{class:"el-icon-arrow-down el-icon--right"},null,-1),k=Object(a["createElementVNode"])("span",{class:"el-dropdown-link"},[Object(a["createElementVNode"])("span",{class:"user-bg"},[Object(a["createElementVNode"])("i",{class:"iconfont iconAdministration1"})]),Object(a["createElementVNode"])("i",{class:"el-icon-arrow-down el-icon--right"})],-1),N=Object(a["defineComponent"])({setup:function(e){var n=Object(u["b"])(),t=Object(l["d"])(),o=function(){t.push({name:"License"})},r=function(){t.push({name:"About"})},i=Object(h["a"])(),s=i.downloadFile,N=function(){Object(d["a"])().then((function(e){var n=e.data,t=e.headers;s(t,n)}))},S=Object(m["a"])(),x=S.langList,T=Object(m["b"])(),E=T.changeLang,L=function(e){E(e)},M=Object(a["computed"])({get:function(){return n.state.lang},set:function(e){L(e)}}),P=function(){var e=Object(c["a"])(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:try{L(M.value),n.commit("LOGOUT"),t.push({name:"Login"})}catch(a){console.error(a)}case 1:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),C=Object(a["computed"])((function(){var e;return(null===(e=x.find((function(e){return e.value===M.value})))||void 0===e?void 0:e.label)||""})),A=function(e){M.value=e};return function(e,n){var t=Object(a["resolveComponent"])("emqx-dropdown-item"),c=Object(a["resolveComponent"])("emqx-dropdown-menu"),i=Object(a["resolveComponent"])("emqx-dropdown"),s=Object(a["resolveComponent"])("emqx-header");return Object(a["openBlock"])(),Object(a["createBlock"])(s,{class:"header"},{title:Object(a["withCtx"])((function(){return[f]})),right:Object(a["withCtx"])((function(){return[Object(a["createElementVNode"])("div",null,[Object(a["createVNode"])(i,{class:"dropdown-item"},{dropdown:Object(a["withCtx"])((function(){return[Object(a["createVNode"])(c,{class:"header-menu"},{default:Object(a["withCtx"])((function(){return[Object(a["createVNode"])(t,{onClick:o},{default:Object(a["withCtx"])((function(){return[g,v]})),_:1}),Object(a["createVNode"])(t,{onClick:r},{default:Object(a["withCtx"])((function(){return[O,Object(a["createElementVNode"])("span",null,Object(a["toDisplayString"])(e.$t("common.about")),1)]})),_:1}),Object(a["createVNode"])(t,{onClick:N},{default:Object(a["withCtx"])((function(){return[w,Object(a["createElementVNode"])("span",null,Object(a["toDisplayString"])(e.$t("admin.debugFiles")),1)]})),_:1})]})),_:1})]})),default:Object(a["withCtx"])((function(){return[Object(a["createElementVNode"])("span",b,[Object(a["createTextVNode"])(Object(a["toDisplayString"])(e.$t("common.systemInformation"))+" ",1),z])]})),_:1}),Object(a["unref"])(p["b"])?Object(a["createCommentVNode"])("",!0):(Object(a["openBlock"])(),Object(a["createBlock"])(i,{key:0,class:"dropdown-item",onCommand:A},{dropdown:Object(a["withCtx"])((function(){return[Object(a["createVNode"])(c,{class:"header-menu"},{default:Object(a["withCtx"])((function(){return[(Object(a["openBlock"])(!0),Object(a["createElementBlock"])(a["Fragment"],null,Object(a["renderList"])(Object(a["unref"])(x),(function(e){return Object(a["openBlock"])(),Object(a["createBlock"])(t,{key:e.label,command:e.value,class:Object(a["normalizeClass"])({"active-lang":e.value===Object(a["unref"])(M)})},{default:Object(a["withCtx"])((function(){return[Object(a["createTextVNode"])(Object(a["toDisplayString"])(e.label),1)]})),_:2},1032,["command","class"])})),128))]})),_:1})]})),default:Object(a["withCtx"])((function(){return[Object(a["createElementVNode"])("span",y,[Object(a["createTextVNode"])(Object(a["toDisplayString"])(Object(a["unref"])(C))+" ",1),j])]})),_:1})),Object(a["createVNode"])(i,null,{dropdown:Object(a["withCtx"])((function(){return[Object(a["createVNode"])(c,{class:"header-menu"},{default:Object(a["withCtx"])((function(){return[Object(a["createVNode"])(t,{onClick:P},{default:Object(a["withCtx"])((function(){return[Object(a["createTextVNode"])(Object(a["toDisplayString"])(e.$t("common.logout")),1)]})),_:1})]})),_:1})]})),default:Object(a["withCtx"])((function(){return[k]})),_:1})])]})),_:1})}}});t("3fb0");const S=N;var x=S,T=t("5530"),E=(t("ac1f"),t("466d"),t("159b"),{class:"nav-item-content"}),L={class:"nav-label ellipsis"},M={class:"nav-item-content"},P={class:"nav-label ellipsis"},C={class:"sub-menu-list"},A={class:"nav-item-content"},B={class:"nav-label ellipsis"},D=Object(a["defineComponent"])({name:"SideNav"});function V(e){var n=Object(a["computed"])((function(){var e=[{to:"/monitoring/data",label:"data.monitoring",icon:"iconstatus",subMenus:[{to:"/monitoring/data",label:"data.dataMonitoring"}]},{to:"/configuration",label:"config.config",icon:"iconconfig",subMenus:[{to:"/configuration/south-driver",label:"config.southDeviceManagement"},{to:"/configuration/north-driver",label:"config.northAppSetup"},{to:"/configuration/template",label:"config.templateManagement"},{to:"/configuration/plugin",label:"config.plugin"}]},{to:"/admin",label:"admin.admin",icon:"iconAdministration1",subMenus:[{to:"/admin/change-password",label:"common.changePassword"}]}];return e})),t=[],o=[],r=Object(a["computed"])((function(){var e=Object(l["c"])().path,n=o.find((function(n){return e.match(n)}));return n||(n=t.find((function(n){return e.match(n)})),n||"")})),c=function(){n.value.forEach((function(e){e.subMenus?e.subMenus.forEach((function(e){e.to.match(/^\/[^/]+\/[^/]+/)?o.push(e.to):t.push(e.to)})):t.push(e.to)}))};return c(),function(e,t){var o=Object(a["resolveComponent"])("emqx-menu-item"),c=Object(a["resolveComponent"])("emqx-submenu"),i=Object(a["resolveComponent"])("emqx-menu");return Object(a["openBlock"])(),Object(a["createBlock"])(i,{class:"el-menu-vertical-demo side-nav",router:"","default-active":Object(a["unref"])(r)},{default:Object(a["withCtx"])((function(){return[(Object(a["openBlock"])(!0),Object(a["createElementBlock"])(a["Fragment"],null,Object(a["renderList"])(Object(a["unref"])(n),(function(n,t){return Object(a["openBlock"])(),Object(a["createElementBlock"])(a["Fragment"],null,[n.subMenus?(Object(a["openBlock"])(),Object(a["createBlock"])(c,{key:n.to,index:n.to},{title:Object(a["withCtx"])((function(){return[Object(a["createElementVNode"])("div",M,[Object(a["createElementVNode"])("i",{class:Object(a["normalizeClass"])(["nav-icon iconfont",n.icon])},null,2),Object(a["createElementVNode"])("span",P,Object(a["toDisplayString"])(e.$t("".concat(n.label))),1)])]})),default:Object(a["withCtx"])((function(){return[Object(a["createElementVNode"])("div",C,[(Object(a["openBlock"])(!0),Object(a["createElementBlock"])(a["Fragment"],null,Object(a["renderList"])(n.subMenus,(function(n,t){return Object(a["openBlock"])(),Object(a["createBlock"])(o,{class:"nav-item",key:t,index:n.to},{default:Object(a["withCtx"])((function(){return[Object(a["createElementVNode"])("div",A,[Object(a["createElementVNode"])("span",B,Object(a["toDisplayString"])(e.$t("".concat(n.label))),1)])]})),_:2},1032,["index"])})),128))])]})),_:2},1032,["index"])):(Object(a["openBlock"])(),Object(a["createBlock"])(o,{class:"nav-item",style:{"padding-left":"0"},key:t,index:n.to},{default:Object(a["withCtx"])((function(){return[Object(a["createElementVNode"])("div",E,[Object(a["createElementVNode"])("i",{class:Object(a["normalizeClass"])(["nav-icon iconfont",n.icon])},null,2),Object(a["createElementVNode"])("span",L,Object(a["toDisplayString"])(e.$t("".concat(n.label))),1)])]})),_:2},1032,["index"]))],64)})),256))]})),_:1},8,["default-active"])}}var _=Object(a["defineComponent"])(Object(T["a"])(Object(T["a"])({},D),{},{setup:V}));const I=_;var R=I,F=(t("4ec9"),t("1276"),t("d81d"),t("b0c0"),t("4de4"),t("99af"),t("2c8a")),q={key:0,class:"no-redirect"},H=["onClick"],G=Object(a["defineComponent"])({name:"Breadcrumb"});function U(e){var n=Object(l["c"])(),t=Object(l["d"])(),o=Object(a["reactive"])({levelList:[]}),r=Object(a["toRefs"])(o),c=r.levelList;Object(a["onBeforeMount"])((function(){d()})),Object(a["watch"])((function(){return n.path}),(function(){d()}),{immediate:!1});var i=Object(a["computed"])((function(){return!n.meta.hiddenBreadcrumb})),s=function(){var e=Object(F["b"])();if(!e)return new Map;var n=e.split(";"),t=n.map((function(e){return e.split(":")})),a=new Map(t);return a.delete(""),a},u=function(e){var n=s(),t=e.map((function(e){var t=n.get(e.name);return t&&(e.fullPath=t),e}));return t},d=function(){var e=n.fullPath,t=n.matched;if(n.meta.hiddenBreadcrumb)return o.levelList=[],void Object(F["e"])("");var a=t.filter((function(e){return e.meta&&e.meta.title})),r=Object(T["a"])(Object(T["a"])({},a[a.length-1]),{},{fullPath:e});a[a.length-1]=r;var c=Object(F["b"])(),i=s(),l=i.size,d=null===a||void 0===a?void 0:a.length,h=d?a[0].name:"";if(l&&d&&h&&i.get(h)){if(l<d){var m=String(r.name)||"",p="".concat(m,":").concat(e,";");Object(F["e"])("".concat(c).concat(p))}}else{var f=a.map((function(e){var n=e.fullPath||e.path;return"".concat(e.name,":").concat(n)})),b=f?f.reduce((function(e,n){return"".concat(e,";").concat(n,";")})):"";Object(F["e"])(b)}o.levelList=u(a)},h=function(e){var n=e.redirect,a=e.path,o=e.fullPath;if(n)t.push(n);else{var r=o||a;t.push(r)}};return function(e,n){var t=Object(a["resolveComponent"])("emqx-breadcrumb-item"),o=Object(a["resolveComponent"])("emqx-breadcrumb");return Object(a["unref"])(i)?(Object(a["openBlock"])(),Object(a["createBlock"])(o,{key:0,separator:"/"},{default:Object(a["withCtx"])((function(){return[Object(a["createVNode"])(a["TransitionGroup"],{name:"breadcrumb",mode:"out-in"},{default:Object(a["withCtx"])((function(){return[(Object(a["openBlock"])(!0),Object(a["createElementBlock"])(a["Fragment"],null,Object(a["renderList"])(Object(a["unref"])(c),(function(n,o){return Object(a["openBlock"])(),Object(a["createBlock"])(t,{key:n.path},{default:Object(a["withCtx"])((function(){return["noRedirect"===n.redirect||o===Object(a["unref"])(c).length-1?(Object(a["openBlock"])(),Object(a["createElementBlock"])("span",q,Object(a["toDisplayString"])(e.$t("".concat(n.meta.title))),1)):(Object(a["openBlock"])(),Object(a["createElementBlock"])("a",{key:1,onClick:Object(a["withModifiers"])((function(e){return h(n)}),["prevent"])},Object(a["toDisplayString"])(e.$t("".concat(n.meta.title))),9,H))]})),_:2},1024)})),128))]})),_:1})]})),_:1})):Object(a["createCommentVNode"])("",!0)}}var W=Object(a["defineComponent"])(Object(T["a"])(Object(T["a"])({},G),{},{setup:U})),K=(t("9386"),t("6b0d")),Y=t.n(K);const J=Y()(W,[["__scopeId","data-v-1d4da8a4"]]);var $=J,X=Object(a["defineComponent"])({name:"Home",components:{Header:x,SideNav:R,Breadcrumb:$},setup:function(){var e=Object(u["b"])(),n=Object(a["computed"])((function(){return e.state.isSubAppLoading}));return{isSubAppLoading:n}}});const Q=Y()(X,[["render",r]]);var Z=Q,ee=t("5898"),ne=t.n(ee),te=t("f727"),ae=t("73ec"),oe=t("47e2"),re={class:"login-page"},ce={class:"container"},ie=Object(a["createElementVNode"])("img",{class:"img-login",src:ne.a},null,-1),se={class:"login-main"},ue=Object(a["createElementVNode"])("img",{class:"img-logo",src:s.a,alt:"neuron-logo",width:"141"},null,-1),le=Object(a["defineComponent"])({setup:function(e){var n=Object(l["d"])(),t=Object(oe["b"])(),o=t.t,r=Object(u["b"])(),i=Object(a["ref"])(),s=Object(a["reactive"])({userName:"",password:""}),d=Object(a["computed"])((function(){return{userName:[{required:!0,message:Object(ae["c"])("input",o("common.username"))}],password:[{required:!0,message:Object(ae["c"])("input",o("common.password"))}]}})),h=Object(a["ref"])(!1),m=function(){r.commit("SET_LANG",r.state.lang)},p=function(){var e=Object(c["a"])(regeneratorRuntime.mark((function e(){var t,a,o,c;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,i.value.validate();case 3:return h.value=!0,t=s.userName,a=s.password,e.next=7,Object(te["b"])({name:t,pass:a});case 7:o=e.sent,c=o.data,r.commit("SET_TOKEN",c.token),m(),n.push({path:"/"}),e.next=17;break;case 14:e.prev=14,e.t0=e["catch"](0),console.error(e.t0);case 17:return e.prev=17,h.value=!1,e.finish(17);case 20:case"end":return e.stop()}}),e,null,[[0,14,17,20]])})));return function(){return e.apply(this,arguments)}}();return m(),function(e,n){var t=Object(a["resolveComponent"])("emqx-input"),o=Object(a["resolveComponent"])("emqx-form-item"),r=Object(a["resolveComponent"])("emqx-button"),c=Object(a["resolveComponent"])("emqx-form"),u=Object(a["resolveComponent"])("emqx-card");return Object(a["openBlock"])(),Object(a["createElementBlock"])("div",re,[Object(a["createElementVNode"])("div",ce,[Object(a["createVNode"])(u,{shadow:"never"},{default:Object(a["withCtx"])((function(){return[ie,Object(a["createElementVNode"])("div",se,[ue,Object(a["createVNode"])(c,{ref:function(e,n){n["formCom"]=e,i.value=e},model:Object(a["unref"])(s),rules:Object(a["unref"])(d),onKeyup:Object(a["withKeys"])(p,["enter"])},{default:Object(a["withCtx"])((function(){return[Object(a["createVNode"])(o,{prop:"userName"},{default:Object(a["withCtx"])((function(){return[Object(a["createVNode"])(t,{modelValue:Object(a["unref"])(s).userName,"onUpdate:modelValue":n[0]||(n[0]=function(e){return Object(a["unref"])(s).userName=e}),modelModifiers:{trim:!0},type:"text",placeholder:e.$t("common.username")},null,8,["modelValue","placeholder"])]})),_:1}),Object(a["createVNode"])(o,{prop:"password"},{default:Object(a["withCtx"])((function(){return[Object(a["createVNode"])(t,{modelValue:Object(a["unref"])(s).password,"onUpdate:modelValue":n[1]||(n[1]=function(e){return Object(a["unref"])(s).password=e}),type:"password",placeholder:e.$t("common.password")},null,8,["modelValue","placeholder"])]})),_:1}),Object(a["createVNode"])(r,{type:"primary",class:"login",onClick:p},{default:Object(a["withCtx"])((function(){return[Object(a["createTextVNode"])(Object(a["toDisplayString"])(e.$t("common.login")),1)]})),_:1})]})),_:1},8,["model","rules","onKeyup"])])]})),_:1})])])}}});t("c5c2");const de=le;var he=de,me=t("a007"),pe="Login",fe="ChangePassword",be=[{path:"/",name:"Home",redirect:"/configuration/south-driver"},{path:"/monitoring",name:"Monitoring",meta:{title:"data.monitoring"},component:Z,children:[{path:"data",name:"DataMonitoring",component:function(){return Promise.all([t.e("chunk-5d2e48d2"),t.e("chunk-92f4f370")]).then(t.bind(null,"c851"))},meta:{hiddenBreadcrumb:!0}}]},{path:"/configuration/north-driver",name:"NorthAPP",meta:{title:"config.northAppSetup"},component:Z,children:[{path:"",name:"NorthDriver",component:function(){return Promise.all([t.e("chunk-c1b1e2a2"),t.e("chunk-371400a5"),t.e("chunk-18d731da"),t.e("chunk-032317e3")]).then(t.bind(null,"8ea7"))},meta:{hiddenBreadcrumb:!0}},{path:":node/:plugin",name:"NorthDriverGroup",component:function(){return Promise.all([t.e("chunk-c1b1e2a2"),t.e("chunk-24713b76")]).then(t.bind(null,"7a6b"))},meta:{title:"config.groupList"}},{path:"config/:node",name:"NorthDriverConfig",component:function(){return Promise.all([t.e("chunk-5d2e48d2"),t.e("chunk-575dac77")]).then(t.bind(null,"5492"))},props:{direction:me["a"].North},meta:{title:"config.appConfig"}}]},{path:"/configuration/south-driver",name:"SouthDevice",meta:{title:"config.southDeviceManagement"},component:Z,children:[{path:"",name:"SouthDriver",component:function(){return Promise.all([t.e("chunk-c1b1e2a2"),t.e("chunk-371400a5"),t.e("chunk-18d731da"),t.e("chunk-3030e2a2")]).then(t.bind(null,"3df6"))},meta:{hiddenBreadcrumb:!0}},{path:"config/:node",name:"SouthDriverConfig",component:function(){return Promise.all([t.e("chunk-5d2e48d2"),t.e("chunk-575dac77")]).then(t.bind(null,"5492"))},props:{direction:me["a"].South},meta:{title:"config.deviceConfig"}},{path:":node/:plugin",name:"SouthDriverGroupG",component:function(){return t.e("chunk-2d0c4d7b").then(t.bind(null,"3d0d"))},meta:{title:"config.groupList"},children:[{path:"",name:"SouthDriverGroup",component:function(){return Promise.all([t.e("chunk-5d2e48d2"),t.e("chunk-3e4b8485"),t.e("chunk-5e9a9aa7")]).then(t.bind(null,"0579"))}},{path:":group",name:"SouthGroupTags",meta:{title:"config.tagList"},component:function(){return t.e("chunk-2d0c4d7b").then(t.bind(null,"3d0d"))},children:[{path:"",name:"SouthDriverGroupTag",component:function(){return Promise.all([t.e("chunk-5d2e48d2"),t.e("chunk-3e4b8485"),t.e("chunk-321d270e"),t.e("chunk-be9e2788")]).then(t.bind(null,"0ac6"))}},{path:"add",name:"SouthDriverGroupAddTag",component:function(){return Promise.all([t.e("chunk-5d2e48d2"),t.e("chunk-3e4b8485"),t.e("chunk-6fb67a99"),t.e("chunk-325e06e2")]).then(t.bind(null,"5a70"))},meta:{title:"config.addTags"}}]}]}]},{path:"/configuration/template",name:"Template",meta:{title:"config.templateManagement"},component:Z,children:[{path:"",name:"Template",component:function(){return Promise.all([t.e("chunk-371400a5"),t.e("chunk-3b72ccb7")]).then(t.bind(null,"9b3a"))},meta:{hiddenBreadcrumb:!0}},{path:":template/:plugin",name:"TemplateGroupG",component:function(){return t.e("chunk-2d0c4d7b").then(t.bind(null,"3d0d"))},meta:{title:"config.groupList"},children:[{path:"",name:"TemplateGroup",component:function(){return Promise.all([t.e("chunk-5d2e48d2"),t.e("chunk-3e4b8485"),t.e("chunk-5cfaa5c2")]).then(t.bind(null,"11c3"))}},{path:":group",name:"TemplateTags",meta:{title:"config.tagList"},component:function(){return t.e("chunk-2d0c4d7b").then(t.bind(null,"3d0d"))},children:[{path:"",name:"TemplateGroupTag",component:function(){return Promise.all([t.e("chunk-5d2e48d2"),t.e("chunk-3e4b8485"),t.e("chunk-321d270e"),t.e("chunk-a1b3580c")]).then(t.bind(null,"94f9"))}},{path:"add",name:"TemplateGroupAddTag",component:function(){return Promise.all([t.e("chunk-5d2e48d2"),t.e("chunk-3e4b8485"),t.e("chunk-6fb67a99"),t.e("chunk-d5975f04")]).then(t.bind(null,"efb5"))},meta:{title:"config.addTags"}}]}]}]},{path:"/configuration/plugin",name:"Plugin",meta:{title:"config.plugin"},component:Z,children:[{path:"",name:"Plugin",component:function(){return Promise.all([t.e("chunk-c1b1e2a2"),t.e("chunk-11202032")]).then(t.bind(null,"477d"))},meta:{hiddenBreadcrumb:!0}}]},{path:"/admin",name:"Admin",component:Z,children:[{path:"change-password",name:fe,meta:{title:"admin.changePassword",requireAuth:!0,hiddenBreadcrumb:!0},component:function(){return t.e("chunk-618f7912").then(t.bind(null,"ec78"))}}]},{path:"/license",name:"LicensePage",meta:{title:"admin.license"},component:Z,children:[{path:"/license",name:"License",meta:{requireAuth:!0},component:function(){return t.e("chunk-7b4ab6ba").then(t.bind(null,"acc1"))}}]},{path:"/about",name:"AboutPage",meta:{title:"common.about"},component:Z,children:[{path:"",name:"About",meta:{requireAuth:!0},component:function(){return t.e("chunk-67de72a8").then(t.bind(null,"ef4c"))}}]},{path:"/login",name:pe,component:he,meta:{hiddenBreadcrumb:!0}}];n["c"]=be},"55b6":function(e,n,t){"use strict";t.r(n);t("d3b7"),t("159b"),t("b64b");var a=t("47e2"),o=t("0613"),r=t("eb58"),c=["data","common","config","admin","error","ekuiper","template"],i={en:{},zh:{}},s=Object(r["b"])(),u=s.initLang;c.forEach((function(e){var n=t("0519")("./".concat(e)).default;Object.keys(n).forEach((function(t){var a=n[t],o=a.en,r=a.zh;i.en[e]=i.en[e]||{},i.zh[e]=i.zh[e]||{},i.en[e][t]=o,i.zh[e][t]=r}))}));var l={en:i.en,zh:i.zh},d=Object(a["a"])({legacy:!1,globalInjection:!0,locale:o["a"].state.lang,fallbackLocale:"zh",messages:l,warnHtmlMessage:!1});u(),n["default"]=d},5898:function(e,n,t){e.exports=t.p+"img/img-login.bbe2ed63.png"},"5d41":function(e,n,t){"use strict";t.r(n),n["default"]={streamProcessing:{zh:"数据流处理",en:"Data Streaming"},sources:{zh:"源管理",en:"Sources"},rule:{zh:"规则",en:"Rules"},extension:{zh:"拓展",en:"Extension"},configuration:{zh:"配置",en:"Configuration"}}},6865:function(e,n,t){},"6a4f":function(e,n,t){"use strict";t.d(n,"c",(function(){return o})),t.d(n,"e",(function(){return r})),t.d(n,"d",(function(){return c})),t.d(n,"b",(function(){return i})),t.d(n,"a",(function(){return s}));var a=t("e423"),o=function(){return a["a"].get("/license",{_handleErrorSelf:!0})},r=function(e){return a["a"].post("/license",{license:e},{_compatibleErrorCode:!0,name:"uploadLicense"})},c=function(){return a["a"].get("/version")},i=function(){return a["a"].get("/hwtoken")},s=function(){return Object(a["a"])({url:"/logs",method:"get",responseType:"blob",timeout:18e5})}},"6b3c":function(e,n,t){},"73ec":function(e,n,t){"use strict";t.d(n,"c",(function(){return h})),t.d(n,"n",(function(){return m})),t.d(n,"e",(function(){return p})),t.d(n,"d",(function(){return f})),t.d(n,"m",(function(){return b})),t.d(n,"i",(function(){return z})),t.d(n,"g",(function(){return g})),t.d(n,"b",(function(){return v})),t.d(n,"o",(function(){return O})),t.d(n,"a",(function(){return w})),t.d(n,"h",(function(){return j})),t.d(n,"l",(function(){return k})),t.d(n,"f",(function(){return N})),t.d(n,"k",(function(){return S})),t.d(n,"j",(function(){return x})),t.d(n,"q",(function(){return T})),t.d(n,"p",(function(){return E}));var a=t("ade3"),o=t("5530"),r=t("4f96"),c=(t("d81d"),t("4de4"),t("d3b7"),t("b64b"),t("fb6a"),t("a15b"),t("99af"),t("caad"),t("2532"),t("25f0"),t("a9e3"),t("b0c0"),t("b680"),t("4ec9"),t("3ca3"),t("ddb0"),t("159b"),t("55b6")),i=t("9613"),s=t("1146"),u=t("d472"),l=t("2ef0"),d=function(e){var n=Object(r["a"])(e),t=n[0],a=n.slice(1);return t.toLowerCase()+a.join("")},h=function(e,n){var t=c["default"].global.t,a=c["default"].global.locale,o=n;"en"===a.value&&(o=d(o));var r=t("input"===e?"common.inputRequired":"common.selectRequired"),i="".concat(r).concat(o);return i},m=function(e,n,t){return e.slice((t-1)*n,t*n)},p=function(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:6,n="ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678",t=n.length,a="",o=0;o<e;o+=1)a+=n.charAt(Math.floor(Math.random()*t));return a},f=function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"id";return e.reduce((function(e,t){return e[t[n]]=t,e}),Object.create(null))},b=function(e,n){var t=Object.keys(n),a=Object.keys(e);return t.every((function(e){return a.includes(e)}))},z=function(e){var n=i["d"].includes(e);return n?c["default"].global.t("error.".concat(e)):"unknown"},g=function(e,n){return new Promise((function(t,a){try{var o=s["utils"].aoa_to_sheet(e),r=s["utils"].book_new();s["utils"].book_append_sheet(r,o,"SheetJS"),Object(s["writeFile"])(r,"".concat(n,".xlsx")),t(!0)}catch(c){a(c)}}))},v=function(){var e=window.location,n=e.hostname,t=e.port,a=e.protocol,o=e.origin,r=t?"".concat(a,"//").concat(n,":").concat(Number(t).toString()):o;return"".concat(r,"/api/v2")},O=function(e,n){var t,a=n||{},o=a.compatibleErrorCode,r=a.name,s=void 0===r?"":r,l=e>=10701&&e<=10744?10701:e,d=null===(t=i["h"][s])||void 0===t?void 0:t.includes(l),h=o&&d?c["default"].global.t("error.".concat(s).concat(l)):"Error (code: ".concat(l,"): ").concat(z(l));Object(u["EmqxMessage"])({message:h,type:"error",dangerouslyUseHTMLString:!0})},w=function(e,n){var t=e.map((function(e){return Object(l["omit"])(e,n)}));return t},y=function(e,n){var t="",a=null;if("byte"===n){a=e/1073741824>1;var o=a?e/1073741824:e/1048576;t=o.toFixed(2)}else if("kb"===n){a=e/1048576>1;var r=a?e/1048576:e/1024;t=r.toFixed(2)}else if("mb"===n){a=e/1024>1;var c=a?e/1024:e;t=c.toFixed(2)}else"gb"===n?t=e.toFixed(2):"tb"===n&&(t=(1024*e).toFixed(2));var i=a?"G":"M";return"".concat(t," ").concat(i)},j=function(e,n){if(!e)return"0 M";var t=(null===n||void 0===n?void 0:n.toLocaleLowerCase())||"byte",a=new Map([["byte",y(e,t)],["kb",y(e,t)],["mb",y(e,t)],["gb",y(e,t)],["tb",y(e,t)]]);return a.get(t)||""},k=function(e,n,t){if(!e.length)return[];var a=Object(l["cloneDeep"])(e),o=Object(l["orderBy"])(a,[n],[t]);return o},N=function(e){var n=Object.prototype.toString.call(e),t=new Map([["[object String]","string"],["[object Number]","number"],["[object Boolean]","boolean"],["[object Undefined]","undefined"],["[object Object]","object"],["[object Array]","array"],["[object Null]","null"],["[object RegExp]","RegExp"],["[object Symbol]","symbol"],["[object JSON]","json"],["[object Math]","math"],["[object File]","file"],["[object Blob]","blob"],["[object ArrayBuffer]","arrayBuffer"],["default","object"]]);return t.get(n)||t.get("default")},S=function(e,n){var t,a,o=e.matched,r=n.matched,c=null===(t=o[0])||void 0===t?void 0:t.name,i=null===(a=r[0])||void 0===a?void 0:a.name;return c===i},x=function(e){try{return JSON.parse(e),Promise.resolve(!0)}catch(n){return console.error(n),Promise.reject(n)}},T=function(e,n){var t=e,r=Object.keys(n);return r.length?(r.forEach((function(e){var r=n[e];r&&(t=Object(o["a"])(Object(o["a"])({},t),{},Object(a["a"])({},e,r)))})),t):t},E=function(e){for(var n=e||1,t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",a=t.length,o="",r=0;r<n;r+=1){var c=Math.floor(Math.random()*a);o+=t.charAt(c)}return o}},"7d7a":function(e,n,t){"use strict";t.r(n),n["default"]={1e3:{zh:"通用错误",en:"Generic error"},1001:{zh:"内部错误",en:"Internal error"},1002:{zh:"请求 body 无效",en:"Request body invalid"},1003:{zh:"请求 param 无效",en:"Request param invalid"},1004:{zh:"缺少令牌",en:"Missing token"},1005:{zh:"解码令牌错误",en:"Decoding token error"},1006:{zh:"令牌过期",en:"Expired token"},1007:{zh:"验证令牌错误",en:"Validate token error"},1008:{zh:"无效令牌",en:"Invalid token"},1009:{zh:"用户名或密码错误",en:"User or password error"},1010:{zh:"程序繁忙",en:"Is busy"},1011:{zh:"文件不存在",en:"File not exist"},1012:{zh:"密码长度太短或太长",en:"Password length too short or too long"},1013:{zh:"密码重复",en:"Duplicate password"},1014:{zh:"执行指令失败",en:"Command execution failed"},1015:{zh:"IP 地址无效",en:"Invalid ip address"},1016:{zh:"IP 地址已占用",en:"IP address in use"},1017:{zh:"用户名无效",en:"Invalid user"},1018:{zh:"密码无效",en:"Invalid password"},2002:{zh:"Node 已存在",en:"Node exist"},2003:{zh:"Node 不存在",en:"Node not exist"},2004:{zh:"Node 设置无效",en:"Node setting invalid"},2005:{zh:"Node 设置未找到",en:"Node setting not found"},2006:{zh:"Node 未准备好",en:"Node not ready"},2007:{zh:"Node 正在运行",en:"Node is running"},2008:{zh:"Node 未运行",en:"Node not running"},2009:{zh:"Node 已停止",en:"Node is stopped"},2010:{zh:"Node 名称过长",en:"Node name too long"},2011:{zh:"Node 不允许删除",en:"Node not allow delete"},2012:{zh:"Node 不允许订阅",en:"Node not allow subscribe"},2013:{zh:"Node 不允许更新",en:"Node not allow update"},2014:{zh:"Node 不支持图",en:"Node not allow map"},2015:{zh:"Node 名称不允许为空",en:"Node name is empty"},2101:{zh:"组已经被订阅",en:"Group already subscribed"},2102:{zh:"组未被订阅",en:"Group not subscribe"},2103:{zh:"组不允许",en:"Group not allow"},2104:{zh:"组已存在",en:"Group exist"},2105:{zh:"组参数无效",en:"Group parameter invalid"},2106:{zh:"组不存在",en:"Group not exist"},2107:{zh:"组名称过长",en:"Group name too long"},2201:{zh:"点位不存在",en:"Tag not exist"},2202:{zh:"点位名称冲突",en:"Tag name conflict"},2203:{zh:"点位属性不支持",en:"Tag attribute not support"},2204:{zh:"点位类型不支持",en:"Tag type not support"},2205:{zh:"点位地址格式无效",en:"Tag address format invalid"},2206:{zh:"点位名称过长",en:"Tag name too long"},2207:{zh:"点位地址过长",en:"Tag address too long"},2208:{zh:"点位描述过长",en:"Tag description too long"},2209:{zh:"点位精度无效",en:"Tag precision invalid"},2210:{zh:"点位已存在",en:"Tag exist"},2301:{zh:"库未找到",en:"Library not found"},2302:{zh:"库信息无效",en:"Library info invalid"},2303:{zh:"库名称冲突",en:"Library name conflict"},2304:{zh:"库打开失败",en:"Library failed to open"},2305:{zh:"库模块无效",en:"Libraray module invalid"},2306:{zh:"系统库不允许删除",en:"Library system not allow del"},2307:{zh:"插件不允许实例化",en:"Library not allow create instance"},2308:{zh:"插件不支持此架构",en:"Library arch not support"},2400:{zh:"License 未找到",en:"License not found"},2401:{zh:"License 无效",en:"License invalid"},2402:{zh:"License 过期",en:"License expired"},uploadLicense2402:{zh:'您的 License 已过期，\n    请<a target="_blank" rel="noopener norefferrer" href="https://www.emqx.com/zh/apply-licenses/neuron">更新 License</a>\n    或联系销售人员更新 License。',en:'Your License has expired,\n    please <a target="_blank" rel="noopener norefferrer" href="https://www.emqx.com/zh/apply-licenses/neuron">update the License</a>\n    or contact the sales staff to update the License.'},2403:{zh:"License 未启用插件",en:"Plugin disabled by license"},uploadLicense2403:{zh:"导入 License 失败，请先删除 License 不包含的驱动",en:"Failed to import the License, please delete the driver not included in the License."},2404:{zh:"达到 license 授权的最大节点数",en:"Reach licensed max number of nodes"},uploadLicense2404:{zh:'节点数超过 License 限制，Neuron 无法正常使用，\n    请<a target="_blank" rel="noopener norefferrer" href="https://www.emqx.com/zh/apply-licenses/neuron">更新 License</a>\n    或删除部分驱动节点。',en:'The count of nodes exceeds the license limit, and Neuron cannot be used normally.\n    Please <a target="_blank" rel="noopener norefferrer" href="https://www.emqx.com/zh/apply-licenses/neuron">update the license</a>\n    or delete some driver nodes.'},addDriverByPlugin2404:{zh:"节点数超过 License 限制，创建驱动失败",en:"The count of nodes exceeds the limit of the license, and the creation of the driver fails"},addDriverByTemplate2404:{zh:"节点数超过 License 限制，创建驱动失败",en:"The count of nodes exceeds the limit of the license, and the creation of the driver fails"},2405:{zh:"达到 license 授权的节点最大点位数",en:"Reach licensed max number of tags per node"},uploadLicense2405:{zh:'点位数超过 License 限制，Neuron 无法正常使用，\n    请<a target="_blank" rel="noopener norefferrer" href="https://www.emqx.com/zh/apply-licenses/neuron">更新 License</a>\n    或删除部分点位。',en:'The count of data tags exceeds the license limit, and Neuron cannot be used normally.\n    Please <a target="_blank" rel="noopener norefferrer" href="https://www.emqx.com/zh/apply-licenses/neuron">update the license</a>\n    or delete some data tags.'},addDriverByTemplate2405:{zh:"点位数超过 License 限制，模版创建驱动失败",en:"The count of data points exceeds the limit of the license, and the creation of the driver fails"},addTagByNode2405:{zh:"点位数超过 License 限制，添加失败",en:"The count of data points exceeds the license limit, and the addition fails"},importTag2405:{zh:"点位数超过 License 限制，导入失败",en:"The count of data points exceeds the license limit, and the import fails"},2406:{zh:"License 硬件不匹配",en:"License hardware token not match"},uploadLicense2406:{zh:'License 与该硬件不匹配，Neuron 无法正常使用，\n    请<a target="_blank" rel="noopener norefferrer" href="https://www.emqx.com/zh/apply-licenses/neuron">更新 License</a>\n    或联系销售人员更新 License。',en:'The license does not match the hardware, and Neuron cannot be used normally.\n    Please <a target="_blank" rel="noopener norefferrer" href="https://www.emqx.com/zh/apply-licenses/neuron">update the license</a>\n    or contact the sales staff to update the License.'},2407:{zh:"License 检测到时钟异常",en:"License detect bad clock"},2408:{zh:"License 模块无效",en:"License module invalid"},2500:{zh:"模板已存在",en:"Template exist"},2501:{zh:"模板未找到",en:"Template not found"},2502:{zh:"模板名称太长",en:"Template name too long"},3e3:{zh:"插件读失败",en:"Plugin read failure"},3001:{zh:"插件写失败",en:"Plugin write failure"},3002:{zh:"插件未连接",en:"Plugin disconnected"},3003:{zh:"插件 tag 不允许读",en:"Plugin tag not allow read"},3004:{zh:"插件 tag 不允许写",en:"Plugin tag not allow write"},3007:{zh:"插件 tag 类型不匹配",en:"Plugin tag type mismatch"},3008:{zh:"插件 tag 值失效",en:"Plugin tag value expired"},3009:{zh:"插件协议解析失败",en:"Plugin protocol decode failure"},3010:{zh:"插件未运行",en:"Plugin not running"},3011:{zh:"插件 tag 未就绪",en:"Plugin tag not ready"},3012:{zh:"插件报文乱序",en:"Plugin packet out of order"},3013:{zh:"插件名称太长",en:"Plugin name too long"},3014:{zh:"插件未找到",en:"Plugin not found"},3015:{zh:"插件设备未回复",en:"Plugin device not response"},3016:{zh:"插件不支持模板",en:"Plugin not support template"},3017:{zh:"插件不支持写点位",en:"Plugin not support write tags"},4100:{zh:"字符串太长",en:"String too long"},4101:{zh:"打开文件失败",en:"File open failure"},4102:{zh:"读文件失败",en:"File read failure"},4103:{zh:"写文件失败",en:"File write failure"},10001:{zh:"Opcua 点位不存在",en:"Opcua tag does not exist"},10002:{zh:"Opcua 连接配置错误",en:"Opcua connection configuration error"},10003:{zh:"Opcua 访问超时",en:"Opcua access timeout"},10004:{zh:"Opcua 点位不可读",en:"Opcua tag is not readable"},10005:{zh:"Opcua 点位不可写",en:"Opcua tag is not writable"},10006:{zh:"Opcua 点位不支持",en:"Opcua tag is not supported"},10101:{zh:"硬件错误",en:"S7comm hardware error"},10103:{zh:"对象无访问权限",en:"S7comm accessing the object not allowed"},10105:{zh:"无效地址",en:"S7comm invalid address"},10106:{zh:"数据类型不支持",en:"S7comm data type not supported"},10107:{zh:"数据类型不一致",en:"S7comm data type inconsistent"},10110:{zh:"对象不存在",en:"S7comm object not exist"},10150:{zh:"COTP 连接断开",en:"S7comm cotp disconnected"},10151:{zh:"S7 连接断开",en:"S7comm disconnected"},10152:{zh:"没有值",en:"S7comm no value"},10153:{zh:"值长度太短",en:"S7comm value too short"},10200:{zh:"设备不存在",en:"Knx no devices"},10400:{zh:"无效地址",en:"Nona11 invalid address"},10500:{zh:"Fins 连接断开",en:"Fins disconnected"},10501:{zh:"Fins 错误",en:"Fins error"},10502:{zh:"本地节点错误",en:"Fins local node error"},10503:{zh:"目标节点错误",en:"Fins dest node error"},10504:{zh:"控制器错误",en:"Fins communication controller error"},10505:{zh:"服务不受支持",en:"Fins not executable"},10506:{zh:"路由表错误",en:"Fins routing error"},10507:{zh:"命令格式错误",en:"Fins command format error"},10508:{zh:"参数错误",en:"Fins parameter error"},10509:{zh:"无法读取",en:"Fins read not possible"},10510:{zh:"无法写入",en:"Fins write not possible"},10511:{zh:"当前模式不可执行",en:"Fins not executable in current mode"},10512:{zh:"单元不存在",en:"Fins no unit"},10513:{zh:"无法启动/停止",en:"Fins start/stop not possible"},10514:{zh:"单元错误",en:"Fins unit error"},10515:{zh:"命令错误",en:"Fins command error"},10516:{zh:"访问权限错误",en:"Fins access error"},10517:{zh:"中止",en:"Fins abort"},10600:{zh:"Focas 错误",en:"Focas error"},10701:{zh:"EtherNet/IP 错误",en:"EtherNet/IP error"},10797:{zh:"EtherNet/IP 没有 CIP 连接",en:"EtherNet/IP no CIP connection"},10798:{zh:"EtherNet/IP 数据类型不匹配",en:"EtherNet/IP data type mismatch"},10799:{zh:"EtherNet/IP 未注册session",en:"EtherNet/IP no session"},10800:{zh:"Profinet IO 未识别",en:"Profinet IO unidentified"},10801:{zh:"Profinet IO 未连接",en:"Profinet IO not connected"},10802:{zh:"Profinet IO 未准备好",en:"Profinet IO not ready"},10803:{zh:"Profinet IO 参数未准备好",en:"Profinet IO not param end"},10804:{zh:"Profinet IO 没有写入权限",en:"Profinet IO not DWRITE"},10805:{zh:"Profinet IO 等待 HELLO 响应",en:"Profinet IO wait HELLO"}}},"82da":function(e,n,t){},9386:function(e,n,t){"use strict";t("34b9")},"94ee":function(e,n,t){"use strict";t.r(n),n["default"]={templateName:{zh:"模板名称",en:"Template Name"},addTemplate:{zh:"添加模板",en:"Add Template"},editTemplate:{zh:"编辑模板",en:"Edit Template"},pluginName:{zh:"插件名称",en:"Plugin name"},importTemplate:{zh:"导入模板",en:"Import Template"},deleteTemplateTip:{zh:"操作不可撤回，删除后会导致模版关联的节点和组也被删除，是否确认删除？",en:"The operation is irreversible. After deleting, the nodes and groups associated with the template will also be deleted. Do you confirm the deletion?"},templateRemark:{zh:"用于创建一个包含组和点位相关配置的设备实例。可基于模板创建多个具有相同点位的设备节点",en:"Used to create a device instance that contains group and point-related configurations. Multiple device nodes with the same point can be created based on a template"},templateLink:{zh:"模板文档",en:"Template document"},missingNameInFile:{zh:"缺少名称，请补充名称",en:"The name is missing, please add the plugin"},missingPluginInFile:{zh:"缺少插件，请补充插件",en:"The plugin is missing, please add the plugin"}}},9613:function(e,n,t){"use strict";t.d(n,"g",(function(){return o})),t.d(n,"i",(function(){return r})),t.d(n,"f",(function(){return c})),t.d(n,"d",(function(){return i})),t.d(n,"h",(function(){return s})),t.d(n,"e",(function(){return u})),t.d(n,"b",(function(){return l})),t.d(n,"a",(function(){return d})),t.d(n,"c",(function(){return h}));var a=t("a007"),o=[a["a"].North],r=[a["a"].South],c="0x",i=[1e3,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,2002,2003,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013,2014,2015,2101,2102,2103,2104,2105,2106,2107,2201,2202,2203,2204,2205,2206,2207,2208,2209,2210,2301,2302,2303,2304,2305,2306,2307,2308,2400,2401,2402,2403,2404,2405,2406,2407,2408,2500,2501,2502,3e3,3001,3002,3003,3004,3007,3008,3009,3010,3011,3012,3013,3014,3015,3016,3017,4100,4101,4102,4103,10101,10103,10105,10106,10107,10110,10150,10151,10152,10153,10200,10400,10500,10501,10502,10503,10504,10505,10506,10507,10508,10509,10510,10511,10512,10513,10514,10515,10516,10517,10600,10001,10002,10003,10004,10005,10006,10701,10702,10703,10704,10705,10706,10707,10708,10709,10710,10711,10712,10713,10714,10715,10716,10717,10718,10719,10720,10721,10722,10723,10724,10725,10726,10727,10728,10729,10730,10731,10732,10733,10734,10735,10736,10737,10738,10739,10740,10741,10742,10743,10744,10744,10797,10798,10799,10800,10801,10802,10803,10804,10805],s={uploadLicense:[2402,2403,2404,2405,2406],addDriverByPlugin:[2404],addDriverByTemplate:[2404,2405]},u=" ",l="default-dashboard-adapter",d=[l,"default-persist-adapter"],h="en"},"9c75":function(e,n,t){"use strict";t.r(n),n["default"]={driver:{zh:"驱动",en:"Driver"},app:{zh:"应用",en:"Application"},nodeType:{zh:"节点类型",en:"Node type"},config:{zh:"配置",en:"Configuration"},northAppSetup:{zh:"北向应用",en:"North Apps"},southDeviceManagement:{zh:"南向设备",en:"South Devices"},appName:{zh:"应用名称",en:"Name of Application "},addApplication:{zh:"添加应用",en:"Add Application"},addDevice:{zh:"添加设备",en:"Add Device"},addApp:{zh:"添加应用",en:"Add App"},editApp:{zh:"编辑应用",en:"Edit App"},workStatus:{zh:"工作状态",en:"Status"},north:{zh:"北向",en:"North"},south:{zh:"南向",en:"South"},groupList:{zh:"组列表",en:"Group List"},tagList:{zh:"点位列表",en:"Tag List"},addTags:{zh:"添加点位",en:"Add Tags"},editTag:{zh:"编辑点位",en:"Edit Tag"},tagNum:{zh:"点位数目",en:"Number of tags"},tagName:{zh:"点位名称",en:"Tag Name"},tag:{zh:"点位",en:"Tag"},address:{zh:"地址",en:"Address"},decimal:{zh:"乘系数",en:"Decimal"},precision:{zh:"精度",en:"Precision"},tagValue:{zh:"值",en:"Value"},groupName:{zh:"组名称",en:"Group Name"},createGroup:{zh:"创建组",en:"Create Group"},editGroup:{zh:"编辑组",en:"Edit Group"},viewGroup:{zh:"查看组",en:"View Group"},deviceName:{zh:"设备名称",en:"Device Name"},newDevice:{zh:"新增设备",en:"New Device"},editDevice:{zh:"编辑设备",en:"Edit Device"},addr:{zh:"地址",en:"Addr"},addressRequired:{zh:"地址是必填项，请单击地址按钮以配置地址！",en:"Address is requried, please click the addr button to configure the address!"},suffix:{zh:"后缀",en:"Suffix"},prefix:{zh:"前缀",en:"Prefix"},event:{zh:"事件",en:"Event"},overview:{zh:"概览",en:"Overview"},deviceConnectNum:{zh:"设备连接数",en:"Number of device connections"},runningTime:{zh:"运行时长",en:"Up time"},moreApp:{zh:"更多应用",en:"More applications"},northApp:{zh:"北向应用",en:"North application"},moreDevices:{zh:"更多设备",en:"More devices"},southDevice:{zh:"南向设备",en:"South device"},templateManagement:{zh:"模板管理",en:"Template"},plugin:{zh:"插件",en:"Plugin"},addPlugin:{zh:"添加插件",en:"Add Plugin"},libName:{zh:"驱动库名称",en:"Lib name"},useFor:{zh:"关联节点类型",en:"Associated node type"},pluginKind:{zh:"插件类别",en:"Kind of plugin"},pluginKindPlaceholder:{zh:"请选择插件类型 ",en:"Select plugin type"},all:{zh:"全部",en:"All"},addSubscription:{zh:"添加订阅",en:"Add subscription"},appConfig:{zh:"应用配置",en:"Application configuration"},deviceConfig:{zh:"设备配置",en:"Device configuration "},unsubscribeGroupConfirm:{zh:"是否确定取消订阅该组",en:"Are you sure you want to unsubscribe this group"},clearSubscriptionConfirm:{zh:"是否确认清空订阅",en:"Are you sure to clear the subscription?"},unsubscribeInBulkConfirm:{zh:"是否确认取消订阅所选组",en:"Are you sure to unsubscribe from selected groups?"},noConfigInfoDesc:{zh:"没有需要配置的数据",en:"There is no data to configure."},init:{zh:"初始化",en:"Init"},ready:{zh:"准备好",en:"Ready"},running:{zh:"运行中",en:"Running"},stopped:{zh:"停止",en:"Stopped"},connectionStatus:{zh:"连接状态",en:"Connection status"},disconnected:{zh:"断开连接",en:"Disconnected"},connecting:{zh:"正在连接",en:"Connecting"},connected:{zh:"已连接",en:"Connected"},unsubscribe:{zh:"取消订阅",en:"unsubscribe"},viewAll:{zh:"查看全部",en:"View all"},numberOfNorthApp:{zh:"北向应用数",en:"North apps"},numberOfSouthDevices:{zh:"南向设备数",en:"South devices"},numberOfRunningInstances:{zh:"运行实例数",en:"Running instances"},southMessageCount:{zh:"总共南向采集消息数",en:"Total number of South collected messages"},northMessageCount:{zh:"总共北向发送消息数",en:"Total number of North messages sent"},groupNameRequired:{zh:"请填写组名称",en:"Please enter the group name"},nodeRequired:{zh:"请选择 Node",en:"Please select the node"},readIntervalRequired:{zh:"请填写 Read interval",en:"Please enter the read interval"},readIntervalMinimumError:{zh:"Read interval 最小值为 100 ms",en:"The minimum read interval is 100 ms"},readIntervalError:{zh:"请填写有效数字",en:"Please fill in a valid number"},tagNumExceedsTheMaximumError:{zh:"一次最多能添加 100 条点位数据，请重新输入点位数目。",en:"A maximum of 100 tags can be added at a time. Please re-enter the number of tags."},tagNameRequired:{zh:"请填写点位名称",en:"Please enter the tag name"},tagAddressRequired:{zh:"请填写点位地址",en:"Please enter the tag address"},tagAddressValid:{zh:"请填写有效的点位地址",en:"Please enter the valid tag address"},tagAttributeRequired:{zh:"请选择点位属性",en:"Please select tag attribute"},tagTypeRequired:{zh:"请选择点位类型",en:"Please select tag type"},tagValueRequired:{zh:"请输入点位的值",en:"Please enter tag value"},nameRequired:{zh:"请填写名称",en:"Please enter the name"},pluginRequired:{zh:"请选择插件",en:"Please select the plugin"},templateRequired:{zh:"请选择模板",en:"Please select the template"},southDeviceRequired:{zh:"请选择南向设备",en:"Please select south device"},group:{zh:"组",en:"Group"},groupRequired:{zh:"请选择 group",en:""},numberFormatError:{zh:"请输入数字",en:"Please enter a number"},numberRangeErrorMsg:{zh:"请输入 {min} - {max} 之间的数字",en:"Please enter a number between {min} - {max}"},numberMinimumErrorMsg:{zh:"请输入不小于 {min} 的数字",en:"Please enter a number not less than {min}"},numberMaximumErrorMsg:{zh:"请输入不大于 {max} 的数字",en:"Please enter a number no greater than {max}"},hexadecimalFormatError:{zh:"请输入格式正确的 16 进制数",en:"Please enter a hexadecimal number in the correct format"},decimalFormatError:{zh:"请输入格式正确的 10 进制数",en:"Please enter a decimal number in the correct format"},lengthRangeErrorMsg:{zh:"请输入长度在 {min} - {max} 之间的数据",en:"Please enter data between {min} - {max} in length"},lengthMinimumErrorMsg:{zh:"请输入长度不小于 {length} 的数据",en:"Please enter data with a length not less than {length}"},lengthMaximumErrorMsg:{zh:"请输入长度不大于 {length} 的数据",en:"Please enter data with a length no greater than {length}"},stringLengthErrorPrefix:{zh:"请输入",en:"Please enter "},stringLengthErrorSuffix:{zh:"个字符",en:" characters"},validHostError:{zh:"请输入有效的 host",en:"Please enter a valid host"},validTableError:{zh:"请上传具有有效数据的表格",en:"Please upload a form with valid data"},errorTableError:{zh:"请下载模版，根据模版填充表格数据再尝试上传",en:"Please download the template, fill in the form data according to the template and try to upload"},errorTableAddress:{zh:"表格第 {rowNum} 行的点位 {name} 的地址无效，请填入有效的地址后再尝试提交",en:"The address of tag {name} in row {rowNum} of the table. Please fill in valid address before attempting to submit"},errorStaticWithValue:{zh:"表格第 {rowNum} 行的点位 {name} 的属性包含 'Static'，请填入有效的值后再尝试提交",en:"The attribute of tag {name} in row {rowNum} of the table contains 'Static'. Please fill in valid value before attempting to submit"},uploadSuc:{zh:"上传成功",en:"Uploaded successfully"},tableRowDataError:{zh:"表格第 {rowNum} 行数据有误",en:"Incorrect data in row {rowNum} of the table"},downloadTemplate:{zh:"下载模版",en:"Download template"},tagTypeError:{zh:"当前设备插件能添加的点位的类型为 {typesStr}, 请调整表格数据后再尝试提交",en:"The type of the tag that can be added by the current device plugin is {typesStr}, please try to upload after adjusting the table data"},partialUploadFailed:{zh:"自第 {errorRow} 行开始的数据上传失败，失败原因：{reason}；请手动删除掉第 {errorRow} 行前的数据并检查后再次尝试上传",en:"Data upload from line {errorRow} failed, failure reason: {reason}; please manually delete the data before line {errorRow} and check and try uploading again"},clearUploadedFile:{zh:"清空已上传文件",en:"Clear uploaded file"},tagPartAddedFailedPopup:{zh:"部分点位添加失败，错误原因：{0}。 已过滤掉添加成功的 tag，请检查后再次尝试添加",en:"Some tags failed to be added, the reason for the error: {0}, the successfully added tags have been filtered out, please check and try to add again"},desc:{zh:"描述",en:"Description"},stopSuc:{zh:"停止成功",en:"Stop successfully"},runSuc:{zh:"运行成功",en:"Run successfully"},dataStatistics:{zh:"数据统计",en:"Data statistics"},delayTime:{zh:"延时",en:"Delay time"},updateDebugLogLevel:{zh:"DEBUG 日志",en:"DEBUG log"},modifyNodeLogLevelSuc:{zh:"已修改节点的日志等级为 debug，十分钟左右自动切回默认等级",en:"Updated node log level to debug, it will auto back to the default level in about 10 minutes"},enableNodeLogDebugSuc:{zh:"已开启 Debug 日志",en:"Debug log turned on"},disableNodeLogDebugSuc:{zh:"已关闭 Debug 日志",en:"Debug log turned off"},searchNodePlaceholder:{zh:"请输入节点名称",en:"Enter a node name"},searchSouthDevicePlaceholder:{zh:"请输入设备名称",en:"Please enter a device name"},topic:{zh:"主题",en:"Topic"},tagCounts:{zh:"点位数量",en:"Tag Counts"},interval:{zh:"间隔",en:"Interval"},selectPlugin:{zh:"请选择插件",en:"Select plugin"},topicContainWildcard:{zh:"不能向包含通配符 #、+ 的主题发布消息",en:"Cannot publish messages to topics containing wildcard characters #, +"},addDriverMode:{zh:"方式",en:"Mode"},templateMode:{zh:"模板",en:"Template"},pluginMode:{zh:"插件",en:"Plugin"},templatesSelectorPlaceholder:{zh:"请选择模板",en:"Select template"},southDevicePlaceholder:{zh:"请选择南向设备",en:"Select south device"},groupPlaceholder:{zh:"请选择组",en:"Select group"},enNumberFormatError:{zh:"请输入英文、数字",en:"Please enter English, number"},enNumberLengthError:{zh:"请输入长度小于 {max} 的数据",en:"Please enter data with length less than {max}"},linkState:{zh:"连接状态",en:"Connection status"},runningState:{zh:"工作状态",en:"Working status"},lastRttMs:{zh:"当前采集延迟",en:"Current acquisition delay"},tagReadsTotal:{zh:"点位读取数总计",en:"Total tag reads"},tagReadErrorsTotal:{zh:"点位读取错误数总计",en:"Total tag read errors"},groupTagsTotal:{zh:"采集组 {group} 点位数",en:"Group {group} tags total"},sendBytes:{zh:"发送字节数",en:"Send bytes"},recvBytes:{zh:"接收字节数",en:"Received bytes"},tagsTotal:{zh:"点位总数",en:"Tags total"},groupLastSendMsgs:{zh:"采集组 {group} 最近一次发送消息数",en:"Group {group} last send messages"},groupLastTimerMs:{zh:"采集组 {group} 最近一次执行消耗时间",en:"Group {group} last execution time"},groupLastErrorCode:{zh:"采集组 {group} 上一次采集错误信息",en:"Group {group} last error messages"},groupLastErrorTimestampMs:{zh:"采集组 {group} 上一次采集错误信息时间戳",en:"Group {group} last error messages timestamp"},sendMsgsTotal:{zh:"发送消息数总计",en:"Total messages sent"},sendMsgErrorsTotal:{zh:"发送消息失败数总计",en:"Total messages sent failures"},recvMsgsTotal:{zh:"下行消息指令数",en:"Total downlink messages commands"},cachedMsgs:{zh:"缓存消息数",en:"Cached messages"},subscribeSouthDriverData:{zh:"订阅南向驱动数据",en:"Subscription south drive data"},subscribeSouthDriverDataRequired:{zh:"请选择南向驱动数据",en:"Please select the south drive data"},staticNotSupportBytes:{zh:"Static 属性暂不支持 BYTES 类型",en:"Static attribute do not currently support BYTES type"},arrayFormatError:{zh:"请输入数组",en:"Please enter an array"},subscribeSouthDriverDeviceIllegal:{zh:"注册的设备，名称只能包含数字和字母",en:"Subscribed devices, names can only contain numbers and letters"}}},"9d64":function(e,n,t){e.exports=t.p+"img/logo.f1eda044.png"},a007:function(e,n,t){"use strict";var a,o,r,c,i,s,u,l,d,h,m,p,f,b,z,g,v;t.d(n,"a",(function(){return r})),t.d(n,"c",(function(){return c})),t.d(n,"l",(function(){return i})),t.d(n,"k",(function(){return s})),t.d(n,"j",(function(){return u})),t.d(n,"h",(function(){return l})),t.d(n,"e",(function(){return h})),t.d(n,"f",(function(){return m})),t.d(n,"d",(function(){return p})),t.d(n,"g",(function(){return f})),t.d(n,"b",(function(){return z})),t.d(n,"i",(function(){return v})),function(e){e["Restart"]="restart",e["Restartnew"]="restartnew",e["Shutdown"]="shutdown"}(a||(a={})),function(e){e["STANDBY"]="STANDBY",e["ACTIVE"]="ACTIVE"}(o||(o={})),function(e){e[e["North"]=2]="North",e[e["South"]=1]="South",e[e["Web"]=2]="Web"}(r||(r={})),function(e){e["South"]="driver",e["North"]="app",e["GLOBAL"]="global"}(c||(c={})),function(e){e["Int"]="int",e["String"]="string",e["Boolean"]="bool",e["Enum"]="enum",e["Map"]="map",e["File"]="file",e["Array"]="array"}(i||(i={})),function(e){e[e["INT8"]=1]="INT8",e[e["UINT8"]=2]="UINT8",e[e["INT16"]=3]="INT16",e[e["UINT16"]=4]="UINT16",e[e["INT32"]=5]="INT32",e[e["UINT32"]=6]="UINT32",e[e["INT64"]=7]="INT64",e[e["UINT64"]=8]="UINT64",e[e["FLOAT"]=9]="FLOAT",e[e["DOUBLE"]=10]="DOUBLE",e[e["BIT"]=11]="BIT",e[e["BOOL"]=12]="BOOL",e[e["STRING"]=13]="STRING",e[e["BYTES"]=14]="BYTES",e[e["WORD"]=16]="WORD",e[e["DWORD"]=17]="DWORD",e[e["LWORD"]=18]="LWORD"}(s||(s={})),function(e){e[e["Read"]=1]="Read",e[e["Write"]=2]="Write",e[e["Subscribe"]=4]="Subscribe",e[e["Static"]=8]="Static"}(u||(u={})),function(e){e[e["Static"]=0]="Static",e[e["System"]=1]="System",e[e["Custom"]=2]="Custom"}(l||(l={})),function(e){e[e["Stop"]=0]="Stop",e[e["Standby"]=1]="Standby",e[e["Activate"]=2]="Activate"}(d||(d={})),function(e){e[e["Start"]=0]="Start",e[e["Stop"]=1]="Stop"}(h||(h={})),function(e){e[e["Init"]=1]="Init",e[e["Ready"]=2]="Ready",e[e["Running"]=3]="Running",e[e["Stopped"]=4]="Stopped"}(m||(m={})),function(e){e[e["Disconnected"]=0]="Disconnected",e[e["Connected"]=1]="Connected"}(p||(p={})),function(e){e["True"]="required",e["False"]="optional"}(f||(f={})),function(e){e["All"]="",e["Trace"]="trace",e["Debug"]="debug",e["Info"]="info",e["Warn"]="warn",e["Error"]="error",e["Fatal"]="fatal"}(b||(b={})),function(e){e["Text"]="text",e["Binary"]="binary"}(z||(z={})),function(e){e["EthernetIp"]="ethernet-ip-cip",e["ADS"]="ads",e["A1E"]="mitsubishi-melsec-q-a1e",e["Focas"]="cnc-fanuc-focas",e["MQTT"]="MQTT",e["Modbus"]="Modbus",e["OPCUA"]="OPC UA",e["SiemensS7ISOTCP"]="Siemens S7 ISOTCP",e["OmronFinsOnTCP"]="OMRON FINS on TCP",e["MitsubishiMelsecQE71"]="Mitsubishi MELSEC-Q E71",e["IEC608705104"]="IEC 60870-5-104",e["KNXnetIP"]="KNXnet IP",e["BACnetIP"]="BACnet IP",e["DLT6452007"]="DL T645-2007",e["SparkplugB"]="Sparkplug B",e["zhNoA11"]="非 A11",e["enNoA11"]="Non A11"}(g||(g={})),function(e){e[e["decimal"]=10]="decimal",e[e["hexadecimal"]=16]="hexadecimal"}(v||(v={}))},aa74:function(e,n,t){"use strict";t.r(n),n["default"]={device:{zh:"设备",en:"Device"},list:{zh:"列表",en:"List"},system:{zh:"系统",en:"System"},systemStatus:{zh:"系统状态",en:"System status"},login:{zh:"登 录",en:"Login"},logout:{zh:"退出登录",en:"Logout"},user:{zh:"用户",en:"User"},username:{zh:"用户名",en:"Username"},password:{zh:"密 码",en:"Password"},userAdmin:{zh:"用户管理",en:"User Admin"},accountSettings:{zh:"账户设置",en:"Account Settings"},changePassword:{zh:"修改密码",en:"Change Password"},oldPassword:{zh:"原密码",en:"Old Password"},newPassword:{zh:"新密码",en:"New password"},confirmPassword:{zh:"新密码确认",en:"Password Confirmation"},newPassConfirmRequired:{zh:"请再次输入新密码",en:"Please enter a new password again"},newPassNotMatch:{zh:"两次密码不匹配",en:"Password does not match"},changePwSuccessful:{zh:"修改密码成功",en:"Change password successfully"},changePwFailed:{zh:"修改密码失败",en:"Failed to change password"},notFound:{zh:"页面没有找到",en:"page not found"},stop:{zh:"停止",en:"Stop"},start:{zh:"启动",en:"Start"},restart:{zh:"重启",en:"Restart"},standby:{zh:"待机",en:"Standby"},active:{zh:"开机",en:"Active"},shutdown:{zh:"关机",en:"Shutdown"},send:{zh:"发送",en:"Send"},add:{zh:"添加",en:"Add"},delete:{zh:"删除",en:"Delete"},batchDelete:{zh:"批量删除",en:"Batch Delete"},create:{zh:"创建 {name}",en:"Create {name}"},clear:{zh:"清空",en:"Clear"},submit:{zh:"提交",en:"Submit"},submitSuccess:{zh:"提交成功",en:"Submit success"},createSuccess:{zh:"创建成功",en:"Create success"},updateSuccess:{zh:"更新成功",en:"Update success"},operateConfirm:{zh:"操作确认",en:"Operation Confirmation"},operateSuccessfully:{zh:"操作成功",en:"Operate successfully"},cancel:{zh:"取消",en:"Cancel"},nextStep:{zh:"下一步",en:"Next"},edit:{zh:"编辑",en:"Edit"},actions:{zh:"动作",en:"Actions"},name:{zh:"名称",en:"Name"},generalStatistics:{zh:"通用数据统计",en:"General Statistics"},about:{zh:"关于",en:"About"},warning:{zh:"警告",en:"Warning"},import:{zh:"导入",en:"Import"},export:{zh:"导出",en:"Export"},upload:{zh:"上传",en:"Upload"},reUpload:{zh:"重新上传",en:"Re-upload"},uploadSuccess:{zh:"上传成功",en:"Upload success!"},batchDeleteError:{zh:"请先选择要删除的项",en:"Please select items to delete first"},confirmDelete:{zh:"您确定要删除吗？",en:"Are you sure delete?"},confirmClear:{zh:"操作不可撤回，是否确定要清空数据？",en:"The operation is irreversible. Are you sure you want to clear the data?"},confirmLogout:{zh:"确认退出登录？",en:"Are you sure logout?"},confirm:{zh:"确认",en:"Confirm"},confirmButtonText:{zh:"确认",en:"OK"},cancelButtonText:{zh:"取消",en:"Cancel"},back:{zh:"返回",en:"Back"},type:{zh:"类型",en:"Type"},attribute:{zh:"属性",en:"Attribute"},description:{zh:"描述",en:"Description"},lang:{zh:"语言",en:"Language"},emptyData:{zh:"暂无数据",en:"No Data"},save:{zh:"保存",en:"Save"},oper:{zh:"操作",en:"Operate"},date:{zh:"日期",en:"Date"},time:{zh:"时间",en:"Time"},pleaseSelect:{zh:"请选择",en:"please select"},home:{zh:"首页",en:"Home"},copyright:{zh:"版权",en:"Copyright"},second:{zh:"秒",en:"second | second | seconds"},minute:{zh:"分钟",en:"minute | minute | minutes"},hour:{zh:"小时",en:"hour | hour | hours"},day:{zh:"天",en:"day | day | days"},authorized:{zh:"已授权",en:"Authorized"},unauthorized:{zh:"未授权",en:"Unauthorized"},inputRequired:{zh:"请输入",en:"Please enter the "},selectRequired:{zh:"请选择",en:"Please select the "},fileRequired:{zh:"请上传文件",en:"Please upload files"},uploadFile:{zh:"上传文件",en:"Upload file"},readFileError:{zh:"读取文件时发生错误。",en:"An error occurred while reading the file."},validFileError:{zh:"请上传具有内容的文件",en:"Please upload a file with content"},close:{zh:"关闭",en:"Close"},view:{zh:"查看",en:"View"},systemInformation:{zh:"系统信息",en:"System Information"},reset:{zh:"重置",en:"Reset"},document:{zh:"文档",en:"Document"},normal:{zh:"正常",en:"Normal"},exceptions:{zh:"异常",en:"Exceptions"},keywordSearchPlaceholder:{zh:"输入关键字搜索",en:"Enter keyword search"},ms:{zh:"毫秒",en:"ms"},more:{zh:"更多",en:"more"},No:{zh:"序号",en:"No."},card:{zh:"卡片",en:"Card"},importSuccess:{zh:"导入成功",en:"Import success"},isNotFile:{zh:"不支持非文件类型",en:"Non-file types are not supported"},notJSONData:{zh:"非 JSON 数据",en:"Non-JSON data"},jsonFormatError:{zh:"数据格式发生错误，请检查数据",en:"Data format error, please check the data"},collapseAll:{zh:"全部折叠",en:"Collapse all"},expandAll:{zh:"全部展开",en:"Expand all"}}},afbc:function(e,n,t){"use strict";var a=t("1da1"),o=(t("96cf"),t("b0c0"),t("d3b7"),t("159b"),t("6c02")),r=t("5566"),c=t("0613"),i=Object(o["a"])({history:Object(o["b"])("/web/"),routes:r["c"]});i.beforeEach((function(e,n,t){var o=c["a"].state.axiosPromiseCancel;e.path===n.path||n.name===r["b"]?c["a"].commit("SET_AXIOS_PROMISE_CANCEL",[]):o.length&&(o.forEach(function(){var e=Object(a["a"])(regeneratorRuntime.mark((function e(n){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",n&&n());case 1:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}()),c["a"].commit("SET_AXIOS_PROMISE_CANCEL",[])),c["a"].state.token||"Login"===e.name?c["a"].state.token&&"Login"===e.name?t("/"):t():t({name:"Login"}),window.scroll(0,0)})),n["a"]=i},b20f:function(e,n,t){},c5c2:function(e,n,t){"use strict";t("1d4c")},cd49:function(e,n,t){"use strict";t.r(n);t("e260"),t("e6cf"),t("cca6"),t("a79d");var a=t("7a23"),o=t("d472"),r=t.n(o);function c(e,n){var t=Object(a["resolveComponent"])("router-view");return Object(a["openBlock"])(),Object(a["createBlock"])(a["Suspense"],null,{default:Object(a["withCtx"])((function(){return[Object(a["createVNode"])(t)]})),_:1})}var i=t("6b0d"),s=t.n(i);const u={},l=s()(u,[["render",c]]);var d=l,h=t("afbc"),m=t("0613"),p=t("55b6");t("85f9"),t("6b3c"),t("f5fa"),t("1c98"),t("1be0"),t("6865"),t("b20f"),t("82da"),t("0038");Object(a["createApp"])(d).use(p["default"]).use(r.a).use(m["a"]).use(h["a"]).mount("#neuron-dashboard")},e423:function(e,n,t){"use strict";var a=t("5530"),o=t("1da1"),r=(t("b0c0"),t("d3b7"),t("caad"),t("2532"),t("96cf"),t("bc3a")),c=t.n(r),i=t("d472"),s=t("afbc"),u=t("0613"),l=t("5566"),d=t("73ec"),h=t("2de2"),m=Object(d["b"])(),p={headers:{"Content-Type":"application/json","Cache-Control":"no-cache",Accept:"application/json"},baseURL:m,timeout:h["a"]};Object.assign(c.a.defaults,p);var f=function(){var e=Object(o["a"])(regeneratorRuntime.mark((function e(n,t,a){var o,r,c,s;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:o=t.statusText,r=t.status,c=o||r,s=new FileReader,s.readAsText(n,"utf-8"),s.onload=function(){var e=s.result||"";"string"===typeof e?Object(d["j"])(e).then((function(){var e=JSON.parse(s.result),n=e.error;n?Object(d["o"])(n,a):i["EmqxMessage"].error(c)})).catch((function(){i["EmqxMessage"].error(c)})):i["EmqxMessage"].error(c)};case 5:case"end":return e.stop()}}),e)})));return function(n,t,a){return e.apply(this,arguments)}}(),b=function(e){var n,t=e.response,a=e.message,o=e.config,r=o||{},c=r._compatibleErrorCode,s=r.name,u={compatibleErrorCode:!!c,name:s};if(null!==t&&void 0!==t&&null!==(n=t.data)&&void 0!==n&&n.error)Object(d["o"])(t.data.error,u);else if(null!==t&&void 0!==t&&t.data){var l=t.data,h=l.statusText,m=l.status,p=h||(null===t||void 0===t?void 0:t.statusText)||a,b=m||(null===t||void 0===t?void 0:t.status);if("blob"===Object(d["f"])(t.data))f(t.data,{statusText:p,status:b},u);else{var z=p||a||b;i["EmqxMessage"].error(z)}}else{var g=(null===t||void 0===t?void 0:t.statusText)||a||(null===t||void 0===t?void 0:t.status)||"unknow";i["EmqxMessage"].error(g)}},z=c.a.CancelToken;c.a.interceptors.request.use((function(e){return u["a"].state.token&&(e.headers=Object(a["a"])(Object(a["a"])({},e.headers),{},{Authorization:"Bearer ".concat(u["a"].state.token)})),e.cancelToken=new z((function(e){u["a"].commit("ADD_AXIOS_PROMISE_CANCEL",e)})),e}),(function(e){return Promise.reject(e)})),c.a.interceptors.response.use((function(e){var n=e.status,t=e.data,a=e.config,o=a||{},r=o._compatibleErrorCode,c=o.name,i={compatibleErrorCode:!!r,name:c};if(200!==n)Object(d["o"])(t.error,i);else if(null!==t&&void 0!==t&&t.error)return e.config._handleCustomError||Object(d["o"])(t.error,i),Promise.reject(e);return e}),(function(e){var n,t,a,o,r,i=[l["b"],l["a"]],d=(null===(n=s["a"].currentRoute)||void 0===n||null===(t=n.value)||void 0===t?void 0:t.name)||"",h=i.includes(d);if(401===(null===e||void 0===e||null===(a=e.response)||void 0===a?void 0:a.status)&&!h||403===(null===e||void 0===e||null===(o=e.response)||void 0===o?void 0:o.status))u["a"].commit("LOGOUT"),s["a"].push({name:"Login"});else{if(c.a.isCancel(e))return Promise.resolve();null!==e&&void 0!==e&&null!==(r=e.config)&&void 0!==r&&r._handleErrorSelf||b(e)}return Promise.reject(e)})),n["a"]=c.a},eb58:function(e,n,t){"use strict";t.d(n,"b",(function(){return m}));t("d3b7"),t("25f0");var a=t("7a23"),o=t("5502"),r=t("51d4"),c=t.n(r),i=t("3ef0"),s=t.n(i),u=t("b40f"),l=t.n(u),d=t("0613"),h=t("55b6"),m=function(){var e=function(){"zh"===d["a"].state.lang?c.a.use(s.a):c.a.use(l.a)},n=function(n){d["a"].commit("SET_LANG",n),h["default"].global.locale.value=n,e()};return{initLang:e,changeLang:n}};n["a"]=function(){var e=Object(o["b"])(),n=[{label:"中文",value:"zh"},{label:"English",value:"en"}],t=Object(a["computed"])((function(){return e.state.lang})),r=function(e,n){if(n in e&&"zh"===t.value){var a="".concat(n.toString(),"_zh");return a in e?e[a]:e[n]}return e[n]||""};return{langList:n,currentLang:t,i18nContent:r}}},f5fa:function(e,n,t){},f727:function(e,n,t){"use strict";t.d(n,"b",(function(){return o})),t.d(n,"a",(function(){return r}));var a=t("e423"),o=function(e){return a["a"].post("/login",e)},r=function(e){return a["a"].post("/password",e)}}});