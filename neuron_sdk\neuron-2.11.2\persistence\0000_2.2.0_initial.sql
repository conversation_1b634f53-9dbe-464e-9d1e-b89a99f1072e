/**
 * NEURON IIoT System for Industry 4.0
 * Copyright (C) 2020-2022 EMQ Technologies Co., Ltd All rights reserved.
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 3 of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with this program; if not, write to the Free Software Foundation,
 * Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.
 **/
--- Add plugins table ---
CREATE TABLE IF NOT EXISTS
  plugins (library TEXT PRIMARY KEY check(length(library) <= 32));

--- Add adapters table ---
CREATE TABLE IF NOT EXISTS
  nodes (
    name TEXT PRIMARY KEY check(length(name) <= 32),
    type integer(1) NOT NULL check(type IN (1, 2)),
    state integer(1) NOT NULL check(state BETWEEN 1 AND 4),
    plugin_name TEXT NOT NULL check(length(plugin_name) <= 32),
    mid INTEGER NULL UNIQUE,
    pid INTEGER NOT NULL check(pid >= 0),
    link INTEGER NOT NULL check(link >= 0),
    modeType INTEGER NOT NULL check(modeType >= 0),
    eui TEXT NOT NULL,
    deviceCode INTEGER NOT NULL,
    version TEXT NOT NULL,
    subType INTEGER NOT NULL,
    createTime TIMESTAMP DEFAULT (datetime(CURRENT_TIMESTAMP,'localtime')),
    updateTime TIMESTAMP,
    userData TEXT
  );

--- 创建触发器：每次更新时自动刷新 updated_at
CREATE TRIGGER update_nodes_timestamp
BEFORE UPDATE ON nodes
FOR EACH ROW
WHEN (
    NEW.name IS NOT OLD.name OR
    NEW.type IS NOT OLD.type OR
    NEW.plugin_name IS NOT OLD.plugin_name OR
    NEW.mid IS NOT OLD.mid OR
    NEW.pid IS NOT OLD.pid OR
    NEW.eui IS NOT OLD.eui OR
    NEW.link IS NOT OLD.link OR
    NEW.modeType IS NOT OLD.modeType OR
    NEW.deviceCode IS NOT OLD.deviceCode OR
    NEW.version IS NOT OLD.version OR
    NEW.subType IS NOT OLD.subType OR
    NEW.userData IS NOT OLD.userData 
)
BEGIN    
    UPDATE nodes SET updateTime = datetime(CURRENT_TIMESTAMP,'localtime') 
    WHERE rowid = NEW.rowid;
END;

-- 使用触发器实现mid自增
CREATE TRIGGER auto_increment_mid 
AFTER INSERT ON nodes
BEGIN
    UPDATE nodes 
    SET mid = (SELECT IFNULL(MAX(mid), 0) + 1 FROM nodes)
    WHERE rowid = NEW.rowid;
END;

--- Add settings table ---
CREATE TABLE IF NOT EXISTS
  settings (
    node_name TEXT NOT NULL,
    setting TEXT NOT NULL,
    UNIQUE (node_name),
    FOREIGN KEY (node_name) REFERENCES nodes (name) ON UPDATE CASCADE ON DELETE CASCADE
  );

--- Add groups table ---
CREATE TABLE IF NOT EXISTS
  groups (
    driver_name TEXT NOT NULL,
    name TEXT NOT NULL check(length(name) <= 64),
    interval INTEGER NOT NULL check(interval >= 100),
    addr  INTEGER NULL,
    type  INTEGER NOT NULL,
    description TEXT NULL  check(length(description) <= 200),
    context TEXT NULL check(length(context) <= 512),
    createTime TIMESTAMP DEFAULT (datetime(CURRENT_TIMESTAMP,'localtime')),
    UNIQUE (driver_name, name),
    FOREIGN KEY (driver_name) REFERENCES nodes (name) ON UPDATE CASCADE ON DELETE CASCADE
  );

--- Add tags table ---
CREATE TABLE IF NOT EXISTS
  tags (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    driver_name TEXT NOT NULL,
    group_name TEXT NOT NULL,
    name TEXT NULL check(length(name) <= 32),
    description TEXT NULL check(length(description) <= 256),
    unit  TEXT NOT NULL,
    rwFlag INTEGER NOT NULL,    
    type INTEGER NOT NULL,
    atMid INTEGER NOT NULL,
    addr TEXT NOT NULL,
    linkType INTEGER NOT NULL, 
    value TEXT DEFAULT NULL,
    params TEXT NULL,
    createTime TIMESTAMP DEFAULT (datetime(CURRENT_TIMESTAMP,'localtime')),
    UNIQUE (driver_name, group_name, name),
    FOREIGN KEY (driver_name, group_name) REFERENCES groups (driver_name, name) ON UPDATE CASCADE ON DELETE CASCADE
  );

--- Add subscriptions table ---
CREATE TABLE IF NOT EXISTS
  subscriptions (
    app_name TEXT NOT NULL,
    driver_name TEXT NOT NULL,
    group_name TEXT NOT NULL,
    CHECK (app_name != driver_name),
    UNIQUE (app_name, driver_name, group_name),
    FOREIGN KEY (app_name) REFERENCES nodes (name) ON UPDATE CASCADE ON DELETE CASCADE,
    FOREIGN KEY (driver_name, group_name) REFERENCES groups (driver_name, name) ON UPDATE CASCADE ON DELETE CASCADE
  );

--- Add users table ---
CREATE TABLE
  IF NOT EXISTS users (
    name TEXT PRIMARY KEY check(length(name) <= 32),
    password TEXT NOT NULL
  );

--- Default admin user ---
INSERT INTO
  users (name, password)
VALUES
  (
    "admin",
    "$5$PwFeXpBBIBZuZdZl$fP8fFPWCLoaWcnVXVSR.3Xi8TEqCvX92gjhowNNn6S4"
  );
