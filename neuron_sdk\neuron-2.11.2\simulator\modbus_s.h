/**
 * NEURON IIoT System for Industry 4.0
 * Copyright (C) 2020-2022 EMQ Technologies Co., Ltd All rights reserved.
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 3 of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with this program; if not, write to the Free Software Foundation,
 * Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.
 **/
#ifndef SIMULATOR_MODBUS_S_H
#define SIMULATOR_MODBUS_S_H

#include <stdbool.h>
#include <stdint.h>
#include <stdio.h>

void modbus_s_init();

void modbus_s_simulate_error(bool b);

ssize_t modbus_s_rtu_req(uint8_t *req, uint16_t req_len, uint8_t *res,
                         int res_mlen, int *res_len);
ssize_t modbus_s_tcp_req(uint8_t *req, uint16_t req_len, uint8_t *res,
                         int res_mlen, int *res_len);

#endif