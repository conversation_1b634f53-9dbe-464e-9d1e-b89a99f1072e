#include "ac_data_parser.h"

// 期望的数据包长度
#define AC_DATA_PACKET_LEN  23

/**
 * 解析空调设备上报的十六进制数据
 * @param data: 输入的十六进制数据
 * @param data_len: 数据长度
 * @param ac_data: 输出的解析结果
 * @return: 0-成功, -1-失败
 */
int parse_ac_data(const uint8_t *data, uint16_t data_len, ac_data_t *ac_data)
{
    if (data == NULL || ac_data == NULL) {
        printf("Error: Invalid parameters\n");
        return -1;
    }
    
    if (data_len < AC_DATA_PACKET_LEN) {
        printf("Error: Data length too short, expected %d bytes, got %d bytes\n", 
               AC_DATA_PACKET_LEN, data_len);
        return -1;
    }
    
    // 清零结构体
    memset(ac_data, 0, sizeof(ac_data_t));
    
    int offset = 0;
    
    // 解析温度 (2B) - 大端序
    ac_data->temperature = (data[offset] << 8) | data[offset + 1];
    offset += 2;
    
    // 解析设定温度 (2B) - 大端序
    ac_data->set_temperature = (data[offset] << 8) | data[offset + 1];
    offset += 2;
    
    // 解析模式 (1B)
    ac_data->mode = data[offset++];
    
    // 解析星期 (1B)
    ac_data->week_day = data[offset++];
    
    // 解析周 (1B)
    ac_data->week = data[offset++];
    
    // 解析时 (1B)
    ac_data->hour = data[offset++];
    
    // 解析分 (1B)
    ac_data->minute = data[offset++];
    
    // 解析开关 (1B)
    ac_data->power_switch = data[offset++];
    
    // 解析高风 (1B)
    ac_data->high_wind = data[offset++];
    
    // 解析中风 (1B)
    ac_data->medium_wind = data[offset++];
    
    // 解析低风 (1B)
    ac_data->low_wind = data[offset++];
    
    // 解析冷阀 (1B)
    ac_data->cold_valve = data[offset++];
    
    // 解析热阀 (1B)
    ac_data->hot_valve = data[offset++];
    
    // 解析湿度 (2B) - 大端序
    ac_data->humidity = (data[offset] << 8) | data[offset + 1];
    offset += 2;
    
    // 解析风速自动标志 (1B)
    ac_data->wind_speed_auto_flag = data[offset++];
    
    // 解析GID (1B)
    ac_data->gid = data[offset++];
    
    // 解析OID (1B)
    ac_data->oid = data[offset++];
    
    // 解析IID (1B)
    ac_data->iid = data[offset++];
    
    // 解析LOCK (1B)
    ac_data->lock = data[offset++];
    
    // 解析优先级字段
    ac_data->onoff_pri = data[offset++];      // 21-onoff pri.
    ac_data->set_temp_pri = data[offset++];   // 22-set temp pri.
    ac_data->set_mode_pri = data[offset++];   // 23-set mode pri.
    ac_data->set_wspeed_pri = data[offset++]; // 24-set wspeed pri.
    
    return 0;
}

/**
 * 获取空调模式字符串
 */
const char* get_ac_mode_string(uint8_t mode)
{
    switch (mode) {
        case AC_MODE_AUTO: return "Auto";
        case AC_MODE_COOL: return "Cool";
        case AC_MODE_HEAT: return "Heat";
        case AC_MODE_FAN:  return "Fan";
        case AC_MODE_DRY:  return "Dry";
        default:           return "Unknown";
    }
}

/**
 * 获取星期字符串
 */
const char* get_weekday_string(uint8_t weekday)
{
    const char* weekdays[] = {"Sunday", "Monday", "Tuesday", "Wednesday", 
                              "Thursday", "Friday", "Saturday"};
    if (weekday >= 1 && weekday <= 7) {
        return weekdays[weekday - 1];
    }
    return "Invalid";
}

/**
 * 打印解析后的空调数据
 */
void print_ac_data(const ac_data_t *ac_data)
{
    if (ac_data == NULL) {
        printf("Error: ac_data is NULL\n");
        return;
    }
    
    printf("=== Air Conditioner Data ===\n");
    printf("Temperature:        %d.%d°C\n", ac_data->temperature / 10, ac_data->temperature % 10);
    printf("Set Temperature:    %d.%d°C\n", ac_data->set_temperature / 10, ac_data->set_temperature % 10);
    printf("Mode:               %s (%d)\n", get_ac_mode_string(ac_data->mode), ac_data->mode);
    printf("Week Day:           %s (%d)\n", get_weekday_string(ac_data->week_day), ac_data->week_day);
    printf("Week:               %d\n", ac_data->week);
    printf("Time:               %02d:%02d\n", ac_data->hour, ac_data->minute);
    printf("Power Switch:       %s (%d)\n", ac_data->power_switch ? "ON" : "OFF", ac_data->power_switch);
    printf("High Wind:          %s (%d)\n", ac_data->high_wind ? "ON" : "OFF", ac_data->high_wind);
    printf("Medium Wind:        %s (%d)\n", ac_data->medium_wind ? "ON" : "OFF", ac_data->medium_wind);
    printf("Low Wind:           %s (%d)\n", ac_data->low_wind ? "ON" : "OFF", ac_data->low_wind);
    printf("Cold Valve:         %s (%d)\n", ac_data->cold_valve ? "OPEN" : "CLOSE", ac_data->cold_valve);
    printf("Hot Valve:          %s (%d)\n", ac_data->hot_valve ? "OPEN" : "CLOSE", ac_data->hot_valve);
    printf("Humidity:           %d.%d%%\n", ac_data->humidity / 10, ac_data->humidity % 10);
    printf("Wind Speed Auto:    %s (%d)\n", ac_data->wind_speed_auto_flag ? "AUTO" : "MANUAL", ac_data->wind_speed_auto_flag);
    printf("GID:                0x%02X (%d)\n", ac_data->gid, ac_data->gid);
    printf("OID:                0x%02X (%d)\n", ac_data->oid, ac_data->oid);
    printf("IID:                0x%02X (%d)\n", ac_data->iid, ac_data->iid);
    printf("LOCK:               %s (0x%02X)\n", ac_data->lock ? "LOCKED" : "UNLOCKED", ac_data->lock);
    printf("OnOff Priority:     0x%02X (%d)\n", ac_data->onoff_pri, ac_data->onoff_pri);
    printf("Set Temp Priority:  0x%02X (%d)\n", ac_data->set_temp_pri, ac_data->set_temp_pri);
    printf("Set Mode Priority:  0x%02X (%d)\n", ac_data->set_mode_pri, ac_data->set_mode_pri);
    printf("Set WSpeed Priority: 0x%02X (%d)\n", ac_data->set_wspeed_pri, ac_data->set_wspeed_pri);
    printf("=============================\n");
}
