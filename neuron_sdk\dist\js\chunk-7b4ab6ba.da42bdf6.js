(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7b4ab6ba"],{"15fd":function(e,t,r){"use strict";r.d(t,"a",(function(){return c}));r("a4d3"),r("b64b");function n(e,t){if(null==e)return{};var r,n,c={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(c[r]=e[r]);return c}function c(e,t){if(null==e)return{};var r,c,a=n(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(c=0;c<o.length;c++)r=o[c],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}},2391:function(e,t,r){"use strict";var n=r("1da1"),c=(r("96cf"),r("d3b7"),r("47e2")),a=r("d472"),o=r("a007");t["a"]=function(){var e=Object(c["b"])(),t=e.t,r=function(e){e},i=function(){var e=Object(n["a"])(regeneratorRuntime.mark((function e(r){var n,c=arguments;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=c.length>1&&void 0!==c[1]?c[1]:o["b"].Text,e.abrupt("return",new Promise((function(e,c){var i=new FileReader;n===o["b"].Text?i.readAsText(r,"UTF-8"):i.readAsArrayBuffer(r),i.onload=function(r){var n,o=null===r||void 0===r||null===(n=r.target)||void 0===n?void 0:n.result;o?e(o):(""===o||void 0===o)&&(a["EmqxMessage"].error(t("common.readFileError")),c())},i.onerror=function(e){a["EmqxMessage"].error(t("common.readFileError")),c()}})));case 2:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();return{setMaxSize:r,readFile:i}}},9794:function(e,t,r){"use strict";r("a053")},a053:function(e,t,r){},a91e:function(e,t,r){e.exports=r.p+"img/license.5a0dd902.png"},acc1:function(e,t,r){"use strict";r.r(t);var n=r("5530"),c=r("15fd"),a=r("1da1"),o=(r("96cf"),r("a15b"),r("b680"),r("a9e3"),r("ac1f"),r("1276"),r("b0c0"),r("d3b7"),r("7a23")),i=r("3fd4"),l=r("6a4f"),u=r("2391"),s=r("d472"),b=r("47e2"),d=r("eb58"),j=["error"],O={class:"card-hd-with-btn"},f={class:"card-title"},p={class:"license"},m={class:"progress-text"},v=["src"],g={class:"method-text"},x=Object(o["createTextVNode"])(" 1. "),h=["href"],w=Object(o["createTextVNode"])(" 2. "),y=["href"],N=Object(o["defineComponent"])({setup:function(e){Object(o["useCssVars"])((function(e){return{"12c04e6d":Object(o["unref"])(S)}}));var t=Object(b["b"])(),N=t.t,V=t.locale,_=Object(o["ref"])(!1),C=Object(o["ref"])(void 0),k=Object(d["a"])(),E=k.currentLang,D=Object(o["computed"])((function(){return!!C.value})),S=Object(o["computed"])((function(){return"zh"===E.value?"120px":"140px"})),T=Object(o["computed"])((function(){var e=C.value||{},t=e.enabled_plugins;return t&&Array.isArray(t)?t.join(", "):""})),q=Object(o["computed"])((function(){return"https://www.emqx.com/".concat(V.value,"/apply-licenses/neuron")})),$=Object(o["computed"])((function(){return"https://www.emqx.com/".concat(V.value,"/contact?product=neuron")})),F=function(){var e=Object(a["a"])(regeneratorRuntime.mark((function e(){var t,r,a,o,i,u;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,_.value=!0,e.next=4,Object(l["c"])();case 4:t=e.sent,r=t.data,r.error,a=Object(c["a"])(r,j),o=a.used_tags,i=a.max_node_tags,u=100*parseFloat((Number(o)/Number(i)).toFixed(2)),C.value=Object(n["a"])(Object(n["a"])({},a),{},{tagsUsage:u}),e.next=15;break;case 12:e.prev=12,e.t0=e["catch"](0),console.error(e.t0);case 15:return e.prev=15,_.value=!1,e.finish(15);case 18:case"end":return e.stop()}}),e,null,[[0,12,15,18]])})));return function(){return e.apply(this,arguments)}}(),B=Object(u["a"])(),P=B.readFile,R=function(){var e=Object(a["a"])(regeneratorRuntime.mark((function e(t){var r,n,c;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(r=t.name.split("."),n=r[r.length-1],"lic"===n){e.next=5;break}return s["EmqxMessage"].warning(N("admin.uploadFileTypeError")),e.abrupt("return",!1);case 5:return e.prev=5,e.next=8,P(t);case 8:return c=e.sent,e.next=11,Object(l["e"])(c);case 11:s["EmqxMessage"].success(N("admin.uploadSuccessful")),F(),e.next=18;break;case 15:e.prev=15,e.t0=e["catch"](5),console.error(e.t0);case 18:return e.abrupt("return",Promise.reject());case 19:case"end":return e.stop()}}),e,null,[[5,15]])})));return function(t){return e.apply(this,arguments)}}();return F(),function(e,t){var n=Object(o["resolveComponent"])("emqx-descriptions-item"),c=Object(o["resolveComponent"])("emqx-descriptions"),a=Object(o["resolveComponent"])("i18n-t"),l=Object(o["resolveComponent"])("emqx-button"),u=Object(o["resolveComponent"])("emqx-upload"),s=Object(o["resolveComponent"])("emqx-card"),b=Object(o["resolveDirective"])("emqx-loading");return Object(o["withDirectives"])((Object(o["openBlock"])(),Object(o["createBlock"])(s,null,{default:Object(o["withCtx"])((function(){return[Object(o["createElementVNode"])("div",O,[Object(o["createElementVNode"])("h3",f,Object(o["toDisplayString"])(e.$t("admin.license")),1)]),Object(o["createElementVNode"])("div",p,[Object(o["unref"])(D)?(Object(o["openBlock"])(),Object(o["createBlock"])(c,{key:0,column:1},{default:Object(o["withCtx"])((function(){return[Object(o["createVNode"])(n,{label:e.$t("admin.object")},{default:Object(o["withCtx"])((function(){return[Object(o["createTextVNode"])(Object(o["toDisplayString"])(C.value.object),1)]})),_:1},8,["label"]),Object(o["createVNode"])(n,{label:e.$t("admin.tagUsage")},{default:Object(o["withCtx"])((function(){return[Object(o["createVNode"])(Object(o["unref"])(i["ElProgress"]),{"stroke-width":14,percentage:C.value.tagsUsage,status:"success",class:"progress-bar"},{default:Object(o["withCtx"])((function(){return[Object(o["createElementVNode"])("span",m,Object(o["toDisplayString"])(C.value.used_tags)+" / "+Object(o["toDisplayString"])(C.value.max_node_tags),1)]})),_:1},8,["percentage"])]})),_:1},8,["label"]),Object(o["createVNode"])(n,{label:e.$t("admin.emailAddress")},{default:Object(o["withCtx"])((function(){return[Object(o["createTextVNode"])(Object(o["toDisplayString"])(C.value.email_address),1)]})),_:1},8,["label"]),Object(o["createVNode"])(n,{label:e.$t("admin.effectiveDate")},{default:Object(o["withCtx"])((function(){return[Object(o["createTextVNode"])(Object(o["toDisplayString"])(C.value.valid_since),1)]})),_:1},8,["label"]),Object(o["createVNode"])(n,{label:e.$t("admin.expireDate")},{default:Object(o["withCtx"])((function(){return[Object(o["createTextVNode"])(Object(o["toDisplayString"])(C.value.valid_until),1)]})),_:1},8,["label"]),Object(o["createVNode"])(n,{label:e.$t("admin.enabledPlugins")},{default:Object(o["withCtx"])((function(){return[Object(o["createTextVNode"])(Object(o["toDisplayString"])(Object(o["unref"])(T)),1)]})),_:1},8,["label"]),Object(o["createVNode"])(n,{label:e.$t("admin.hardwareToken")},{default:Object(o["withCtx"])((function(){return[Object(o["createTextVNode"])(Object(o["toDisplayString"])(C.value.hardware_token||"-"),1)]})),_:1},8,["label"])]})),_:1})):Object(o["createCommentVNode"])("",!0),Object(o["createElementVNode"])("div",{class:Object(o["normalizeClass"])(["msg",{"align-center":!Object(o["unref"])(D)}])},[Object(o["unref"])(D)?Object(o["createCommentVNode"])("",!0):(Object(o["openBlock"])(),Object(o["createElementBlock"])("img",{key:0,src:r("a91e"),width:"400"},null,8,v)),Object(o["createElementVNode"])("div",g,[Object(o["createElementVNode"])("p",null,Object(o["toDisplayString"])(Object(o["unref"])(D)?e.$t("admin.howToGetTheCertificate"):e.$t("admin.licensePlaceholder")),1),Object(o["createElementVNode"])("p",null,[x,Object(o["createVNode"])(a,{class:"payload-desc",keypath:"admin.getFreeLicense",tag:"span"},{default:Object(o["withCtx"])((function(){return[Object(o["createElementVNode"])("a",{href:Object(o["unref"])(q),target:"_blank"},Object(o["toDisplayString"])(e.$t("admin.apply")),9,h)]})),_:1})]),Object(o["createElementVNode"])("p",null,[w,Object(o["createVNode"])(a,{class:"payload-desc",keypath:"admin.buyLicense",tag:"span"},{default:Object(o["withCtx"])((function(){return[Object(o["createElementVNode"])("a",{href:Object(o["unref"])($),target:"_blank"},Object(o["toDisplayString"])(e.$t("admin.contactUs")),9,y)]})),_:1})])]),Object(o["createVNode"])(u,{action:"",class:"file-upload","show-file-list":!1,"before-upload":R},{default:Object(o["withCtx"])((function(){return[Object(o["createVNode"])(l,{size:"small",type:"primary",class:"btn-upload"},{default:Object(o["withCtx"])((function(){return[Object(o["createTextVNode"])(Object(o["toDisplayString"])(Object(o["unref"])(D)?Object(o["unref"])(N)("common.reUpload"):Object(o["unref"])(N)("common.upload")),1)]})),_:1})]})),_:1})],2)])]})),_:1},512)),[[b,_.value]])}}}),V=(r("9794"),r("6b0d")),_=r.n(V);const C=_()(N,[["__scopeId","data-v-bab94a02"]]);t["default"]=C}}]);