#include "otel/otel_manager.h"
#include "utils/asprintf.h"
#include "utils/time.h"
#include <stdlib.h>
#include "neuron.h"
#include "neuron/msg.h"
#include "errcodes.h"
#include "util.h"

#include "lora_protocol.h"
#include "acme_gw.h"
#include "acme_lora_driver.h"


static int inline hex2str_printf(char *data,int nByte)
{
    char hexs[600]={0};
    
    Hex2Str(data, hexs, nByte);
    hexs[2*nByte] = '\0';
    printf ("Hex: %s\n", hexs); 
    return 0;
}

//同步系统时间到Lora 模块
void wave_mesh_broadcast_systime(neu_plugin_t *plugin)
{
    char tmp[30] = {0};
    char broad_eui[8] = {0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff};

    if ( 0 == system_popen("date +\"%Y-%m-%d %H:%M:%S\"", 'r', tmp, NULL) ){
        printf("BC system time:%s\n", tmp);   
        acme_lora_msg_send(plugin,LR_SYNS_SYS_TIME,SPT, broad_eui, (uint8_t *)tmp, strlen(tmp));
    }

}

/*
* 子设备删除
*/
int neu_device_leave(neu_plugin_t *plugin,const char *name)
{
    if(plugin == NULL || name == NULL) {
        nlog_debug("parameter error !!!");
        return -1;
    }
    int ret = 0;

    neu_device_manager_del(plugin->subDevice_manager,name);

    //TODO： 子设备被删除，设备离网是否需要发送 lora 消息给子模块
    //此处可添加发送lora离网消息....





    nlog_debug("sub dev:%s leave ok.",name);
    return ret;
}


/*
* 子设备注册到网关
*/
int neu_device_register(neu_plugin_t *plugin,neu_acme_subDev_reg_t *subdev)
{
    if(plugin == NULL || subdev == NULL) {
        nlog_debug("parameter error !!!");
        return -1;
    }
    int ret = 0;

    manager_dev_info_t *dev = (manager_dev_info_t *)calloc(1,sizeof(manager_dev_info_t));
    dev->name           = strdup(subdev->node);
    dev->mid            = subdev->mid;
    dev->link           = subdev->link;
    dev->online         = subdev->online; //0-offline   1-online
    dev->pair_flag      = 0;      //是否正在配对
    dev->deviceType     = subdev->modeType;     //设备类型
    dev->pid            = subdev->pid;
    dev->subType        = subdev->subType;
    memcpy(dev->eui, subdev->eui,EUI_ADR_LEN); 
    memcpy(dev->version, subdev->version,10); 

    ret = neu_device_manager_add(plugin->subDevice_manager,dev);

    manager_dev_info_free(dev);

    return ret;
}

/*
*  lora 设备控制命令下发
*/
int neu_device_action_send(neu_plugin_t *plugin, char * eui, uint8_t modeType, neu_acme_dev_action_t *action)
{
    int ret = 0;
    char res_eui[10] = {0};
    if(eui == NULL || action == NULL){
        nlog_debug("parameter error.");
        return -1;
    }

    //此处组空中控制包，进行网关 Lora 串口写入
    StringToBuff(eui, (uint8_t *)res_eui);
   
    if(strcmp(action->type,"AO") == 0){
        char buff[7] = {0};
        buff[0] = (uint8_t)action->chn;         // 1B 通道
        for(int i=0;i<4;i++){
            buff[1+i] = action->value[i]; 
        }
        buff[5] = 0;   
        buff[6] = (uint8_t)(action->trigger);           //控制源
        acme_lora_msg_send(plugin, modeType ,LR_RSP_AO_CTL, res_eui, buff, 7);
    }else if(strcmp(action->type,"BO") == 0){
        char buff[7] = {0};
        buff[0] = (uint8_t)action->chn;         // 1B 通道

        float fval;
        Byte_to_Float(&fval, action->value);
        int tmp = (int)fval;
        if(tmp){
            buff[1] = 0x01;
        }else{
            buff[1] = 0x00;
        }

        nlog_debug("dev:%s BO ctrl start. json value=%f || tmp=%d || buff value=%02x",eui,fval,tmp,buff[1]);
        
        buff[2] = (uint8_t)(action->trigger); 
        acme_lora_msg_send(plugin, modeType ,LR_RSP_DO_CTL, res_eui, buff, 3);

    }else if(strcmp(action->type, "CO") == 0){


    }else if(strcmp(action->type, "DO") == 0){


    }else{
        nlog_debug("dev:%s action type:%s not support.",eui, action->type);
        ret = 0;
    }

    return ret;
}

/*
*  设备业务层请求下发设备控制命令
* 参数: plugin -- 网关插件句柄   ctrl -- 设备业务层下发的请求控制动作集合（单控或者多控）
*/
int neu_device_ctrl_request(neu_plugin_t *plugin,neu_acme_dev_ctrl_t * ctrl)
{
    int ret = 0;
    manager_dev_info_t  *dev = NULL;
    if(plugin == NULL || ctrl == NULL){
        nlog_debug("parameter error. ");
        return -1;
    }

    nlog_debug("node:%s eui:%s request ctrl. action num=%d",ctrl->node,ctrl->eui,utarray_len(ctrl->actions));

    uint8_t modeType = neu_device_manager_get_modeType_by_name(plugin->subDevice_manager, ctrl->node);
    if(modeType < 0){
        nlog_debug("gw logic device manager not found dev:%s ", ctrl->node);
        return -1;
    }

    utarray_foreach(ctrl->actions, neu_acme_dev_action_t *, action)
    {
        int index = utarray_eltidx(ctrl->actions,action);
        nlog_debug("--> gw recv action[%d] ctrl type:%s chn:%d trigger:%d",index, action->type,action->chn,action->trigger);

         //TODO: 此处需要针对具体的控制类型，调用Lora 驱动程序 完成控制包的发射
        neu_device_action_send(plugin, ctrl->eui, modeType, action);
        neu_msleep(50);
    }

    utarray_free(ctrl->actions);
    
    return ret;
}


/*
* 网关通知 子设备node适配器，设备配对 入网成功消息
* 参数: plugin - 网关插件
*       name - 子设备node 名称
*       mid - 子设备 mid 标识
*/
static int neu_device_notify_bind(neu_plugin_t *plugin,char * name, int mid,char *eui)
{
    int ret = 0;
    if(plugin == NULL || name == NULL || eui == NULL){
        nlog_debug("parameter error !!!");
        return -1;
    }

    neu_reqresp_head_t header = {
        .type = NEU_REQ_ACME_NOTIFY_DEV_JOIN,
        .ctx  = plugin,
    };

    neu_acme_notify_dev_join_t cmd = {
        .mid  = mid,
    };

    strcpy(cmd.node, name);
    strcpy(cmd.eui, eui);

    nlog_debug("send dev:%s notify info.",name);

    if (0 != neu_plugin_op(plugin, header, &cmd)) {
        plog_error(plugin, "neu_plugin_op(NEU_REQ_ACME_NOTIFY_DEV_JOIN) fail");
        return -1;
    }

    return ret;
}

/*
* 通知设备设备已绑定
*/
static int lora_dev_bind(neu_plugin_t *plugin,manager_dev_info_t  *dev)
{
    if(plugin == NULL || dev == NULL){
        nlog_debug("parameter error !!!");
        return -1;
    }
    int ret = 0;

    nlog_debug("bind ok. update dev:%s eui:%s ", dev->name,dev->eui);
    //1、更新子设备管理器中设备节点
    neu_device_manager_update(plugin->subDevice_manager, dev->name, dev);


    //2、通知子设备适配器，设备绑定请求(适配器将更新 eui 地址 和 绑定参数)
    //....
    ret = neu_device_notify_bind(plugin,dev->name,dev->mid,dev->eui);

    return 0;
}

/*
* 处理Lora 上报的配对组网请求
*/
int loraRcvCfgAckHandle(neu_plugin_t *plugin,lpwan_wavemesh_t *pack)
{
    int ret = 0;
    manager_dev_info_t  *dev = NULL;
    if(pack == NULL || plugin == NULL) return -1;

#if 1
    if(plugin->pair_mid > 0){   //当前已有子模块请求配对
        int deviceType = neu_device_manager_deviceType_by_mid(plugin->subDevice_manager, plugin->pair_mid);
        if(deviceType < 0){
            nlog_debug("query mid=%d device type error !!!",plugin->pair_mid);
            return -1;
        }
        if(pack->DeviceType == deviceType){     //正确匹配上设备类型
            //TODO: 设备配对成功
            // 设备 eui 刷新,发送配对成功消息到 node 适配器(完成数据库eui 更新)
            dev = neu_device_manager_deviceInfo_by_mid(plugin->subDevice_manager,plugin->pair_mid);
            if(dev == NULL){
                nlog_debug("lora device request deviceType:%d .not allow 1 !!!", pack->DeviceType);
                return -1;
            }
        }else{
            nlog_debug("web pair deviceType=%02x , lora device request deviceType:%02x .not allow !!!",deviceType, pack->DeviceType);
            return -1;
        }
    }else{  //web 没有请求配置，则从子设备管理器中查找匹配的设备
        //TODO: 从子设备管理器查找请求入网的设备类型，并且eui 是空，没有配对的设备节点，完成设备配对
        dev = neu_device_manager_pair_dev_by_deviceType(plugin->subDevice_manager,pack->DeviceType);
        if(dev == NULL){
            nlog_debug("lora device request deviceType:%d .not allow 2 !!!", pack->DeviceType);
            return -1;
        }
    }
 #else 
        //测试数据  -- 测试子设备跳过子设备管理器校验，直接入网
        dev = (manager_dev_info_t  *)calloc(1,sizeof(manager_dev_info_t));
        dev->name       = strdup("ACM_001");
        dev->mid        = 1;
        dev->netType    = 2;
        dev->online     = 1; //0-offline   1-online
        dev->pair_flag  = 0;      //是否正在配对
        dev->deviceType = pack->DeviceType;     //设备类型
#endif  
    
    //memcpy(dev->eui,pack->EuiAdr,EUI_ADR_LEN);
    Hex2Str(pack->EuiAdr,dev->eui,8);       //eui 字节序转字符串存储
    
    //1、Lora 应答子设备，发送工作频段、信道等参数
    //... 
    ret = wave_mesh_ack_join_request(plugin, pack->EuiAdr, pack->DeviceType);
    neu_msleep(1000);
    
    lora_dev_bind(plugin, dev);
    ret = wave_mesh_ack_join_request(plugin, pack->EuiAdr, pack->DeviceType);
    neu_msleep(50);
    
    plugin->pair_mode = 3;  //下一轮定时任务时，停止搜网

    manager_dev_info_free(dev);
    return ret;
}

/*
* 处理Lora 上报的 io 状态数据 
*/
int loraRecvIoStatusHandle(neu_plugin_t *plugin,lpwan_wavemesh_t *pack)
{
    int ret = 0;
    char  *name = NULL;
    char eui[20] = {0};
    if(pack == NULL || plugin == NULL) return -1;

    Hex2Str(pack->EuiAdr,eui,8); 
    name = neu_device_manager_get_name_by_eui(plugin->subDevice_manager, eui);
    if(name == NULL){
        nlog_debug("eui:%s dev not found.",eui);
        return -1;
    }

    nlog_debug("eui:%s dev:%s found.",eui,name);

    //发送数据给 设备层
    neu_reqresp_head_t header = {
        .type = NEU_REQ_ACME_DEV_DATA_POP,
        .ctx  = plugin,
    };

    neu_acme_dev_data_pop_t cmd = {
        .rsp    = pack->RSP,
        .len    = pack->DataLength,
        .data   = NULL,
    };

    cmd.data = calloc(1,pack->DataLength);
    if(cmd.data == NULL){
        nlog_debug("calloc error.");
        return -1;
    }
    memcpy(cmd.data, pack->SubData,pack->DataLength);

    strcpy(cmd.node, name);
    strcpy(cmd.eui, eui);

    nlog_debug("send dev:%s eui:%s transparent data info.",name,eui);

    if (0 != neu_plugin_op(plugin, header, &cmd)) {
        plog_error(plugin, "neu_plugin_op(NEU_REQ_ACME_DEV_DATA_POP) fail");
        // 发送失败时需要释放cmd.data内存
        if (cmd.data != NULL) {
            free(cmd.data);
            cmd.data = NULL;
        }
        return -1;
    }



    return ret;
}


/*
* Lora 协议解析
*/
int acme_lora_process_protocol_buf(neu_plugin_t *plugin,lpwan_wavemesh_t *pack)
{
    int ret = 0;
    if(plugin == NULL || pack == NULL) return -1;


    nlog_debug("Lora pack decode ^_^ --> head=%02x || DeviceType=%d || DataLen=%d || RSP=%02x ",pack->STX,pack->DeviceType,\
                                                                                                     pack->DataLength,pack->RSP);
    printf("eui= ");   
    hex2str_printf(pack->EuiAdr,8);
    
    printf("data= ");
    hex2str_printf(pack->SubData,pack->DataLength-1);
    for(int i=0;i<pack->DataLength-1;i++){
        printf("%02x ",pack->SubData[i]);
    }
    printf(" \r\npack decode end.\r\n");

    switch (pack->RSP)
    {
        case LR_RSP_CFG_CTL:{        //配对请求
            loraRcvCfgAckHandle(plugin,pack);
            break;
        }
        case LR_RSP_IOM_LP:{
            loraRecvIoStatusHandle(plugin,pack);
            break;
        }
        default:
            break;
    }





    return ret;
}   


