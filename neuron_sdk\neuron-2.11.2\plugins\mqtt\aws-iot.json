{"client-id": {"name": "Client ID", "name_zh": "客户端 ID", "type": "string", "attribute": "required", "valid": {"length": 255}}, "qos": {"name": "QoS Level", "name_zh": "QoS 等级", "description": "MQTT QoS level for message delivery", "description_zh": "MQTT 消息传输使用的服务质量等级", "type": "map", "attribute": "optional", "default": 0, "valid": {"map": [{"key": "QoS 0", "value": 0}, {"key": "QoS 1", "value": 1}]}}, "format": {"name": "Upload Format", "name_zh": "上报数据格式", "description": "JSON format of the data reported from Neuron to AWS IoT Core. In values-mode, data are split into `values` and `errors` sub objects. In tags-mode, tag data are put in a single array.", "description_zh": "Neuron 上报数据到 AWS IoT Core 的 JSON 格式。在 values-format 格式下，数据被分为 `values` 和 `errors` 两个子对象。在 tags-format 格式下，数据被放在一个数组中。", "attribute": "required", "type": "map", "default": 0, "valid": {"map": [{"key": "values-format", "value": 0}, {"key": "tags-format", "value": 1}]}}, "write-req-topic": {"name": "Write Request Topic", "name_zh": "写请求主题", "description": "Neuron subscribes to this MQTT topic to receive write request messages from AWS IoT Core.", "description_zh": "Neuron 向 AWS IoT Core 订阅的 MQTT 主题，用于接收点位写入请求。", "attribute": "required", "type": "string", "valid": {"length": 255}}, "write-resp-topic": {"name": "Write Response Topic", "name_zh": "写响应主题", "description": "Neuron publishes write response messages to AWS IoT Core through this topic.", "description_zh": "Neuron 通过该 MQTT 主题将点位写入响应发布到 AWS IoT Core。", "attribute": "required", "type": "string", "valid": {"length": 255}}, "offline-cache": {"name": "Offline Data Caching", "name_zh": "离线缓存", "description": "Offline caching switch. Cache MQTT messages when offline, and sync cached messages when back online.", "description_zh": "离线缓存开关。连接断开时缓存 MQTT 消息，连接重建时同步缓存的 MQTT 消息到服务器。", "attribute": "optional", "condition": {"field": "invisible", "value": true}, "type": "bool", "default": false, "valid": {}}, "cache-mem-size": {"name": "<PERSON><PERSON> (MB)", "name_zh": "缓存内存大小（MB）", "description": "Max in-memory cache size in megabytes when MQTT connection exception occurs. Should be smaller than cache disk size.", "description_zh": "当 MQTT 连接异常时，最大的内存缓存大小（单位：MB）。应该小于缓存磁盘大小。", "type": "int", "attribute": "required", "condition": {"field": "offline-cache", "value": true}, "valid": {"min": 1, "max": 1024}}, "cache-disk-size": {"name": "<PERSON><PERSON> (MB)", "name_zh": "缓存磁盘大小（MB）", "description": "Max in-disk cache size in megabytes when MQTT connection exception occurs. Should be larger than cache memory size. If nonzero, cache memory size should also be nonzero.", "description_zh": "当 MQTT 连接异常时，最大的磁盘缓存大小（单位：MB）。应该大于缓存内存大小。如果不为 0，缓存内存大小也应该不为 0。", "type": "int", "attribute": "required", "condition": {"field": "offline-cache", "value": true}, "valid": {"min": 1, "max": 10240}}, "cache-sync-interval": {"name": "<PERSON><PERSON> (MS)", "name_zh": "缓存消息重传间隔（MS）", "type": "int", "attribute": "required", "condition": {"field": "offline-cache", "value": true}, "default": 100, "valid": {"min": 10, "max": 120000}}, "host": {"name": "Device data endponit", "name_zh": "设备数据终端节点", "description": "AWS IoT device data endpont", "description_zh": "AWS IoT 设备数据终端节点", "attribute": "required", "type": "string", "valid": {"length": 255}}, "port": {"name": "MQTT Port", "name_zh": "MQTT 端口", "attribute": "required", "condition": {"field": "invisible", "value": true}, "type": "int", "default": 8883, "valid": {"min": 1024, "max": 65535}}, "username": {"name": "Username", "name_zh": "用户名", "attribute": "optional", "condition": {"field": "invisible", "value": true}, "type": "string", "default": "", "valid": {"length": 255}}, "password": {"name": "Password", "name_zh": "密码", "attribute": "optional", "condition": {"field": "invisible", "value": true}, "type": "string", "default": "", "valid": {"length": 255}}, "ssl": {"name": "SSL", "name_zh": "SSL", "description": "Enable SSL connection", "description_zh": "是否启用 SSL 连接", "attribute": "optional", "condition": {"field": "invisible", "value": true}, "type": "bool", "default": true, "valid": {}}, "ca": {"name": "Root CA Certificate", "name_zh": "根 CA 证书", "description": "AWS IoT data endpoint root CA certificate", "description_zh": "AWS IoT 设备数据终端节点根 CA 证书", "attribute": "optional", "type": "file", "condition": {"field": "ssl", "value": true}, "valid": {"length": 81960}}, "cert": {"name": "Device Certificate", "name_zh": "设备证书", "description": "Device Certificate corresponding to a `thing` in the AWS IoT console", "description_zh": "在 AWS IoT 控制台中创建事物时创建的设备证书", "attribute": "optional", "type": "file", "condition": {"field": "ssl", "value": true}, "valid": {"length": 81960}}, "key": {"name": "Private Key", "name_zh": "私有密钥", "description": "Private Key corresponding to a `thing` in the AWS IoT console", "description_zh": "在 AWS IoT 控制台中创建事物时创建的私有密钥", "attribute": "optional", "type": "file", "condition": {"field": "ssl", "value": true}, "valid": {"length": 81960}}}