cmake_minimum_required(VERSION 3.12)

project(plugin-acme-lora)
enable_language(C)
set(CMAKE_C_STANDARD 99)
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -fno-common")
find_package(Threads)


set(LIBRARY_OUTPUT_PATH "${CMAKE_BINARY_DIR}/plugins")
#set(SDK_PATH "/opt/externs/libs/arm-linux-gnueabihf")
set(SDK_PATH "${CMAKE_SOURCE_DIR}/../../../neuron_sdk/neuron-2.11.2")

file(COPY ${CMAKE_SOURCE_DIR}/acme-lora.json DESTINATION ${CMAKE_BINARY_DIR}/plugins/schema/)

# 递归匹配所有子目录的.c文件
file(GLOB_RECURSE C_SOURCES 
    CONFIGURE_DEPENDS
	"${CMAKE_SOURCE_DIR}/../comman/*.c"
    "business_logic/*.c"
	"business_logic/product/*.c"
)

add_library(${PROJECT_NAME} SHARED
  acme_lora.c
  ${C_SOURCES}
)

target_include_directories(${PROJECT_NAME} PRIVATE 
  ${SDK_PATH}/include/neuron)
include_directories(${SDK_PATH}/include)
include_directories(${CMAKE_SOURCE_DIR})
include_directories(${CMAKE_SOURCE_DIR}/../comman)
include_directories(${CMAKE_SOURCE_DIR}/business_logic)
include_directories(${CMAKE_SOURCE_DIR}/business_logic/product)


link_directories(/opt/externs/libs/arm-linux-gnueabihf/lib)

#target_link_libraries(${PROJECT_NAME} /opt/externs/libs/arm-linux-gnueabihf/lib/libneuron-base.so)
#target_link_libraries(${PROJECT_NAME} /home/<USER>/work/SPT/acme_gw_project/neuron_sdk/neuron-2.11.2/build/libneuron-base.so)
target_link_libraries(${PROJECT_NAME} ${SDK_PATH}/build/libneuron-base.so)
target_link_libraries(${PROJECT_NAME} ${CMAKE_THREAD_LIBS_INIT})

