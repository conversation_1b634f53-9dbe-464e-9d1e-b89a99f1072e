{{- if .Values.tls.enabled }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "neuron.fullname" . }}-certs
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "neuron.labels" . | nindent 4 }}
type: Opaque
data:
  {{- if and .Values.tls.publicKey .Values.tls.privateKey }}
  neuron.pem: {{ required "A valid .Values.tls.publicKey entry required!" .Values.tls.publicKey | b64enc | quote }}
  neuron.key: {{ required "A valid .Values.tls.privateKey entry required!" .Values.tls.privateKey | b64enc | quote }}
  {{- end }}
{{- end }}