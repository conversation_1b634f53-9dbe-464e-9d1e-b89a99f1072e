cmake_minimum_required(VERSION 3.12)

project(plugin-acme-mqtt)
enable_language(C)
set(CMAKE_C_STANDARD 99)
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -fno-common")
find_package(Threads)


set(LIBRARY_OUTPUT_PATH "${CMAKE_BINARY_DIR}/plugins")
set(SDK_PATH "/opt/externs/libs/arm-linux-gnueabihf")

file(COPY ${CMAKE_SOURCE_DIR}/acme-mqtt.json DESTINATION ${CMAKE_BINARY_DIR}/plugins/schema/)

# 递归匹配所有子目录的.c文件
file(GLOB_RECURSE C_SOURCES 
    CONFIGURE_DEPENDS
    "chttp/*.c"
	"connect/*.c"
)

add_library(${PROJECT_NAME} SHARED
  mqtt_config.c
  mqtt_handle.c
  acme_mqtt_plugin.c
  mqtt_plugin_intf.c
  schema.c
  #connect/connect_server.c
  #connect/rsa.c
  ${C_SOURCES}
)

target_include_directories(${PROJECT_NAME} PRIVATE 
  ${SDK_PATH}/include/neuron)
include_directories(${CMAKE_SOURCE_DIR}/connect)
include_directories(${CMAKE_SOURCE_DIR}/chttp)
include_directories(${CMAKE_SOURCE_DIR})



link_directories(/opt/externs/libs/arm-linux-gnueabihf/lib)

target_link_libraries(${PROJECT_NAME} /opt/externs/libs/arm-linux-gnueabihf/lib/libneuron-base.so)
target_link_libraries(${PROJECT_NAME} ${CMAKE_THREAD_LIBS_INIT})

