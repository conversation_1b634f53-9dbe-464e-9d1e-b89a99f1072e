# ACME_MQTT 物模型实现指南

## 📋 实现概述

基于 **ACME_MQTT物模型规范v1.0**，本文档提供具体的代码实现指南，包括物模型解析、协议映射、设备管理等核心功能的实现方案。

## 🏗️ 核心组件设计

### 1. 物模型管理器 (Thing Model Manager)

```c
// thing_model_manager.h
#ifndef ACME_THING_MODEL_MANAGER_H
#define ACME_THING_MODEL_MANAGER_H

#include <stdint.h>
#include <stdbool.h>
#include "cjson/cJSON.h"

// 数据类型枚举
typedef enum {
    ACME_DATA_TYPE_INT32 = 0,
    ACME_DATA_TYPE_FLOAT,
    ACME_DATA_TYPE_DOUBLE,
    ACME_DATA_TYPE_BOOL,
    ACME_DATA_TYPE_STRING,
    ACME_DATA_TYPE_ENUM,
    ACME_DATA_TYPE_ARRAY,
    ACME_DATA_TYPE_STRUCT
} acme_data_type_e;

// 访问模式枚举
typedef enum {
    ACME_ACCESS_READ = 1,
    ACME_ACCESS_WRITE = 2,
    ACME_ACCESS_READ_WRITE = 3
} acme_access_mode_e;

// 事件类型枚举
typedef enum {
    ACME_EVENT_INFO = 0,
    ACME_EVENT_WARNING,
    ACME_EVENT_ERROR,
    ACME_EVENT_CRITICAL
} acme_event_type_e;

// 属性定义结构
typedef struct acme_property {
    char *identifier;           // 属性标识符
    char *name;                // 属性名称
    char *description;         // 属性描述
    acme_data_type_e data_type; // 数据类型
    acme_access_mode_e access_mode; // 访问模式
    bool required;             // 是否必需
    char *unit;               // 单位
    cJSON *default_value;     // 默认值
    cJSON *range;             // 取值范围
    cJSON *enum_values;       // 枚举值
    char **tags;              // 标签数组
    int tag_count;            // 标签数量
    
    struct acme_property *next; // 链表指针
} acme_property_t;

// 服务参数结构
typedef struct acme_service_param {
    char *name;
    acme_data_type_e data_type;
    bool required;
    char *description;
    cJSON *constraints;        // 参数约束
    
    struct acme_service_param *next;
} acme_service_param_t;

// 服务定义结构
typedef struct acme_service {
    char *identifier;          // 服务标识符
    char *name;               // 服务名称
    char *description;        // 服务描述
    bool is_async;            // 是否异步调用
    acme_service_param_t *input_params;  // 输入参数
    acme_service_param_t *output_params; // 输出参数
    
    struct acme_service *next;
} acme_service_t;

// 事件定义结构
typedef struct acme_event {
    char *identifier;          // 事件标识符
    char *name;               // 事件名称
    char *description;        // 事件描述
    acme_event_type_e event_type; // 事件类型
    acme_service_param_t *output_params; // 输出参数
    
    struct acme_event *next;
} acme_event_t;

// 物模型结构
typedef struct acme_thing_model {
    char *version;            // 版本号
    char *model_id;           // 模型ID
    char *model_name;         // 模型名称
    char *device_type;        // 设备类型
    char *manufacturer;       // 制造商
    char *description;        // 描述
    char *created_time;       // 创建时间
    char *updated_time;       // 更新时间
    
    acme_property_t *properties; // 属性链表
    acme_service_t *services;    // 服务链表
    acme_event_t *events;        // 事件链表
    cJSON *extensions;           // 扩展信息
} acme_thing_model_t;

// 物模型管理器
typedef struct acme_thing_model_manager {
    acme_thing_model_t **models; // 物模型数组
    int model_count;             // 模型数量
    int capacity;                // 容量
} acme_thing_model_manager_t;

// 函数声明
acme_thing_model_manager_t *acme_thing_model_manager_create(void);
void acme_thing_model_manager_destroy(acme_thing_model_manager_t *manager);

int acme_thing_model_manager_register(acme_thing_model_manager_t *manager, 
                                     const char *model_json);
acme_thing_model_t *acme_thing_model_manager_get(acme_thing_model_manager_t *manager,
                                                 const char *model_id);
int acme_thing_model_manager_validate(const char *model_json);

acme_thing_model_t *acme_thing_model_parse(const char *json_str);
void acme_thing_model_free(acme_thing_model_t *model);

acme_property_t *acme_thing_model_get_property(acme_thing_model_t *model,
                                              const char *identifier);
acme_service_t *acme_thing_model_get_service(acme_thing_model_t *model,
                                            const char *identifier);
acme_event_t *acme_thing_model_get_event(acme_thing_model_t *model,
                                        const char *identifier);

#endif // ACME_THING_MODEL_MANAGER_H
```

### 2. 协议映射器 (Protocol Mapper)

```c
// protocol_mapper.h
#ifndef ACME_PROTOCOL_MAPPER_H
#define ACME_PROTOCOL_MAPPER_H

#include "thing_model_manager.h"
#include "neuron.h"

// MQTT消息类型
typedef enum {
    ACME_MSG_READ_REQUEST = 0,
    ACME_MSG_READ_RESPONSE,
    ACME_MSG_WRITE_REQUEST,
    ACME_MSG_WRITE_RESPONSE,
    ACME_MSG_SERVICE_REQUEST,
    ACME_MSG_SERVICE_RESPONSE,
    ACME_MSG_EVENT_NOTIFY,
    ACME_MSG_EVENT_ACK,
    ACME_MSG_MODEL_DISCOVER_REQUEST,
    ACME_MSG_MODEL_DISCOVER_RESPONSE
} acme_message_type_e;

// 协议消息结构
typedef struct acme_protocol_message {
    char version[16];                 // 协议版本
    int64_t timestamp;                // 时间戳
    char request_id[64];              // 请求ID
    acme_message_type_e message_type; // 消息类型
    char source[32];                  // 消息来源
    char target[64];                  // 目标设备
    cJSON *data;                      // 数据内容
} acme_protocol_message_t;

// 属性值结构
typedef struct acme_property_value {
    char *identifier;        // 属性标识符
    cJSON *value;           // 属性值
    acme_data_type_e data_type; // 数据类型
    char *unit;             // 单位
    int64_t timestamp;      // 时间戳
    char *quality;          // 数据质量
} acme_property_value_t;

// 服务调用结构
typedef struct acme_service_call {
    char *identifier;       // 服务标识符
    cJSON *input_params;    // 输入参数
    cJSON *output_params;   // 输出参数
    bool is_async;          // 是否异步
    int result_code;        // 结果码
    char *error_message;    // 错误信息
} acme_service_call_t;

// 事件通知结构
typedef struct acme_event_notify {
    char *identifier;       // 事件标识符
    acme_event_type_e event_type; // 事件类型
    cJSON *output_params;   // 输出参数
    int64_t timestamp;      // 时间戳
    char *description;      // 事件描述
} acme_event_notify_t;

// 函数声明
int acme_protocol_message_parse(const char *json_str, acme_protocol_message_t *message);
char *acme_protocol_message_encode(const acme_protocol_message_t *message);
void acme_protocol_message_free(acme_protocol_message_t *message);

// 属性读取映射
int acme_map_read_request_to_neuron(const acme_protocol_message_t *mqtt_msg,
                                   acme_thing_model_t *model,
                                   neu_reqresp_head_t *neu_head,
                                   void **neu_data);

int acme_map_neuron_to_read_response(const neu_resp_read_group_t *neu_resp,
                                    acme_thing_model_t *model,
                                    acme_protocol_message_t *mqtt_msg);

// 属性写入映射
int acme_map_write_request_to_neuron(const acme_protocol_message_t *mqtt_msg,
                                    acme_thing_model_t *model,
                                    neu_reqresp_head_t *neu_head,
                                    void **neu_data);

int acme_map_neuron_to_write_response(const neu_resp_write_tags_t *neu_resp,
                                     acme_thing_model_t *model,
                                     acme_protocol_message_t *mqtt_msg);

// 服务调用映射
int acme_map_service_request_to_neuron(const acme_protocol_message_t *mqtt_msg,
                                      acme_thing_model_t *model,
                                      neu_reqresp_head_t *neu_head,
                                      void **neu_data);

// 事件通知映射
int acme_map_neuron_to_event_notify(const neu_resp_tag_value_t *neu_data,
                                   acme_thing_model_t *model,
                                   const char *event_identifier,
                                   acme_protocol_message_t *mqtt_msg);

// 物模型发现映射
int acme_map_model_discover_response(acme_thing_model_t *model,
                                   acme_protocol_message_t *mqtt_msg);

#endif // ACME_PROTOCOL_MAPPER_H
```

### 3. 主题管理器 (Topic Manager)

```c
// topic_manager.h
#ifndef ACME_TOPIC_MANAGER_H
#define ACME_TOPIC_MANAGER_H

#include "protocol_mapper.h"

// 主题模板定义
#define ACME_TOPIC_READ_REQUEST     "/acme/%s/%s/read/request"
#define ACME_TOPIC_READ_RESPONSE    "/acme/%s/%s/read/response"
#define ACME_TOPIC_WRITE_REQUEST    "/acme/%s/%s/write/request"
#define ACME_TOPIC_WRITE_RESPONSE   "/acme/%s/%s/write/response"
#define ACME_TOPIC_SERVICE_REQUEST  "/acme/%s/%s/service/request"
#define ACME_TOPIC_SERVICE_RESPONSE "/acme/%s/%s/service/response"
#define ACME_TOPIC_EVENT_NOTIFY     "/acme/%s/%s/event/notify"
#define ACME_TOPIC_EVENT_ACK        "/acme/%s/%s/event/ack"
#define ACME_TOPIC_MODEL_DISCOVER   "/acme/%s/%s/model/discover"
#define ACME_TOPIC_RESPONSE         "/acme/%s/%s/response/%s"

// 主题信息结构
typedef struct acme_topic_info {
    char *gateway_id;           // 网关ID
    char *device_id;            // 设备ID
    acme_message_type_e msg_type; // 消息类型
    char *request_id;           // 请求ID (可选)
} acme_topic_info_t;

// 函数声明
char *acme_topic_generate(acme_message_type_e msg_type,
                         const char *gateway_id,
                         const char *device_id,
                         const char *request_id);

int acme_topic_parse(const char *topic, acme_topic_info_t *info);
void acme_topic_info_free(acme_topic_info_t *info);

bool acme_topic_match_pattern(const char *topic, const char *pattern);
char **acme_topic_get_subscription_list(const char *gateway_id);
void acme_topic_subscription_list_free(char **topics);

#endif // ACME_TOPIC_MANAGER_H
```

## 🔧 核心实现代码

### 1. 物模型解析实现

```c
// thing_model_manager.c
#include "thing_model_manager.h"
#include <stdlib.h>
#include <string.h>

// 创建物模型管理器
acme_thing_model_manager_t *acme_thing_model_manager_create(void)
{
    acme_thing_model_manager_t *manager = calloc(1, sizeof(acme_thing_model_manager_t));
    if (!manager) return NULL;

    manager->capacity = 10;
    manager->models = calloc(manager->capacity, sizeof(acme_thing_model_t*));
    if (!manager->models) {
        free(manager);
        return NULL;
    }

    return manager;
}

// 解析物模型JSON
acme_thing_model_t *acme_thing_model_parse(const char *json_str)
{
    if (!json_str) return NULL;

    cJSON *root = cJSON_Parse(json_str);
    if (!root) return NULL;

    cJSON *thing_model = cJSON_GetObjectItem(root, "thing_model");
    if (!thing_model) {
        cJSON_Delete(root);
        return NULL;
    }

    acme_thing_model_t *model = calloc(1, sizeof(acme_thing_model_t));
    if (!model) {
        cJSON_Delete(root);
        return NULL;
    }

    // 解析基础信息
    cJSON *version = cJSON_GetObjectItem(thing_model, "version");
    if (version && cJSON_IsString(version)) {
        model->version = strdup(version->valuestring);
    }

    cJSON *model_id = cJSON_GetObjectItem(thing_model, "model_id");
    if (model_id && cJSON_IsString(model_id)) {
        model->model_id = strdup(model_id->valuestring);
    }

    cJSON *model_name = cJSON_GetObjectItem(thing_model, "model_name");
    if (model_name && cJSON_IsString(model_name)) {
        model->model_name = strdup(model_name->valuestring);
    }

    cJSON *device_type = cJSON_GetObjectItem(thing_model, "device_type");
    if (device_type && cJSON_IsString(device_type)) {
        model->device_type = strdup(device_type->valuestring);
    }

    cJSON *manufacturer = cJSON_GetObjectItem(thing_model, "manufacturer");
    if (manufacturer && cJSON_IsString(manufacturer)) {
        model->manufacturer = strdup(manufacturer->valuestring);
    }

    // 解析属性
    cJSON *properties = cJSON_GetObjectItem(thing_model, "properties");
    if (properties && cJSON_IsObject(properties)) {
        model->properties = parse_properties(properties);
    }

    // 解析服务
    cJSON *services = cJSON_GetObjectItem(thing_model, "services");
    if (services && cJSON_IsObject(services)) {
        model->services = parse_services(services);
    }

    // 解析事件
    cJSON *events = cJSON_GetObjectItem(thing_model, "events");
    if (events && cJSON_IsObject(events)) {
        model->events = parse_events(events);
    }

    // 解析扩展信息
    cJSON *extensions = cJSON_GetObjectItem(thing_model, "extensions");
    if (extensions) {
        model->extensions = cJSON_Duplicate(extensions, 1);
    }

    cJSON_Delete(root);
    return model;
}

// 解析属性列表
static acme_property_t *parse_properties(cJSON *properties)
{
    acme_property_t *head = NULL;
    acme_property_t *tail = NULL;

    cJSON *property = NULL;
    cJSON_ArrayForEach(property, properties) {
        if (!cJSON_IsObject(property)) continue;

        acme_property_t *prop = calloc(1, sizeof(acme_property_t));
        if (!prop) continue;

        // 属性标识符
        prop->identifier = strdup(property->string);

        // 属性名称
        cJSON *name = cJSON_GetObjectItem(property, "name");
        if (name && cJSON_IsString(name)) {
            prop->name = strdup(name->valuestring);
        }

        // 数据类型
        cJSON *data_type = cJSON_GetObjectItem(property, "data_type");
        if (data_type && cJSON_IsString(data_type)) {
            prop->data_type = parse_data_type(data_type->valuestring);
        }

        // 访问模式
        cJSON *access_mode = cJSON_GetObjectItem(property, "access_mode");
        if (access_mode && cJSON_IsString(access_mode)) {
            prop->access_mode = parse_access_mode(access_mode->valuestring);
        }

        // 是否必需
        cJSON *required = cJSON_GetObjectItem(property, "required");
        if (required && cJSON_IsBool(required)) {
            prop->required = cJSON_IsTrue(required);
        }

        // 单位
        cJSON *unit = cJSON_GetObjectItem(property, "unit");
        if (unit && cJSON_IsString(unit)) {
            prop->unit = strdup(unit->valuestring);
        }

        // 默认值
        cJSON *default_value = cJSON_GetObjectItem(property, "default_value");
        if (default_value) {
            prop->default_value = cJSON_Duplicate(default_value, 1);
        }

        // 取值范围
        cJSON *range = cJSON_GetObjectItem(property, "range");
        if (range) {
            prop->range = cJSON_Duplicate(range, 1);
        }

        // 枚举值
        cJSON *enum_values = cJSON_GetObjectItem(property, "enum_values");
        if (enum_values) {
            prop->enum_values = cJSON_Duplicate(enum_values, 1);
        }

        // 添加到链表
        if (!head) {
            head = tail = prop;
        } else {
            tail->next = prop;
            tail = prop;
        }
    }

    return head;
}

// 数据类型解析
static acme_data_type_e parse_data_type(const char *type_str)
{
    if (strcmp(type_str, "int32") == 0) return ACME_DATA_TYPE_INT32;
    if (strcmp(type_str, "float") == 0) return ACME_DATA_TYPE_FLOAT;
    if (strcmp(type_str, "double") == 0) return ACME_DATA_TYPE_DOUBLE;
    if (strcmp(type_str, "bool") == 0) return ACME_DATA_TYPE_BOOL;
    if (strcmp(type_str, "string") == 0) return ACME_DATA_TYPE_STRING;
    if (strcmp(type_str, "enum") == 0) return ACME_DATA_TYPE_ENUM;
    if (strcmp(type_str, "array") == 0) return ACME_DATA_TYPE_ARRAY;
    if (strcmp(type_str, "struct") == 0) return ACME_DATA_TYPE_STRUCT;
    return ACME_DATA_TYPE_STRING; // 默认类型
}

// 访问模式解析
static acme_access_mode_e parse_access_mode(const char *mode_str)
{
    if (strcmp(mode_str, "r") == 0) return ACME_ACCESS_READ;
    if (strcmp(mode_str, "w") == 0) return ACME_ACCESS_WRITE;
    if (strcmp(mode_str, "rw") == 0) return ACME_ACCESS_READ_WRITE;
    return ACME_ACCESS_READ; // 默认只读
}

// 获取属性
acme_property_t *acme_thing_model_get_property(acme_thing_model_t *model,
                                              const char *identifier)
{
    if (!model || !identifier) return NULL;

    acme_property_t *prop = model->properties;
    while (prop) {
        if (prop->identifier && strcmp(prop->identifier, identifier) == 0) {
            return prop;
        }
        prop = prop->next;
    }

    return NULL;
}

// 获取服务定义
acme_service_t *acme_thing_model_get_service(acme_thing_model_t *model, const char *service_id)
{
    if (!model || !service_id) return NULL;

    acme_service_t *service = model->services;
    while (service) {
        if (service->identifier && strcmp(service->identifier, service_id) == 0) {
            return service;
        }
        service = service->next;
    }

    return NULL;
}

// 获取事件定义
acme_event_t *acme_thing_model_get_event(acme_thing_model_t *model, const char *event_id)
{
    if (!model || !event_id) return NULL;

    acme_event_t *event = model->events;
    while (event) {
        if (event->identifier && strcmp(event->identifier, event_id) == 0) {
            return event;
        }
        event = event->next;
    }

    return NULL;
}

// 验证属性值
int acme_thing_model_validate_property_value(acme_thing_model_t *model,
                                            const char *property_id,
                                            cJSON *value)
{
    if (!model || !property_id || !value) return -1;

    acme_property_t *property = acme_thing_model_get_property(model, property_id);
    if (!property) return -1;

    // 根据数据类型验证
    switch (property->data_type) {
    case ACME_DATA_TYPE_INT32:
        if (!cJSON_IsNumber(value)) return -1;
        if (property->range.has_range) {
            int val = (int)cJSON_GetNumberValue(value);
            if (val < property->range.min || val > property->range.max) return -1;
        }
        break;

    case ACME_DATA_TYPE_FLOAT:
        if (!cJSON_IsNumber(value)) return -1;
        if (property->range.has_range) {
            double val = cJSON_GetNumberValue(value);
            if (val < property->range.min || val > property->range.max) return -1;
        }
        break;

    case ACME_DATA_TYPE_BOOL:
        if (!cJSON_IsBool(value)) return -1;
        break;

    case ACME_DATA_TYPE_STRING:
        if (!cJSON_IsString(value)) return -1;
        if (property->string_length > 0) {
            if (strlen(value->valuestring) > property->string_length) return -1;
        }
        break;

    case ACME_DATA_TYPE_ENUM:
        if (!cJSON_IsNumber(value)) return -1;
        int enum_val = (int)cJSON_GetNumberValue(value);
        bool valid_enum = false;
        for (int i = 0; i < property->enum_count; i++) {
            if (property->enum_values[i].value == enum_val) {
                valid_enum = true;
                break;
            }
        }
        if (!valid_enum) return -1;
        break;

    default:
        return -1;
    }

    return 0;
}

// 验证服务输入参数
int acme_thing_model_validate_service_input(acme_thing_model_t *model,
                                           const char *service_id,
                                           cJSON *input_params)
{
    if (!model || !service_id || !input_params) return -1;

    acme_service_t *service = acme_thing_model_get_service(model, service_id);
    if (!service) return -1;

    // 验证必需参数
    for (int i = 0; i < service->input_param_count; i++) {
        acme_service_param_t *param = &service->input_params[i];
        cJSON *param_value = cJSON_GetObjectItem(input_params, param->identifier);

        if (!param_value) {
            // 检查是否为必需参数
            if (param->required) return -1;
            continue;
        }

        // 验证参数类型和值
        if (validate_service_param_value(param, param_value) != 0) {
            return -1;
        }
    }

    return 0;
}

// 验证服务参数值
static int validate_service_param_value(acme_service_param_t *param, cJSON *value)
{
    if (!param || !value) return -1;

    switch (param->data_type) {
    case ACME_DATA_TYPE_INT32:
        if (!cJSON_IsNumber(value)) return -1;
        break;
    case ACME_DATA_TYPE_FLOAT:
        if (!cJSON_IsNumber(value)) return -1;
        break;
    case ACME_DATA_TYPE_BOOL:
        if (!cJSON_IsBool(value)) return -1;
        break;
    case ACME_DATA_TYPE_STRING:
        if (!cJSON_IsString(value)) return -1;
        break;
    default:
        return -1;
    }

    return 0;
}

// 创建事件数据
cJSON *acme_thing_model_create_event_data(acme_thing_model_t *model,
                                         const char *event_id,
                                         cJSON *output_params)
{
    if (!model || !event_id) return NULL;

    acme_event_t *event = acme_thing_model_get_event(model, event_id);
    if (!event) return NULL;

    cJSON *event_data = cJSON_CreateObject();
    cJSON_AddStringToObject(event_data, "identifier", event_id);
    cJSON_AddStringToObject(event_data, "name", event->name);
    cJSON_AddStringToObject(event_data, "event_type",
                           event->event_type == ACME_EVENT_INFO ? "info" :
                           event->event_type == ACME_EVENT_WARNING ? "warning" :
                           event->event_type == ACME_EVENT_ERROR ? "error" : "critical");
    cJSON_AddNumberToObject(event_data, "timestamp", time(NULL) * 1000);

    if (output_params) {
        cJSON_AddItemToObject(event_data, "output_params", cJSON_Duplicate(output_params, 1));
    }

    return event_data;
}
```

### 2. 协议映射实现

```c
// protocol_mapper.c
#include "protocol_mapper.h"
#include <stdlib.h>
#include <string.h>

// 解析MQTT协议消息
int acme_protocol_message_parse(const char *json_str, acme_protocol_message_t *message)
{
    if (!json_str || !message) return -1;

    cJSON *root = cJSON_Parse(json_str);
    if (!root) return -1;

    // 解析版本
    cJSON *version = cJSON_GetObjectItem(root, "version");
    if (version && cJSON_IsString(version)) {
        strncpy(message->version, version->valuestring, sizeof(message->version) - 1);
    }

    // 解析时间戳
    cJSON *timestamp = cJSON_GetObjectItem(root, "timestamp");
    if (timestamp && cJSON_IsNumber(timestamp)) {
        message->timestamp = (int64_t)timestamp->valuedouble;
    }

    // 解析请求ID
    cJSON *request_id = cJSON_GetObjectItem(root, "request_id");
    if (request_id && cJSON_IsString(request_id)) {
        strncpy(message->request_id, request_id->valuestring, sizeof(message->request_id) - 1);
    }

    // 解析消息类型
    cJSON *message_type = cJSON_GetObjectItem(root, "message_type");
    if (message_type && cJSON_IsString(message_type)) {
        message->message_type = parse_message_type(message_type->valuestring);
    }

    // 解析来源
    cJSON *source = cJSON_GetObjectItem(root, "source");
    if (source && cJSON_IsString(source)) {
        strncpy(message->source, source->valuestring, sizeof(message->source) - 1);
    }

    // 解析目标
    cJSON *target = cJSON_GetObjectItem(root, "target");
    if (target && cJSON_IsString(target)) {
        strncpy(message->target, target->valuestring, sizeof(message->target) - 1);
    }

    // 解析数据
    cJSON *data = cJSON_GetObjectItem(root, "data");
    if (data) {
        message->data = cJSON_Duplicate(data, 1);
    }

    cJSON_Delete(root);
    return 0;
}

// 读取请求映射到Neuron
int acme_map_read_request_to_neuron(const acme_protocol_message_t *mqtt_msg,
                                   acme_thing_model_t *model,
                                   neu_reqresp_head_t *neu_head,
                                   void **neu_data)
{
    if (!mqtt_msg || !model || !neu_head || !neu_data) return -1;

    if (mqtt_msg->message_type != ACME_MSG_READ_REQUEST) return -1;

    cJSON *data = mqtt_msg->data;
    if (!data) return -1;

    // 获取要读取的属性列表
    cJSON *properties = cJSON_GetObjectItem(data, "properties");
    if (!properties || !cJSON_IsArray(properties)) return -1;

    // 获取组名
    cJSON *group = cJSON_GetObjectItem(data, "group");
    char *group_name = "default_group";
    if (group && cJSON_IsString(group)) {
        group_name = group->valuestring;
    }

    // 构造Neuron读取请求
    neu_req_read_group_t *read_req = calloc(1, sizeof(neu_req_read_group_t));
    if (!read_req) return -1;

    strncpy(read_req->driver, mqtt_msg->target, sizeof(read_req->driver) - 1);
    strncpy(read_req->group, group_name, sizeof(read_req->group) - 1);

    // 设置请求头
    neu_head->type = NEU_REQ_READ_GROUP;
    neu_head->ctx = read_req;

    *neu_data = read_req;
    return 0;
}

// Neuron响应映射到读取响应
int acme_map_neuron_to_read_response(const neu_resp_read_group_t *neu_resp,
                                    acme_thing_model_t *model,
                                    acme_protocol_message_t *mqtt_msg)
{
    if (!neu_resp || !model || !mqtt_msg) return -1;

    // 创建响应数据
    cJSON *data = cJSON_CreateObject();
    if (!data) return -1;

    // 设置结果
    cJSON_AddStringToObject(data, "result", "success");
    cJSON_AddStringToObject(data, "thing_model_id", model->model_id);

    // 创建属性数组
    cJSON *properties = cJSON_CreateArray();
    if (!properties) {
        cJSON_Delete(data);
        return -1;
    }

    // 遍历Neuron响应的标签值
    for (int i = 0; i < neu_resp->n_tag; i++) {
        neu_resp_tag_value_t *tag_value = &neu_resp->tags[i];

        // 查找对应的物模型属性
        acme_property_t *prop = acme_thing_model_get_property(model, tag_value->tag);
        if (!prop) continue;

        cJSON *prop_obj = cJSON_CreateObject();
        if (!prop_obj) continue;

        cJSON_AddStringToObject(prop_obj, "identifier", tag_value->tag);

        // 根据数据类型添加值
        switch (prop->data_type) {
        case ACME_DATA_TYPE_INT32:
            cJSON_AddNumberToObject(prop_obj, "value", tag_value->value.i32);
            break;
        case ACME_DATA_TYPE_FLOAT:
            cJSON_AddNumberToObject(prop_obj, "value", tag_value->value.f32);
            break;
        case ACME_DATA_TYPE_BOOL:
            cJSON_AddBoolToObject(prop_obj, "value", tag_value->value.boolean);
            break;
        case ACME_DATA_TYPE_STRING:
            cJSON_AddStringToObject(prop_obj, "value", tag_value->value.str);
            break;
        default:
            cJSON_AddNullToObject(prop_obj, "value");
            break;
        }

        cJSON_AddStringToObject(prop_obj, "data_type", get_data_type_string(prop->data_type));
        if (prop->unit) {
            cJSON_AddStringToObject(prop_obj, "unit", prop->unit);
        }
        cJSON_AddNumberToObject(prop_obj, "timestamp", mqtt_msg->timestamp);
        cJSON_AddStringToObject(prop_obj, "quality", tag_value->error == 0 ? "good" : "bad");

        cJSON_AddItemToArray(properties, prop_obj);
    }

    cJSON_AddItemToObject(data, "properties", properties);

    // 设置消息信息
    mqtt_msg->message_type = ACME_MSG_READ_RESPONSE;
    strcpy(mqtt_msg->source, "gateway");
    strcpy(mqtt_msg->target, "cloud");
    mqtt_msg->data = data;

    return 0;
}
```

## 🔌 ACME_MQTT插件集成

### 1. 插件初始化集成

```c
// acme_mqtt_plugin.c 中的集成代码
#include "thing_model_manager.h"
#include "protocol_mapper.h"
#include "topic_manager.h"

// 扩展插件结构
struct neu_plugin {
    neu_plugin_common_t common;

    // 原有字段...
    neu_conn_t *conn;
    mqtt_config_t *config;

    // 新增物模型相关字段
    acme_thing_model_manager_t *model_manager;  // 物模型管理器
    char *gateway_id;                           // 网关ID
};

// 插件初始化时注册物模型
static int mqtt_plugin_init(neu_plugin_t *plugin, bool load)
{
    // 原有初始化代码...

    // 创建物模型管理器
    plugin->model_manager = acme_thing_model_manager_create();
    if (!plugin->model_manager) {
        plog_error(plugin, "Failed to create thing model manager");
        return -1;
    }

    // 获取网关ID
    plugin->gateway_id = get_gateway_id();

    // 注册预定义物模型
    register_builtin_models(plugin);

    plog_info(plugin, "Thing model manager initialized successfully");
    return 0;
}

// 注册内置物模型
static int register_builtin_models(neu_plugin_t *plugin)
{
    // FCM物模型JSON (简化版)
    const char *fcm_model = "{"
        "\"thing_model\": {"
            "\"version\": \"1.0\","
            "\"model_id\": \"acme.device.fcm.v1\","
            "\"model_name\": \"ACME FCM空调控制器\","
            "\"device_type\": \"FCM\","
            "\"manufacturer\": \"ACME\","
            "\"properties\": {"
                "\"ONOFF\": {"
                    "\"name\": \"开关状态\","
                    "\"data_type\": \"enum\","
                    "\"access_mode\": \"rw\","
                    "\"enum_values\": ["
                        "{\"value\": 0, \"name\": \"关闭\"},"
                        "{\"value\": 1, \"name\": \"开启\"}"
                    "]"
                "},"
                "\"STEMP\": {"
                    "\"name\": \"设定温度\","
                    "\"data_type\": \"float\","
                    "\"access_mode\": \"rw\","
                    "\"unit\": \"°C\","
                    "\"range\": {\"min\": 16.0, \"max\": 30.0}"
                "},"
                "\"RTEMP\": {"
                    "\"name\": \"实际温度\","
                    "\"data_type\": \"float\","
                    "\"access_mode\": \"r\","
                    "\"unit\": \"°C\""
                "}"
            "}"
        "}"
    "}";

    int ret = acme_thing_model_manager_register(plugin->model_manager, fcm_model);
    if (ret != 0) {
        plog_error(plugin, "Failed to register FCM thing model");
        return -1;
    }

    plog_info(plugin, "FCM thing model registered successfully");
    return 0;
}

// MQTT消息处理集成
static int mqtt_plugin_request(neu_plugin_t *plugin, neu_reqresp_head_t *head, void *data)
{
    switch (head->type) {
    case NEU_REQ_ACME_MQTT_DATA_PUSH: {
        // 原有数据推送处理...
        neu_acme_mqtt_data_push_t *push_data = (neu_acme_mqtt_data_push_t *)data;

        // 查找对应的物模型
        acme_thing_model_t *model = find_model_by_device_eui(plugin, push_data->device_eui);
        if (model) {
            // 使用物模型进行数据映射和发布
            handle_data_push_with_model(plugin, push_data, model);
        } else {
            // 使用原有方式处理
            handle_acme_mqtt_data_push(plugin, push_data);
        }
        break;
    }
    default:
        // 其他请求处理...
        break;
    }

    return 0;
}

// 基于物模型的数据推送处理
static int handle_data_push_with_model(neu_plugin_t *plugin,
                                      neu_acme_mqtt_data_push_t *push_data,
                                      acme_thing_model_t *model)
{
    // 构造协议消息
    acme_protocol_message_t mqtt_msg = {0};
    strcpy(mqtt_msg.version, "1.0");
    mqtt_msg.timestamp = push_data->timestamp;
    snprintf(mqtt_msg.request_id, sizeof(mqtt_msg.request_id), "data_push_%ld", mqtt_msg.timestamp);
    mqtt_msg.message_type = ACME_MSG_READ_RESPONSE;
    strcpy(mqtt_msg.source, "gateway");
    strcpy(mqtt_msg.target, "cloud");

    // 创建数据对象
    cJSON *data = cJSON_CreateObject();
    cJSON_AddStringToObject(data, "result", "success");
    cJSON_AddStringToObject(data, "thing_model_id", model->model_id);

    // 创建属性数组
    cJSON *properties = cJSON_CreateArray();

    for (int i = 0; i < push_data->n_tag; i++) {
        neu_resp_tag_value_t *tag_value = &push_data->tags[i];

        // 查找物模型属性
        acme_property_t *prop = acme_thing_model_get_property(model, tag_value->tag);
        if (!prop) continue;

        cJSON *prop_obj = cJSON_CreateObject();
        cJSON_AddStringToObject(prop_obj, "identifier", tag_value->tag);
        cJSON_AddStringToObject(prop_obj, "name", prop->name);

        // 根据物模型数据类型处理值
        switch (prop->data_type) {
        case ACME_DATA_TYPE_INT32:
            cJSON_AddNumberToObject(prop_obj, "value", tag_value->value.i32);
            break;
        case ACME_DATA_TYPE_FLOAT:
            cJSON_AddNumberToObject(prop_obj, "value", tag_value->value.f32);
            break;
        case ACME_DATA_TYPE_BOOL:
            cJSON_AddBoolToObject(prop_obj, "value", tag_value->value.boolean);
            break;
        case ACME_DATA_TYPE_ENUM:
            cJSON_AddNumberToObject(prop_obj, "value", tag_value->value.i32);
            // 添加枚举名称
            if (prop->enum_values) {
                const char *enum_name = get_enum_name(prop->enum_values, tag_value->value.i32);
                if (enum_name) {
                    cJSON_AddStringToObject(prop_obj, "enum_name", enum_name);
                }
            }
            break;
        default:
            cJSON_AddStringToObject(prop_obj, "value", tag_value->value.str);
            break;
        }

        cJSON_AddStringToObject(prop_obj, "data_type", get_data_type_string(prop->data_type));
        if (prop->unit) {
            cJSON_AddStringToObject(prop_obj, "unit", prop->unit);
        }
        cJSON_AddNumberToObject(prop_obj, "timestamp", mqtt_msg.timestamp);
        cJSON_AddStringToObject(prop_obj, "quality", tag_value->error == 0 ? "good" : "bad");

        cJSON_AddItemToArray(properties, prop_obj);
    }

    cJSON_AddItemToObject(data, "properties", properties);
    mqtt_msg.data = data;

    // 生成主题
    char *topic = acme_topic_generate(ACME_MSG_READ_RESPONSE,
                                     plugin->gateway_id,
                                     push_data->device_eui,
                                     NULL);

    // 编码消息
    char *payload = acme_protocol_message_encode(&mqtt_msg);

    // 发布MQTT消息
    if (topic && payload) {
        neu_conn_publish(plugin->conn, topic, payload, strlen(payload), 1, false);
        plog_info(plugin, "Published thing model data to topic: %s", topic);
    }

    // 清理资源
    if (topic) free(topic);
    if (payload) free(payload);
    acme_protocol_message_free(&mqtt_msg);

    return 0;
}

// 根据设备EUI查找物模型
static acme_thing_model_t *find_model_by_device_eui(neu_plugin_t *plugin, const char *device_eui)
{
    // 这里可以根据设备EUI的前缀或配置信息确定设备类型
    // 简化实现：假设所有设备都是FCM类型
    return acme_thing_model_manager_get(plugin->model_manager, "acme.device.fcm.v1");
}

// 获取枚举名称
static const char *get_enum_name(cJSON *enum_values, int value)
{
    if (!enum_values || !cJSON_IsArray(enum_values)) return NULL;

    cJSON *enum_item = NULL;
    cJSON_ArrayForEach(enum_item, enum_values) {
        cJSON *enum_value = cJSON_GetObjectItem(enum_item, "value");
        cJSON *enum_name = cJSON_GetObjectItem(enum_item, "name");

        if (enum_value && cJSON_IsNumber(enum_value) &&
            enum_name && cJSON_IsString(enum_name) &&
            (int)enum_value->valuedouble == value) {
            return enum_name->valuestring;
        }
    }

    return NULL;
}

// 获取数据类型字符串
static const char *get_data_type_string(acme_data_type_e data_type)
{
    switch (data_type) {
    case ACME_DATA_TYPE_INT32: return "int32";
    case ACME_DATA_TYPE_FLOAT: return "float";
    case ACME_DATA_TYPE_DOUBLE: return "double";
    case ACME_DATA_TYPE_BOOL: return "bool";
    case ACME_DATA_TYPE_STRING: return "string";
    case ACME_DATA_TYPE_ENUM: return "enum";
    case ACME_DATA_TYPE_ARRAY: return "array";
    case ACME_DATA_TYPE_STRUCT: return "struct";
    default: return "unknown";
    }
}
```

### 2. MQTT订阅主题配置

```c
// 订阅物模型相关主题
static int setup_thing_model_subscriptions(neu_plugin_t *plugin)
{
    if (!plugin->gateway_id) return -1;

    // 订阅读取请求
    char *read_topic = acme_topic_generate(ACME_MSG_READ_REQUEST,
                                          plugin->gateway_id, "+", NULL);
    if (read_topic) {
        neu_conn_subscribe(plugin->conn, read_topic, 1);
        plog_info(plugin, "Subscribed to read request topic: %s", read_topic);
        free(read_topic);
    }

    // 订阅写入请求
    char *write_topic = acme_topic_generate(ACME_MSG_WRITE_REQUEST,
                                           plugin->gateway_id, "+", NULL);
    if (write_topic) {
        neu_conn_subscribe(plugin->conn, write_topic, 1);
        plog_info(plugin, "Subscribed to write request topic: %s", write_topic);
        free(write_topic);
    }

    // 订阅服务请求
    char *service_topic = acme_topic_generate(ACME_MSG_SERVICE_REQUEST,
                                             plugin->gateway_id, "+", NULL);
    if (service_topic) {
        neu_conn_subscribe(plugin->conn, service_topic, 1);
        plog_info(plugin, "Subscribed to service request topic: %s", service_topic);
        free(service_topic);
    }

    // 订阅物模型发现请求
    char *discover_topic = acme_topic_generate(ACME_MSG_MODEL_DISCOVER_REQUEST,
                                              plugin->gateway_id, "+", NULL);
    if (discover_topic) {
        neu_conn_subscribe(plugin->conn, discover_topic, 1);
        plog_info(plugin, "Subscribed to model discover topic: %s", discover_topic);
        free(discover_topic);
    }

    return 0;
}

## 🧪 测试用例

### 1. 物模型解析测试

```c
// test_thing_model.c
#include "thing_model_manager.h"
#include <assert.h>
#include <stdio.h>

void test_fcm_model_parsing()
{
    const char *fcm_json = "{"
        "\"thing_model\": {"
            "\"version\": \"1.0\","
            "\"model_id\": \"acme.device.fcm.v1\","
            "\"model_name\": \"ACME FCM空调控制器\","
            "\"device_type\": \"FCM\","
            "\"manufacturer\": \"ACME\","
            "\"properties\": {"
                "\"ONOFF\": {"
                    "\"name\": \"开关状态\","
                    "\"data_type\": \"enum\","
                    "\"access_mode\": \"rw\","
                    "\"enum_values\": ["
                        "{\"value\": 0, \"name\": \"关闭\"},"
                        "{\"value\": 1, \"name\": \"开启\"}"
                    "]"
                "},"
                "\"STEMP\": {"
                    "\"name\": \"设定温度\","
                    "\"data_type\": \"float\","
                    "\"access_mode\": \"rw\","
                    "\"unit\": \"°C\","
                    "\"range\": {\"min\": 16.0, \"max\": 30.0}"
                "}"
            "}"
        "}"
    "}";

    // 解析物模型
    acme_thing_model_t *model = acme_thing_model_parse(fcm_json);
    assert(model != NULL);
    assert(strcmp(model->model_id, "acme.device.fcm.v1") == 0);
    assert(strcmp(model->device_type, "FCM") == 0);

    // 测试属性查找
    acme_property_t *onoff_prop = acme_thing_model_get_property(model, "ONOFF");
    assert(onoff_prop != NULL);
    assert(onoff_prop->data_type == ACME_DATA_TYPE_ENUM);
    assert(onoff_prop->access_mode == ACME_ACCESS_READ_WRITE);

    acme_property_t *stemp_prop = acme_thing_model_get_property(model, "STEMP");
    assert(stemp_prop != NULL);
    assert(stemp_prop->data_type == ACME_DATA_TYPE_FLOAT);
    assert(strcmp(stemp_prop->unit, "°C") == 0);

    // 清理资源
    acme_thing_model_free(model);

    printf("✅ FCM model parsing test passed\n");
}

void test_model_manager()
{
    // 创建管理器
    acme_thing_model_manager_t *manager = acme_thing_model_manager_create();
    assert(manager != NULL);

    // 注册模型
    const char *fcm_json = "..."; // FCM模型JSON
    int ret = acme_thing_model_manager_register(manager, fcm_json);
    assert(ret == 0);

    // 获取模型
    acme_thing_model_t *model = acme_thing_model_manager_get(manager, "acme.device.fcm.v1");
    assert(model != NULL);
    assert(strcmp(model->model_id, "acme.device.fcm.v1") == 0);

    // 清理资源
    acme_thing_model_manager_destroy(manager);

    printf("✅ Model manager test passed\n");
}
```

### 2. 协议映射测试

```c
// test_protocol_mapper.c
void test_read_request_mapping()
{
    // 准备MQTT读取请求
    const char *mqtt_json = "{"
        "\"version\": \"1.0\","
        "\"timestamp\": 1703123456789,"
        "\"request_id\": \"read_req_001\","
        "\"message_type\": \"read_request\","
        "\"source\": \"cloud\","
        "\"target\": \"FCM_001\","
        "\"data\": {"
            "\"properties\": [\"ONOFF\", \"STEMP\", \"RTEMP\"],"
            "\"group\": \"default_group\""
        "}"
    "}";

    // 解析MQTT消息
    acme_protocol_message_t mqtt_msg = {0};
    int ret = acme_protocol_message_parse(mqtt_json, &mqtt_msg);
    assert(ret == 0);
    assert(mqtt_msg.message_type == ACME_MSG_READ_REQUEST);

    // 准备物模型
    acme_thing_model_t *model = load_test_fcm_model();
    assert(model != NULL);

    // 映射到Neuron请求
    neu_reqresp_head_t neu_head = {0};
    void *neu_data = NULL;
    ret = acme_map_read_request_to_neuron(&mqtt_msg, model, &neu_head, &neu_data);
    assert(ret == 0);
    assert(neu_head.type == NEU_REQ_READ_GROUP);

    neu_req_read_group_t *read_req = (neu_req_read_group_t *)neu_data;
    assert(strcmp(read_req->driver, "FCM_001") == 0);
    assert(strcmp(read_req->group, "default_group") == 0);

    // 清理资源
    free(neu_data);
    acme_protocol_message_free(&mqtt_msg);
    acme_thing_model_free(model);

    printf("✅ Read request mapping test passed\n");
}

void test_data_response_mapping()
{
    // 准备Neuron响应数据
    neu_resp_read_group_t neu_resp = {0};
    neu_resp.n_tag = 3;
    neu_resp.tags = calloc(3, sizeof(neu_resp_tag_value_t));

    // ONOFF属性
    strcpy(neu_resp.tags[0].tag, "ONOFF");
    neu_resp.tags[0].value.i32 = 1;
    neu_resp.tags[0].error = 0;

    // STEMP属性
    strcpy(neu_resp.tags[1].tag, "STEMP");
    neu_resp.tags[1].value.f32 = 25.5;
    neu_resp.tags[1].error = 0;

    // RTEMP属性
    strcpy(neu_resp.tags[2].tag, "RTEMP");
    neu_resp.tags[2].value.f32 = 24.8;
    neu_resp.tags[2].error = 0;

    // 准备物模型
    acme_thing_model_t *model = load_test_fcm_model();

    // 映射到MQTT响应
    acme_protocol_message_t mqtt_msg = {0};
    mqtt_msg.timestamp = 1703123456789;
    strcpy(mqtt_msg.request_id, "read_req_001");

    int ret = acme_map_neuron_to_read_response(&neu_resp, model, &mqtt_msg);
    assert(ret == 0);
    assert(mqtt_msg.message_type == ACME_MSG_READ_RESPONSE);

    // 验证响应数据
    cJSON *data = mqtt_msg.data;
    assert(data != NULL);

    cJSON *result = cJSON_GetObjectItem(data, "result");
    assert(result && strcmp(result->valuestring, "success") == 0);

    cJSON *properties = cJSON_GetObjectItem(data, "properties");
    assert(properties && cJSON_IsArray(properties));
    assert(cJSON_GetArraySize(properties) == 3);

    // 验证ONOFF属性
    cJSON *onoff_prop = cJSON_GetArrayItem(properties, 0);
    cJSON *identifier = cJSON_GetObjectItem(onoff_prop, "identifier");
    cJSON *value = cJSON_GetObjectItem(onoff_prop, "value");
    assert(identifier && strcmp(identifier->valuestring, "ONOFF") == 0);
    assert(value && value->valueint == 1);

    // 清理资源
    free(neu_resp.tags);
    acme_protocol_message_free(&mqtt_msg);
    acme_thing_model_free(model);

    printf("✅ Data response mapping test passed\n");
}
```

### 3. 主题管理测试

```c
// test_topic_manager.c
void test_topic_generation()
{
    // 测试读取请求主题生成
    char *topic = acme_topic_generate(ACME_MSG_READ_REQUEST, "SPT_GW_001", "FCM_001", NULL);
    assert(topic != NULL);
    assert(strcmp(topic, "/acme/SPT_GW_001/FCM_001/read/request") == 0);
    free(topic);

    // 测试响应主题生成
    topic = acme_topic_generate(ACME_MSG_RESPONSE, "SPT_GW_001", "FCM_001", "req_123");
    assert(topic != NULL);
    assert(strcmp(topic, "/acme/SPT_GW_001/FCM_001/response/req_123") == 0);
    free(topic);

    printf("✅ Topic generation test passed\n");
}

void test_topic_parsing()
{
    const char *topic = "/acme/SPT_GW_001/FCM_001/read/request";

    acme_topic_info_t info = {0};
    int ret = acme_topic_parse(topic, &info);
    assert(ret == 0);
    assert(strcmp(info.gateway_id, "SPT_GW_001") == 0);
    assert(strcmp(info.device_id, "FCM_001") == 0);
    assert(info.msg_type == ACME_MSG_READ_REQUEST);

    acme_topic_info_free(&info);

    printf("✅ Topic parsing test passed\n");
}
```

## 🚀 部署指南

### 1. 编译配置

```cmake
# CMakeLists.txt 修改
# 添加物模型相关源文件
set(THING_MODEL_SOURCES
    thing_model_manager.c
    protocol_mapper.c
    topic_manager.c
)

# 添加到插件源文件列表
add_library(${PROJECT_NAME} SHARED
    acme_mqtt_plugin.c
    mqtt_plugin_intf.c
    mqtt_handle.c
    mqtt_config.c
    ${THING_MODEL_SOURCES}
    ${PROTOCOL_SOURCES}
    ${CONNECT_SOURCES}
)

# 添加cJSON依赖
find_package(PkgConfig REQUIRED)
pkg_check_modules(CJSON REQUIRED libcjson)

target_link_libraries(${PROJECT_NAME}
    ${SDK_PATH}/build/libneuron-base.so
    ${CJSON_LIBRARIES}
)

target_include_directories(${PROJECT_NAME} PRIVATE
    ${CJSON_INCLUDE_DIRS}
)
```

### 2. 配置文件更新

```json
// acme-mqtt.json 配置扩展
{
    "host": {
        "name": "host",
        "type": "string",
        "default": "127.0.0.1",
        "description": "MQTT服务器地址"
    },
    "port": {
        "name": "port",
        "type": "int",
        "default": 1883,
        "description": "MQTT服务器端口"
    },
    "gateway_id": {
        "name": "gateway_id",
        "type": "string",
        "default": "SPT_GW_001",
        "description": "网关唯一标识符"
    },
    "thing_model_enabled": {
        "name": "thing_model_enabled",
        "type": "bool",
        "default": true,
        "description": "是否启用物模型功能"
    },
    "model_discovery_enabled": {
        "name": "model_discovery_enabled",
        "type": "bool",
        "default": true,
        "description": "是否启用物模型发现"
    },
    "builtin_models": {
        "name": "builtin_models",
        "type": "array",
        "default": ["acme.device.fcm.v1", "acme.device.ecm.v1"],
        "description": "内置物模型列表"
    }
}
```

### 3. 运行时验证

```bash
#!/bin/bash
# deploy_test.sh - 部署测试脚本

echo "🚀 开始部署ACME_MQTT物模型功能..."

# 1. 编译插件
echo "📦 编译插件..."
cd /path/to/acme_mqtt
mkdir -p build && cd build
cmake ..
make -j4

if [ $? -ne 0 ]; then
    echo "❌ 编译失败"
    exit 1
fi

# 2. 部署插件
echo "📋 部署插件..."
cp plugin-acme-mqtt.so /opt/neuron/plugins/
cp schema/acme-mqtt.json /opt/neuron/plugins/schema/

# 3. 重启Neuron服务
echo "🔄 重启Neuron服务..."
systemctl restart neuron

# 4. 等待服务启动
sleep 5

# 5. 验证插件加载
echo "✅ 验证插件加载..."
curl -X GET "http://localhost:7000/api/v2/plugins" | grep "acme-mqtt"

if [ $? -eq 0 ]; then
    echo "✅ 插件加载成功"
else
    echo "❌ 插件加载失败"
    exit 1
fi

# 6. 创建MQTT应用节点
echo "📱 创建MQTT应用节点..."
curl -X POST "http://localhost:7000/api/v2/node" \
    -H "Content-Type: application/json" \
    -d '{
        "name": "mqtt-app",
        "plugin": "acme-mqtt"
    }'

# 7. 配置MQTT连接
echo "⚙️ 配置MQTT连接..."
curl -X POST "http://localhost:7000/api/v2/node/mqtt-app/setting" \
    -H "Content-Type: application/json" \
    -d '{
        "host": "127.0.0.1",
        "port": 1883,
        "gateway_id": "SPT_GW_001",
        "thing_model_enabled": true
    }'

echo "🎉 部署完成！"
echo "📊 可以通过以下方式验证："
echo "   1. 查看Neuron日志: tail -f /var/log/neuron/neuron.log"
echo "   2. 监控MQTT主题: mosquitto_sub -h 127.0.0.1 -t '/acme/SPT_GW_001/+/+'"
echo "   3. 发送测试消息验证物模型功能"
```

## 📊 性能优化建议

### 1. 内存优化
- 使用对象池管理物模型实例
- 实现物模型缓存机制
- 及时释放JSON对象内存

### 2. 性能优化
- 预编译常用物模型
- 使用哈希表加速属性查找
- 批量处理MQTT消息

### 3. 可靠性增强
- 添加物模型版本兼容性检查
- 实现消息重传机制
- 完善错误处理和日志记录

## 🔐 安全认证实现

### 1. 设备认证管理器

```c
// security_manager.h
#ifndef ACME_SECURITY_MANAGER_H
#define ACME_SECURITY_MANAGER_H

#include <openssl/rsa.h>
#include <openssl/x509.h>
#include <openssl/evp.h>

// 认证类型枚举
typedef enum {
    ACME_AUTH_CERTIFICATE = 0,
    ACME_AUTH_PSK,
    ACME_AUTH_JWT,
    ACME_AUTH_SIGNATURE
} acme_auth_type_e;

// 设备证书结构
typedef struct acme_device_certificate {
    char *device_id;
    char *device_eui;
    acme_auth_type_e cert_type;
    X509 *certificate;
    EVP_PKEY *private_key;
    X509 *ca_certificate;
    time_t not_before;
    time_t not_after;

    // 权限信息
    char **read_properties;
    int read_prop_count;
    char **write_properties;
    int write_prop_count;
    char **allowed_services;
    int service_count;
} acme_device_certificate_t;

// 安全管理器
typedef struct acme_security_manager {
    acme_device_certificate_t **certificates;
    int cert_count;
    int capacity;

    // 密钥管理
    EVP_PKEY *signing_key;
    X509 *ca_cert;

    // 配置
    bool signature_required;
    bool encryption_enabled;
    char *cipher_suite;
} acme_security_manager_t;

// 函数声明
acme_security_manager_t *acme_security_manager_create(void);
void acme_security_manager_destroy(acme_security_manager_t *manager);

int acme_security_manager_load_certificate(acme_security_manager_t *manager,
                                          const char *cert_file,
                                          const char *key_file,
                                          const char *ca_file);

int acme_security_manager_authenticate_device(acme_security_manager_t *manager,
                                            const char *device_id,
                                            const char *credentials);

bool acme_security_manager_check_permission(acme_security_manager_t *manager,
                                           const char *device_id,
                                           const char *resource,
                                           const char *action);

int acme_security_manager_sign_message(acme_security_manager_t *manager,
                                      const char *message,
                                      char **signature);

int acme_security_manager_verify_signature(acme_security_manager_t *manager,
                                          const char *message,
                                          const char *signature,
                                          const char *device_id);

#endif // ACME_SECURITY_MANAGER_H
```

### 2. 消息签名验证实现

```c
// security_manager.c
#include "security_manager.h"
#include <openssl/sha.h>
#include <openssl/pem.h>
#include <string.h>

// 创建安全管理器
acme_security_manager_t *acme_security_manager_create(void)
{
    acme_security_manager_t *manager = calloc(1, sizeof(acme_security_manager_t));
    if (!manager) return NULL;

    manager->capacity = 100;
    manager->certificates = calloc(manager->capacity, sizeof(acme_device_certificate_t*));
    if (!manager->certificates) {
        free(manager);
        return NULL;
    }

    manager->signature_required = true;
    manager->encryption_enabled = true;
    manager->cipher_suite = strdup("AES-256-GCM");

    return manager;
}

// 加载设备证书
int acme_security_manager_load_certificate(acme_security_manager_t *manager,
                                          const char *cert_file,
                                          const char *key_file,
                                          const char *ca_file)
{
    if (!manager || !cert_file || !key_file) return -1;

    FILE *fp = NULL;
    X509 *cert = NULL;
    EVP_PKEY *key = NULL;
    X509 *ca_cert = NULL;

    // 加载设备证书
    fp = fopen(cert_file, "r");
    if (!fp) return -1;

    cert = PEM_read_X509(fp, NULL, NULL, NULL);
    fclose(fp);
    if (!cert) return -1;

    // 加载私钥
    fp = fopen(key_file, "r");
    if (!fp) {
        X509_free(cert);
        return -1;
    }

    key = PEM_read_PrivateKey(fp, NULL, NULL, NULL);
    fclose(fp);
    if (!key) {
        X509_free(cert);
        return -1;
    }

    // 加载CA证书
    if (ca_file) {
        fp = fopen(ca_file, "r");
        if (fp) {
            ca_cert = PEM_read_X509(fp, NULL, NULL, NULL);
            fclose(fp);
        }
    }

    // 创建设备证书结构
    acme_device_certificate_t *dev_cert = calloc(1, sizeof(acme_device_certificate_t));
    if (!dev_cert) {
        X509_free(cert);
        EVP_PKEY_free(key);
        if (ca_cert) X509_free(ca_cert);
        return -1;
    }

    // 从证书中提取设备信息
    X509_NAME *subject = X509_get_subject_name(cert);
    char device_id[256] = {0};
    X509_NAME_get_text_by_NID(subject, NID_commonName, device_id, sizeof(device_id));

    dev_cert->device_id = strdup(device_id);
    dev_cert->cert_type = ACME_AUTH_CERTIFICATE;
    dev_cert->certificate = cert;
    dev_cert->private_key = key;
    dev_cert->ca_certificate = ca_cert;

    // 获取证书有效期
    ASN1_TIME *not_before = X509_get_notBefore(cert);
    ASN1_TIME *not_after = X509_get_notAfter(cert);
    dev_cert->not_before = ASN1_TIME_to_time_t(not_before);
    dev_cert->not_after = ASN1_TIME_to_time_t(not_after);

    // 添加到管理器
    if (manager->cert_count >= manager->capacity) {
        // 扩容
        manager->capacity *= 2;
        manager->certificates = realloc(manager->certificates,
                                       manager->capacity * sizeof(acme_device_certificate_t*));
    }

    manager->certificates[manager->cert_count++] = dev_cert;

    return 0;
}

// 设备认证
int acme_security_manager_authenticate_device(acme_security_manager_t *manager,
                                            const char *device_id,
                                            const char *credentials)
{
    if (!manager || !device_id || !credentials) return -1;

    // 查找设备证书
    acme_device_certificate_t *cert = find_device_certificate(manager, device_id);
    if (!cert) return -1;

    // 检查证书有效期
    time_t now = time(NULL);
    if (now < cert->not_before || now > cert->not_after) {
        return -1; // 证书过期
    }

    // 验证证书链
    if (cert->ca_certificate) {
        X509_STORE *store = X509_STORE_new();
        X509_STORE_add_cert(store, cert->ca_certificate);

        X509_STORE_CTX *ctx = X509_STORE_CTX_new();
        X509_STORE_CTX_init(ctx, store, cert->certificate, NULL);

        int verify_result = X509_verify_cert(ctx);

        X509_STORE_CTX_free(ctx);
        X509_STORE_free(store);

        if (verify_result != 1) {
            return -1; // 证书验证失败
        }
    }

    return 0; // 认证成功
}

// 权限检查
bool acme_security_manager_check_permission(acme_security_manager_t *manager,
                                           const char *device_id,
                                           const char *resource,
                                           const char *action)
{
    if (!manager || !device_id || !resource || !action) return false;

    acme_device_certificate_t *cert = find_device_certificate(manager, device_id);
    if (!cert) return false;

    // 检查读权限
    if (strcmp(action, "read") == 0) {
        for (int i = 0; i < cert->read_prop_count; i++) {
            if (strcmp(cert->read_properties[i], resource) == 0) {
                return true;
            }
        }
    }

    // 检查写权限
    if (strcmp(action, "write") == 0) {
        for (int i = 0; i < cert->write_prop_count; i++) {
            if (strcmp(cert->write_properties[i], resource) == 0) {
                return true;
            }
        }
    }

    // 检查服务调用权限
    if (strcmp(action, "call") == 0) {
        for (int i = 0; i < cert->service_count; i++) {
            if (strcmp(cert->allowed_services[i], resource) == 0) {
                return true;
            }
        }
    }

    return false;
}

// 消息签名
int acme_security_manager_sign_message(acme_security_manager_t *manager,
                                      const char *message,
                                      char **signature)
{
    if (!manager || !message || !signature) return -1;
    if (!manager->signing_key) return -1;

    EVP_MD_CTX *ctx = EVP_MD_CTX_new();
    if (!ctx) return -1;

    // 初始化签名上下文
    if (EVP_SignInit(ctx, EVP_sha256()) != 1) {
        EVP_MD_CTX_free(ctx);
        return -1;
    }

    // 更新要签名的数据
    if (EVP_SignUpdate(ctx, message, strlen(message)) != 1) {
        EVP_MD_CTX_free(ctx);
        return -1;
    }

    // 执行签名
    unsigned char *sig_buf = malloc(EVP_PKEY_size(manager->signing_key));
    unsigned int sig_len = 0;

    if (EVP_SignFinal(ctx, sig_buf, &sig_len, manager->signing_key) != 1) {
        EVP_MD_CTX_free(ctx);
        free(sig_buf);
        return -1;
    }

    // Base64编码签名
    *signature = base64_encode(sig_buf, sig_len);

    EVP_MD_CTX_free(ctx);
    free(sig_buf);

    return 0;
}

// 签名验证
int acme_security_manager_verify_signature(acme_security_manager_t *manager,
                                          const char *message,
                                          const char *signature,
                                          const char *device_id)
{
    if (!manager || !message || !signature || !device_id) return -1;

    acme_device_certificate_t *cert = find_device_certificate(manager, device_id);
    if (!cert || !cert->certificate) return -1;

    // 获取公钥
    EVP_PKEY *pub_key = X509_get_pubkey(cert->certificate);
    if (!pub_key) return -1;

    // Base64解码签名
    unsigned char *sig_buf = NULL;
    int sig_len = base64_decode(signature, &sig_buf);
    if (sig_len <= 0) {
        EVP_PKEY_free(pub_key);
        return -1;
    }

    EVP_MD_CTX *ctx = EVP_MD_CTX_new();
    if (!ctx) {
        EVP_PKEY_free(pub_key);
        free(sig_buf);
        return -1;
    }

    // 初始化验证上下文
    if (EVP_VerifyInit(ctx, EVP_sha256()) != 1) {
        EVP_MD_CTX_free(ctx);
        EVP_PKEY_free(pub_key);
        free(sig_buf);
        return -1;
    }

    // 更新要验证的数据
    if (EVP_VerifyUpdate(ctx, message, strlen(message)) != 1) {
        EVP_MD_CTX_free(ctx);
        EVP_PKEY_free(pub_key);
        free(sig_buf);
        return -1;
    }

    // 执行验证
    int verify_result = EVP_VerifyFinal(ctx, sig_buf, sig_len, pub_key);

    EVP_MD_CTX_free(ctx);
    EVP_PKEY_free(pub_key);
    free(sig_buf);

    return (verify_result == 1) ? 0 : -1;
}

// 查找设备证书
static acme_device_certificate_t *find_device_certificate(acme_security_manager_t *manager,
                                                         const char *device_id)
{
    for (int i = 0; i < manager->cert_count; i++) {
        if (manager->certificates[i]->device_id &&
            strcmp(manager->certificates[i]->device_id, device_id) == 0) {
            return manager->certificates[i];
        }
    }
    return NULL;
}
```

### 3. 安全MQTT插件集成

```c
// 在acme_mqtt_plugin.c中集成安全功能
#include "security_manager.h"

// 扩展插件结构
struct neu_plugin {
    neu_plugin_common_t common;

    // 原有字段...
    neu_conn_t *conn;
    mqtt_config_t *config;
    acme_thing_model_manager_t *model_manager;

    // 新增安全相关字段
    acme_security_manager_t *security_manager;
    bool security_enabled;
    char *ca_cert_file;
    char *device_cert_file;
    char *device_key_file;
};

// 安全初始化
static int mqtt_plugin_security_init(neu_plugin_t *plugin)
{
    // 创建安全管理器
    plugin->security_manager = acme_security_manager_create();
    if (!plugin->security_manager) {
        plog_error(plugin, "Failed to create security manager");
        return -1;
    }

    // 加载证书
    if (plugin->device_cert_file && plugin->device_key_file) {
        int ret = acme_security_manager_load_certificate(plugin->security_manager,
                                                        plugin->device_cert_file,
                                                        plugin->device_key_file,
                                                        plugin->ca_cert_file);
        if (ret != 0) {
            plog_error(plugin, "Failed to load device certificate");
            return -1;
        }

        plugin->security_enabled = true;
        plog_info(plugin, "Security manager initialized with certificates");
    }

    return 0;
}

// 安全消息处理
static int handle_secure_mqtt_message(neu_plugin_t *plugin,
                                     const char *topic,
                                     const char *payload)
{
    if (!plugin->security_enabled) {
        // 如果未启用安全功能，使用原有处理方式
        return handle_mqtt_message(plugin, topic, payload);
    }

    // 解析消息
    acme_protocol_message_t mqtt_msg = {0};
    int ret = acme_protocol_message_parse(payload, &mqtt_msg);
    if (ret != 0) {
        plog_error(plugin, "Failed to parse MQTT message");
        return -1;
    }

    // 提取设备ID
    acme_topic_info_t topic_info = {0};
    ret = acme_topic_parse(topic, &topic_info);
    if (ret != 0) {
        plog_error(plugin, "Failed to parse topic: %s", topic);
        acme_protocol_message_free(&mqtt_msg);
        return -1;
    }

    // 验证消息签名
    if (mqtt_msg.data) {
        cJSON *security = cJSON_GetObjectItem(mqtt_msg.data, "security");
        if (security) {
            cJSON *signature = cJSON_GetObjectItem(security, "signature");
            if (signature && cJSON_IsString(signature)) {
                // 构造待验证的消息内容
                char *message_to_verify = create_verification_message(&mqtt_msg);

                ret = acme_security_manager_verify_signature(plugin->security_manager,
                                                           message_to_verify,
                                                           signature->valuestring,
                                                           topic_info.device_id);
                free(message_to_verify);

                if (ret != 0) {
                    plog_warn(plugin, "Message signature verification failed for device: %s",
                             topic_info.device_id);

                    // 记录安全事件
                    log_security_event(plugin, "signature_verification_failed",
                                      topic_info.device_id, topic);

                    acme_protocol_message_free(&mqtt_msg);
                    acme_topic_info_free(&topic_info);
                    return -1;
                }
            }
        }
    }

    // 检查权限
    const char *action = get_action_from_message_type(mqtt_msg.message_type);
    if (action) {
        cJSON *data = mqtt_msg.data;
        if (data) {
            cJSON *properties = cJSON_GetObjectItem(data, "properties");
            if (properties && cJSON_IsArray(properties)) {
                cJSON *prop = NULL;
                cJSON_ArrayForEach(prop, properties) {
                    if (cJSON_IsString(prop)) {
                        bool allowed = acme_security_manager_check_permission(
                            plugin->security_manager,
                            topic_info.device_id,
                            prop->valuestring,
                            action);

                        if (!allowed) {
                            plog_warn(plugin, "Access denied for device %s, property %s, action %s",
                                     topic_info.device_id, prop->valuestring, action);

                            // 记录安全事件
                            log_security_event(plugin, "unauthorized_access",
                                              topic_info.device_id, prop->valuestring);

                            acme_protocol_message_free(&mqtt_msg);
                            acme_topic_info_free(&topic_info);
                            return -1;
                        }
                    }
                }
            }
        }
    }

    // 权限验证通过，处理消息
    ret = process_authenticated_message(plugin, &mqtt_msg, &topic_info);

    acme_protocol_message_free(&mqtt_msg);
    acme_topic_info_free(&topic_info);

    return ret;
}

// 记录安全事件
static void log_security_event(neu_plugin_t *plugin,
                              const char *event_type,
                              const char *device_id,
                              const char *resource)
{
    cJSON *event = cJSON_CreateObject();
    cJSON_AddStringToObject(event, "event_type", event_type);
    cJSON_AddStringToObject(event, "device_id", device_id);
    cJSON_AddStringToObject(event, "resource", resource);
    cJSON_AddNumberToObject(event, "timestamp", time(NULL) * 1000);

    char *event_json = cJSON_Print(event);
    if (event_json) {
        plog_warn(plugin, "Security Event: %s", event_json);
        free(event_json);
    }

    cJSON_Delete(event);
}
```

## 🔗 Neuron架构映射实现

### 1. 映射管理器实现

```c
// neuron_mapping_manager.h
#ifndef ACME_NEURON_MAPPING_MANAGER_H
#define ACME_NEURON_MAPPING_MANAGER_H

#include "neuron.h"
#include "thing_model_manager.h"

// 标签映射结构
typedef struct acme_tag_mapping {
    char *property_id;          // 物模型属性ID
    char *neuron_node;          // Neuron节点名
    char *neuron_group;         // Neuron组名
    char *neuron_tag;           // Neuron标签名
    char *tag_address;          // 标签地址
    neu_datatag_type_e tag_type; // 标签类型
    neu_tag_attribute_e tag_attribute; // 标签属性

    // 数据转换配置
    double scale_factor;        // 缩放因子
    double offset;              // 偏移量
    bool byte_swap;             // 字节序转换

    struct acme_tag_mapping *next;
} acme_tag_mapping_t;

// 设备映射结构
typedef struct acme_device_mapping {
    char *device_id;            // 设备ID
    char *thing_model_id;       // 物模型ID
    char *neuron_node;          // 对应的Neuron节点
    char *device_type;          // 设备类型

    acme_tag_mapping_t *tag_mappings; // 标签映射链表
    int mapping_count;          // 映射数量

    struct acme_device_mapping *next;
} acme_device_mapping_t;

// 映射管理器
typedef struct acme_mapping_manager {
    acme_device_mapping_t *device_mappings;
    int device_count;
} acme_mapping_manager_t;

// 函数声明
acme_mapping_manager_t *acme_mapping_manager_create(void);
void acme_mapping_manager_destroy(acme_mapping_manager_t *manager);

int acme_mapping_manager_load_config(acme_mapping_manager_t *manager,
                                    const char *config_json);

acme_tag_mapping_t *acme_mapping_manager_find_tag(acme_mapping_manager_t *manager,
                                                  const char *device_id,
                                                  const char *property_id);

char *acme_mapping_manager_find_property(acme_mapping_manager_t *manager,
                                        const char *device_id,
                                        const char *neuron_node,
                                        const char *neuron_group,
                                        const char *neuron_tag);

acme_device_mapping_t *acme_mapping_manager_get_device(acme_mapping_manager_t *manager,
                                                      const char *device_id);

#endif // ACME_NEURON_MAPPING_MANAGER_H
```

### 2. 映射管理器核心实现

```c
// neuron_mapping_manager.c
#include "neuron_mapping_manager.h"
#include <stdlib.h>
#include <string.h>

// 创建映射管理器
acme_mapping_manager_t *acme_mapping_manager_create(void)
{
    acme_mapping_manager_t *manager = calloc(1, sizeof(acme_mapping_manager_t));
    return manager;
}

// 加载映射配置
int acme_mapping_manager_load_config(acme_mapping_manager_t *manager,
                                    const char *config_json)
{
    if (!manager || !config_json) return -1;

    cJSON *root = cJSON_Parse(config_json);
    if (!root) return -1;

    cJSON *neuron_mapping = cJSON_GetObjectItem(root, "neuron_mapping");
    if (!neuron_mapping) {
        cJSON_Delete(root);
        return -1;
    }

    // 解析设备实例信息
    cJSON *device_instance = cJSON_GetObjectItem(neuron_mapping, "device_instance");
    if (!device_instance) {
        cJSON_Delete(root);
        return -1;
    }

    acme_device_mapping_t *device_mapping = calloc(1, sizeof(acme_device_mapping_t));
    if (!device_mapping) {
        cJSON_Delete(root);
        return -1;
    }

    // 解析设备基本信息
    cJSON *device_id = cJSON_GetObjectItem(device_instance, "device_id");
    if (device_id && cJSON_IsString(device_id)) {
        device_mapping->device_id = strdup(device_id->valuestring);
    }

    cJSON *thing_model_id = cJSON_GetObjectItem(device_instance, "thing_model_id");
    if (thing_model_id && cJSON_IsString(thing_model_id)) {
        device_mapping->thing_model_id = strdup(thing_model_id->valuestring);
    }

    cJSON *neuron_node = cJSON_GetObjectItem(device_instance, "neuron_node");
    if (neuron_node && cJSON_IsString(neuron_node)) {
        device_mapping->neuron_node = strdup(neuron_node->valuestring);
    }

    // 解析属性组映射
    cJSON *property_groups = cJSON_GetObjectItem(neuron_mapping, "property_groups");
    if (property_groups && cJSON_IsArray(property_groups)) {
        cJSON *group = NULL;
        cJSON_ArrayForEach(group, property_groups) {
            parse_property_group(device_mapping, group);
        }
    }

    // 添加到管理器
    device_mapping->next = manager->device_mappings;
    manager->device_mappings = device_mapping;
    manager->device_count++;

    cJSON_Delete(root);
    return 0;
}

// 解析属性组
static int parse_property_group(acme_device_mapping_t *device_mapping, cJSON *group)
{
    if (!device_mapping || !group) return -1;

    cJSON *neuron_group = cJSON_GetObjectItem(group, "neuron_group");
    if (!neuron_group || !cJSON_IsString(neuron_group)) return -1;

    cJSON *properties = cJSON_GetObjectItem(group, "properties");
    if (!properties || !cJSON_IsArray(properties)) return -1;

    cJSON *property = NULL;
    cJSON_ArrayForEach(property, properties) {
        acme_tag_mapping_t *tag_mapping = calloc(1, sizeof(acme_tag_mapping_t));
        if (!tag_mapping) continue;

        // 解析属性映射信息
        cJSON *property_id = cJSON_GetObjectItem(property, "property_id");
        if (property_id && cJSON_IsString(property_id)) {
            tag_mapping->property_id = strdup(property_id->valuestring);
        }

        cJSON *neuron_tag = cJSON_GetObjectItem(property, "neuron_tag");
        if (neuron_tag && cJSON_IsString(neuron_tag)) {
            tag_mapping->neuron_tag = strdup(neuron_tag->valuestring);
        }

        tag_mapping->neuron_node = strdup(device_mapping->neuron_node);
        tag_mapping->neuron_group = strdup(neuron_group->valuestring);

        cJSON *tag_address = cJSON_GetObjectItem(property, "tag_address");
        if (tag_address && cJSON_IsString(tag_address)) {
            tag_mapping->tag_address = strdup(tag_address->valuestring);
        }

        cJSON *tag_type = cJSON_GetObjectItem(property, "tag_type");
        if (tag_type && cJSON_IsString(tag_type)) {
            tag_mapping->tag_type = parse_tag_type(tag_type->valuestring);
        }

        cJSON *tag_attribute = cJSON_GetObjectItem(property, "tag_attribute");
        if (tag_attribute && cJSON_IsString(tag_attribute)) {
            tag_mapping->tag_attribute = parse_tag_attribute(tag_attribute->valuestring);
        }

        // 添加到设备映射
        tag_mapping->next = device_mapping->tag_mappings;
        device_mapping->tag_mappings = tag_mapping;
        device_mapping->mapping_count++;
    }

    return 0;
}

// 查找标签映射
acme_tag_mapping_t *acme_mapping_manager_find_tag(acme_mapping_manager_t *manager,
                                                  const char *device_id,
                                                  const char *property_id)
{
    if (!manager || !device_id || !property_id) return NULL;

    acme_device_mapping_t *device = manager->device_mappings;
    while (device) {
        if (device->device_id && strcmp(device->device_id, device_id) == 0) {
            acme_tag_mapping_t *tag = device->tag_mappings;
            while (tag) {
                if (tag->property_id && strcmp(tag->property_id, property_id) == 0) {
                    return tag;
                }
                tag = tag->next;
            }
            break;
        }
        device = device->next;
    }

    return NULL;
}

// 根据Neuron标签查找属性ID
char *acme_mapping_manager_find_property(acme_mapping_manager_t *manager,
                                        const char *device_id,
                                        const char *neuron_node,
                                        const char *neuron_group,
                                        const char *neuron_tag)
{
    if (!manager || !device_id || !neuron_node || !neuron_group || !neuron_tag) {
        return NULL;
    }

    acme_device_mapping_t *device = manager->device_mappings;
    while (device) {
        if (device->device_id && strcmp(device->device_id, device_id) == 0) {
            acme_tag_mapping_t *tag = device->tag_mappings;
            while (tag) {
                if (tag->neuron_node && strcmp(tag->neuron_node, neuron_node) == 0 &&
                    tag->neuron_group && strcmp(tag->neuron_group, neuron_group) == 0 &&
                    tag->neuron_tag && strcmp(tag->neuron_tag, neuron_tag) == 0) {
                    return strdup(tag->property_id);
                }
                tag = tag->next;
            }
            break;
        }
        device = device->next;
    }

    return NULL;
}

// 解析标签类型
static neu_datatag_type_e parse_tag_type(const char *type_str)
{
    if (strcmp(type_str, "BIT") == 0) return NEU_TYPE_BIT;
    if (strcmp(type_str, "UINT16") == 0) return NEU_TYPE_UINT16;
    if (strcmp(type_str, "INT16") == 0) return NEU_TYPE_INT16;
    if (strcmp(type_str, "UINT32") == 0) return NEU_TYPE_UINT32;
    if (strcmp(type_str, "INT32") == 0) return NEU_TYPE_INT32;
    if (strcmp(type_str, "FLOAT") == 0) return NEU_TYPE_FLOAT;
    if (strcmp(type_str, "DOUBLE") == 0) return NEU_TYPE_DOUBLE;
    if (strcmp(type_str, "STRING") == 0) return NEU_TYPE_STRING;
    return NEU_TYPE_INT32; // 默认类型
}

// 解析标签属性
static neu_tag_attribute_e parse_tag_attribute(const char *attr_str)
{
    if (strcmp(attr_str, "R") == 0) return NEU_ATTRIBUTE_READ;
    if (strcmp(attr_str, "W") == 0) return NEU_ATTRIBUTE_WRITE;
    if (strcmp(attr_str, "RW") == 0) return NEU_ATTRIBUTE_READ_WRITE;
    return NEU_ATTRIBUTE_READ; // 默认只读
}
```

### 3. 协议映射器增强实现

```c
// 增强的协议映射器，支持Neuron架构映射
// enhanced_protocol_mapper.c

// 属性读取请求映射（支持多组）
int acme_map_read_request_to_neuron_enhanced(const acme_protocol_message_t *mqtt_msg,
                                            acme_thing_model_t *model,
                                            acme_mapping_manager_t *mapping_manager,
                                            neu_reqresp_head_t **neu_heads,
                                            void ***neu_data,
                                            int *request_count)
{
    if (!mqtt_msg || !model || !mapping_manager || !neu_heads || !neu_data || !request_count) {
        return -1;
    }

    cJSON *data = mqtt_msg->data;
    if (!data) return -1;

    cJSON *properties = cJSON_GetObjectItem(data, "properties");
    if (!properties || !cJSON_IsArray(properties)) return -1;

    // 按组分类属性
    typedef struct group_properties {
        char *node_name;
        char *group_name;
        char **property_list;
        int property_count;
        struct group_properties *next;
    } group_properties_t;

    group_properties_t *groups = NULL;

    // 遍历属性，按组分类
    cJSON *prop = NULL;
    cJSON_ArrayForEach(prop, properties) {
        if (!cJSON_IsString(prop)) continue;

        acme_tag_mapping_t *tag_mapping = acme_mapping_manager_find_tag(
            mapping_manager, mqtt_msg->target, prop->valuestring);

        if (!tag_mapping) continue;

        // 查找或创建组
        group_properties_t *group = find_or_create_group(&groups,
                                                        tag_mapping->neuron_node,
                                                        tag_mapping->neuron_group);
        if (group) {
            add_property_to_group(group, prop->valuestring);
        }
    }

    // 为每个组创建Neuron请求
    int group_count = count_groups(groups);
    *neu_heads = calloc(group_count, sizeof(neu_reqresp_head_t));
    *neu_data = calloc(group_count, sizeof(void*));
    *request_count = group_count;

    group_properties_t *current_group = groups;
    int index = 0;

    while (current_group && index < group_count) {
        neu_req_read_group_t *read_req = calloc(1, sizeof(neu_req_read_group_t));
        if (!read_req) continue;

        strncpy(read_req->driver, current_group->node_name, sizeof(read_req->driver) - 1);
        strncpy(read_req->group, current_group->group_name, sizeof(read_req->group) - 1);

        (*neu_heads)[index].type = NEU_REQ_READ_GROUP;
        (*neu_heads)[index].ctx = read_req;
        (*neu_data)[index] = read_req;

        index++;
        current_group = current_group->next;
    }

    // 清理临时数据
    free_group_properties(groups);

    return 0;
}

// 属性写入请求映射（支持多组）
int acme_map_write_request_to_neuron_enhanced(const acme_protocol_message_t *mqtt_msg,
                                             acme_thing_model_t *model,
                                             acme_mapping_manager_t *mapping_manager,
                                             neu_reqresp_head_t **neu_heads,
                                             void ***neu_data,
                                             int *request_count)
{
    if (!mqtt_msg || !model || !mapping_manager || !neu_heads || !neu_data || !request_count) {
        return -1;
    }

    cJSON *data = mqtt_msg->data;
    if (!data) return -1;

    cJSON *properties = cJSON_GetObjectItem(data, "properties");
    if (!properties || !cJSON_IsArray(properties)) return -1;

    // 按组分类写入操作
    typedef struct write_group {
        char *node_name;
        char *group_name;
        neu_datatag_t *tags;
        int tag_count;
        struct write_group *next;
    } write_group_t;

    write_group_t *write_groups = NULL;

    // 遍历要写入的属性
    cJSON *prop = NULL;
    cJSON_ArrayForEach(prop, properties) {
        if (!cJSON_IsObject(prop)) continue;

        cJSON *identifier = cJSON_GetObjectItem(prop, "identifier");
        cJSON *value = cJSON_GetObjectItem(prop, "value");

        if (!identifier || !cJSON_IsString(identifier) || !value) continue;

        acme_tag_mapping_t *tag_mapping = acme_mapping_manager_find_tag(
            mapping_manager, mqtt_msg->target, identifier->valuestring);

        if (!tag_mapping) continue;

        // 查找或创建写入组
        write_group_t *group = find_or_create_write_group(&write_groups,
                                                         tag_mapping->neuron_node,
                                                         tag_mapping->neuron_group);
        if (group) {
            add_write_tag_to_group(group, tag_mapping, value);
        }
    }

    // 为每个组创建Neuron写入请求
    int group_count = count_write_groups(write_groups);
    *neu_heads = calloc(group_count, sizeof(neu_reqresp_head_t));
    *neu_data = calloc(group_count, sizeof(void*));
    *request_count = group_count;

    write_group_t *current_group = write_groups;
    int index = 0;

    while (current_group && index < group_count) {
        neu_req_write_tags_t *write_req = calloc(1, sizeof(neu_req_write_tags_t));
        if (!write_req) continue;

        strncpy(write_req->driver, current_group->node_name, sizeof(write_req->driver) - 1);
        strncpy(write_req->group, current_group->group_name, sizeof(write_req->group) - 1);
        write_req->n_tag = current_group->tag_count;
        write_req->tags = current_group->tags;

        (*neu_heads)[index].type = NEU_REQ_WRITE_TAGS;
        (*neu_heads)[index].ctx = write_req;
        (*neu_data)[index] = write_req;

        index++;
        current_group = current_group->next;
    }

    // 清理临时数据
    free_write_groups(write_groups);

    return 0;
}

// 数据转换函数
static neu_value_u convert_property_value(cJSON *value, neu_datatag_type_e tag_type,
                                         acme_tag_mapping_t *mapping)
{
    neu_value_u neu_value = {0};

    switch (tag_type) {
    case NEU_TYPE_BIT:
        neu_value.boolean = cJSON_IsTrue(value);
        break;
    case NEU_TYPE_UINT16:
        neu_value.u16 = (uint16_t)cJSON_GetNumberValue(value);
        break;
    case NEU_TYPE_INT16:
        neu_value.i16 = (int16_t)cJSON_GetNumberValue(value);
        break;
    case NEU_TYPE_UINT32:
        neu_value.u32 = (uint32_t)cJSON_GetNumberValue(value);
        break;
    case NEU_TYPE_INT32:
        neu_value.i32 = (int32_t)cJSON_GetNumberValue(value);
        break;
    case NEU_TYPE_FLOAT:
        neu_value.f32 = (float)cJSON_GetNumberValue(value);
        // 应用缩放因子和偏移量
        if (mapping) {
            neu_value.f32 = neu_value.f32 * mapping->scale_factor + mapping->offset;
        }
        break;
    case NEU_TYPE_DOUBLE:
        neu_value.d64 = cJSON_GetNumberValue(value);
        if (mapping) {
            neu_value.d64 = neu_value.d64 * mapping->scale_factor + mapping->offset;
        }
        break;
    case NEU_TYPE_STRING:
        if (cJSON_IsString(value)) {
            strncpy(neu_value.str, value->valuestring, sizeof(neu_value.str) - 1);
        }
        break;
    default:
        break;
    }

    return neu_value;
}
```

## 🔍 动态发现和自动映射实现

### 1. 设备发现管理器

```c
// device_discovery_manager.h
#ifndef ACME_DEVICE_DISCOVERY_MANAGER_H
#define ACME_DEVICE_DISCOVERY_MANAGER_H

#include "neuron.h"
#include "thing_model_manager.h"
#include "neuron_mapping_manager.h"

// 发现方法枚举
typedef enum {
    ACME_DISCOVERY_NEURON_SCAN = 0,
    ACME_DISCOVERY_LORA_REGISTRATION,
    ACME_DISCOVERY_MANUAL_CONFIG
} acme_discovery_method_e;

// 设备发现信息
typedef struct acme_discovered_device {
    char *device_id;
    char *device_type;
    char *device_eui;
    char *neuron_node;
    acme_discovery_method_e discovery_method;
    float confidence;
    time_t discovered_time;

    // 发现的属性信息
    char **discovered_properties;
    int property_count;

    struct acme_discovered_device *next;
} acme_discovered_device_t;

// 映射模板
typedef struct acme_mapping_template {
    char *device_type;
    char *node_template;
    char *group_template;

    // 属性映射模板
    struct {
        char *property_id;
        char *address_template;
        char *type;
        char *attribute;
    } *property_templates;
    int template_count;

    struct acme_mapping_template *next;
} acme_mapping_template_t;

// 发现管理器
typedef struct acme_discovery_manager {
    acme_discovered_device_t *discovered_devices;
    acme_mapping_template_t *mapping_templates;

    // 配置
    bool auto_discovery_enabled;
    int scan_interval;
    bool auto_create_mapping;

    // 统计信息
    int total_discovered;
    int auto_mapped_count;
    int manual_mapped_count;
} acme_discovery_manager_t;

// 函数声明
acme_discovery_manager_t *acme_discovery_manager_create(void);
void acme_discovery_manager_destroy(acme_discovery_manager_t *manager);

int acme_discovery_manager_load_templates(acme_discovery_manager_t *manager,
                                         const char *template_json);

int acme_discovery_manager_scan_neuron_nodes(acme_discovery_manager_t *manager,
                                            neu_plugin_t *plugin);

acme_discovered_device_t *acme_discovery_manager_detect_device_type(
    acme_discovery_manager_t *manager,
    const char *device_id,
    const char *device_eui,
    const char **capabilities,
    int capability_count);

int acme_discovery_manager_auto_create_mapping(acme_discovery_manager_t *manager,
                                              acme_discovered_device_t *device,
                                              acme_mapping_manager_t *mapping_manager);

#endif // ACME_DEVICE_DISCOVERY_MANAGER_H
```

### 2. 自动发现核心实现

```c
// device_discovery_manager.c
#include "device_discovery_manager.h"
#include <regex.h>

// 创建发现管理器
acme_discovery_manager_t *acme_discovery_manager_create(void)
{
    acme_discovery_manager_t *manager = calloc(1, sizeof(acme_discovery_manager_t));
    if (!manager) return NULL;

    manager->auto_discovery_enabled = true;
    manager->scan_interval = 30000; // 30秒
    manager->auto_create_mapping = true;

    return manager;
}

// 扫描Neuron节点发现设备
int acme_discovery_manager_scan_neuron_nodes(acme_discovery_manager_t *manager,
                                            neu_plugin_t *plugin)
{
    if (!manager || !plugin) return -1;

    // 获取所有Neuron节点
    neu_req_get_nodes_t req = {0};
    neu_resp_get_nodes_t *resp = NULL;

    int ret = neu_plugin_op(plugin, NEU_REQ_GET_NODES, &req, &resp);
    if (ret != 0 || !resp) return -1;

    // 遍历节点，查找设备节点
    for (int i = 0; i < resp->n_node; i++) {
        neu_resp_node_info_t *node_info = &resp->nodes[i];

        // 检查节点名是否符合设备节点模式
        if (is_device_node(node_info->node)) {
            acme_discovered_device_t *device = create_discovered_device_from_node(
                manager, node_info);

            if (device) {
                // 添加到发现列表
                device->next = manager->discovered_devices;
                manager->discovered_devices = device;
                manager->total_discovered++;

                plog_info(plugin, "Discovered device: %s (node: %s)",
                         device->device_id, device->neuron_node);
            }
        }
    }

    // 清理响应
    neu_resp_free(resp);

    return 0;
}

// 检查是否为设备节点
static bool is_device_node(const char *node_name)
{
    if (!node_name) return false;

    // 使用正则表达式匹配设备节点模式: FCM_001_node, ECM_002_node等
    regex_t regex;
    int ret = regcomp(&regex, "^[A-Z]{3}_[0-9]{3}_node$", REG_EXTENDED);
    if (ret != 0) return false;

    ret = regexec(&regex, node_name, 0, NULL, 0);
    regfree(&regex);

    return (ret == 0);
}

// 从节点信息创建发现的设备
static acme_discovered_device_t *create_discovered_device_from_node(
    acme_discovery_manager_t *manager,
    neu_resp_node_info_t *node_info)
{
    if (!manager || !node_info) return NULL;

    acme_discovered_device_t *device = calloc(1, sizeof(acme_discovered_device_t));
    if (!device) return NULL;

    // 从节点名提取设备ID: FCM_001_node -> FCM_001
    char *device_id = extract_device_id_from_node(node_info->node);
    if (!device_id) {
        free(device);
        return NULL;
    }

    device->device_id = device_id;
    device->neuron_node = strdup(node_info->node);
    device->discovery_method = ACME_DISCOVERY_NEURON_SCAN;
    device->discovered_time = time(NULL);

    // 从设备ID推断设备类型
    if (strncmp(device_id, "FCM_", 4) == 0) {
        device->device_type = strdup("FCM");
        device->confidence = 0.95;
    } else if (strncmp(device_id, "ECM_", 4) == 0) {
        device->device_type = strdup("ECM");
        device->confidence = 0.90;
    } else {
        device->device_type = strdup("UNKNOWN");
        device->confidence = 0.50;
    }

    // 发现节点的属性（标签）
    discover_node_properties(device, node_info->node);

    return device;
}

// 发现节点的属性
static int discover_node_properties(acme_discovered_device_t *device,
                                   const char *node_name)
{
    if (!device || !node_name) return -1;

    // 这里应该调用Neuron API获取节点的所有标签
    // 简化实现，基于设备类型预设属性
    if (strcmp(device->device_type, "FCM") == 0) {
        const char *fcm_properties[] = {"ONOFF", "STEMP", "RTEMP", "SMODE"};
        int prop_count = sizeof(fcm_properties) / sizeof(fcm_properties[0]);

        device->discovered_properties = calloc(prop_count, sizeof(char*));
        for (int i = 0; i < prop_count; i++) {
            device->discovered_properties[i] = strdup(fcm_properties[i]);
        }
        device->property_count = prop_count;
    } else if (strcmp(device->device_type, "ECM") == 0) {
        const char *ecm_properties[] = {"TEMP", "HUMI", "CO2"};
        int prop_count = sizeof(ecm_properties) / sizeof(ecm_properties[0]);

        device->discovered_properties = calloc(prop_count, sizeof(char*));
        for (int i = 0; i < prop_count; i++) {
            device->discovered_properties[i] = strdup(ecm_properties[i]);
        }
        device->property_count = prop_count;
    }

    return 0;
}

// 自动创建映射
int acme_discovery_manager_auto_create_mapping(acme_discovery_manager_t *manager,
                                              acme_discovered_device_t *device,
                                              acme_mapping_manager_t *mapping_manager)
{
    if (!manager || !device || !mapping_manager) return -1;

    // 查找对应的映射模板
    acme_mapping_template_t *template = find_mapping_template(manager, device->device_type);
    if (!template) {
        // 使用通用模板
        template = create_generic_template(device);
        if (!template) return -1;
    }

    // 生成映射配置JSON
    cJSON *mapping_config = create_mapping_config_from_template(device, template);
    if (!mapping_config) return -1;

    char *config_json = cJSON_Print(mapping_config);
    if (!config_json) {
        cJSON_Delete(mapping_config);
        return -1;
    }

    // 加载到映射管理器
    int ret = acme_mapping_manager_load_config(mapping_manager, config_json);

    free(config_json);
    cJSON_Delete(mapping_config);

    if (ret == 0) {
        manager->auto_mapped_count++;
    }

    return ret;
}

// 创建映射配置
static cJSON *create_mapping_config_from_template(acme_discovered_device_t *device,
                                                 acme_mapping_template_t *template)
{
    if (!device || !template) return NULL;

    cJSON *root = cJSON_CreateObject();
    cJSON *neuron_mapping = cJSON_CreateObject();
    cJSON_AddItemToObject(root, "neuron_mapping", neuron_mapping);

    // 设备实例信息
    cJSON *device_instance = cJSON_CreateObject();
    cJSON_AddStringToObject(device_instance, "device_id", device->device_id);
    cJSON_AddStringToObject(device_instance, "neuron_node", device->neuron_node);

    // 推断物模型ID
    char thing_model_id[128];
    snprintf(thing_model_id, sizeof(thing_model_id), "acme.device.%s.v1",
             device->device_type);
    cJSON_AddStringToObject(device_instance, "thing_model_id", thing_model_id);

    cJSON_AddItemToObject(neuron_mapping, "device_instance", device_instance);

    // 属性组配置
    cJSON *property_groups = cJSON_CreateArray();
    cJSON *group = cJSON_CreateObject();

    cJSON_AddStringToObject(group, "group_id", "default_group");
    cJSON_AddStringToObject(group, "neuron_group", "default_group");
    cJSON_AddNumberToObject(group, "read_interval", 1000);

    // 属性映射
    cJSON *properties = cJSON_CreateArray();

    for (int i = 0; i < device->property_count; i++) {
        const char *prop_id = device->discovered_properties[i];

        // 查找模板中的属性配置
        for (int j = 0; j < template->template_count; j++) {
            if (strcmp(template->property_templates[j].property_id, prop_id) == 0) {
                cJSON *property = cJSON_CreateObject();
                cJSON_AddStringToObject(property, "property_id", prop_id);
                cJSON_AddStringToObject(property, "neuron_tag", prop_id);

                // 生成地址
                char address[16];
                snprintf(address, sizeof(address), "0x%02X", i + 1);
                cJSON_AddStringToObject(property, "tag_address", address);

                cJSON_AddStringToObject(property, "tag_type",
                                       template->property_templates[j].type);
                cJSON_AddStringToObject(property, "tag_attribute",
                                       template->property_templates[j].attribute);

                cJSON_AddItemToArray(properties, property);
                break;
            }
        }
    }

    cJSON_AddItemToObject(group, "properties", properties);
    cJSON_AddItemToArray(property_groups, group);
    cJSON_AddItemToObject(neuron_mapping, "property_groups", property_groups);

    return root;
}
```

### 3. 学习型优化实现

```c
// mapping_optimizer.h
#ifndef ACME_MAPPING_OPTIMIZER_H
#define ACME_MAPPING_OPTIMIZER_H

#include "neuron_mapping_manager.h"

// 使用统计
typedef struct acme_usage_stats {
    char *device_id;
    char *property_id;
    int access_count;
    double avg_response_time;
    int error_count;
    time_t last_access;
} acme_usage_stats_t;

// 优化建议
typedef struct acme_optimization_suggestion {
    char *suggestion_type;
    char *description;
    char *target_device;
    char *target_property;
    double expected_improvement;

    struct acme_optimization_suggestion *next;
} acme_optimization_suggestion_t;

// 映射优化器
typedef struct acme_mapping_optimizer {
    acme_usage_stats_t *usage_stats;
    int stats_count;

    acme_optimization_suggestion_t *suggestions;

    // 学习配置
    bool learning_enabled;
    int learning_interval;
    int min_data_points;
} acme_mapping_optimizer_t;

// 函数声明
acme_mapping_optimizer_t *acme_mapping_optimizer_create(void);
void acme_mapping_optimizer_destroy(acme_mapping_optimizer_t *optimizer);

int acme_mapping_optimizer_record_access(acme_mapping_optimizer_t *optimizer,
                                        const char *device_id,
                                        const char *property_id,
                                        double response_time,
                                        bool success);

int acme_mapping_optimizer_analyze_patterns(acme_mapping_optimizer_t *optimizer,
                                           acme_mapping_manager_t *mapping_manager);

acme_optimization_suggestion_t *acme_mapping_optimizer_get_suggestions(
    acme_mapping_optimizer_t *optimizer);

int acme_mapping_optimizer_apply_suggestion(acme_mapping_optimizer_t *optimizer,
                                           acme_optimization_suggestion_t *suggestion,
                                           acme_mapping_manager_t *mapping_manager);

#endif // ACME_MAPPING_OPTIMIZER_H
```

### 4. 集成到MQTT插件

```c
// 在acme_mqtt_plugin.c中集成动态发现功能
struct neu_plugin {
    neu_plugin_common_t common;

    // 原有字段...
    neu_conn_t *conn;
    mqtt_config_t *config;
    acme_thing_model_manager_t *model_manager;
    acme_security_manager_t *security_manager;
    acme_mapping_manager_t *mapping_manager;

    // 新增动态发现相关字段
    acme_discovery_manager_t *discovery_manager;
    acme_mapping_optimizer_t *optimizer;
    neu_event_timer_t *discovery_timer;
    bool auto_discovery_enabled;
};

// 初始化动态发现功能
static int mqtt_plugin_discovery_init(neu_plugin_t *plugin)
{
    // 创建发现管理器
    plugin->discovery_manager = acme_discovery_manager_create();
    if (!plugin->discovery_manager) {
        plog_error(plugin, "Failed to create discovery manager");
        return -1;
    }

    // 创建优化器
    plugin->optimizer = acme_mapping_optimizer_create();
    if (!plugin->optimizer) {
        plog_error(plugin, "Failed to create mapping optimizer");
        return -1;
    }

    // 加载映射模板
    const char *template_config = load_mapping_templates();
    if (template_config) {
        acme_discovery_manager_load_templates(plugin->discovery_manager, template_config);
    }

    // 启动定时发现
    if (plugin->auto_discovery_enabled) {
        plugin->discovery_timer = neu_event_add_timer(plugin->common.events,
                                                     discovery_timer_callback,
                                                     plugin,
                                                     30000); // 30秒间隔
    }

    plog_info(plugin, "Dynamic discovery initialized");
    return 0;
}

// 发现定时器回调
static void discovery_timer_callback(neu_event_timer_t *timer, void *usr_data)
{
    neu_plugin_t *plugin = (neu_plugin_t *)usr_data;

    // 扫描Neuron节点
    int discovered = acme_discovery_manager_scan_neuron_nodes(plugin->discovery_manager, plugin);
    if (discovered > 0) {
        plog_info(plugin, "Discovered %d new devices", discovered);

        // 自动创建映射
        acme_discovered_device_t *device = plugin->discovery_manager->discovered_devices;
        while (device) {
            if (plugin->discovery_manager->auto_create_mapping) {
                int ret = acme_discovery_manager_auto_create_mapping(
                    plugin->discovery_manager, device, plugin->mapping_manager);

                if (ret == 0) {
                    plog_info(plugin, "Auto-created mapping for device: %s", device->device_id);

                    // 发送设备发现事件
                    send_device_discovered_event(plugin, device);
                }
            }
            device = device->next;
        }
    }

    // 运行优化分析
    acme_mapping_optimizer_analyze_patterns(plugin->optimizer, plugin->mapping_manager);
}

// 发送设备发现事件
static void send_device_discovered_event(neu_plugin_t *plugin,
                                        acme_discovered_device_t *device)
{
    cJSON *event = cJSON_CreateObject();
    cJSON_AddStringToObject(event, "event_type", "device_discovered");
    cJSON_AddStringToObject(event, "device_id", device->device_id);
    cJSON_AddStringToObject(event, "device_type", device->device_type);
    cJSON_AddNumberToObject(event, "confidence", device->confidence);
    cJSON_AddBoolToObject(event, "auto_mapped", true);
    cJSON_AddNumberToObject(event, "timestamp", time(NULL) * 1000);

    char *event_json = cJSON_Print(event);
    if (event_json) {
        // 发布到MQTT事件主题
        char topic[256];
        snprintf(topic, sizeof(topic), "/acme/%s/system/events",
                plugin->config->gateway_id);

        mqtt_publish(plugin->conn, topic, event_json, strlen(event_json), 0);
        free(event_json);
    }

    cJSON_Delete(event);
}
```

## 📋 物模型应用示例

### 1. 属性设置示例

#### FCM空调控制器温度设置
```c
// 属性设置完整流程示例
int fcm_set_temperature_example(neu_plugin_t *plugin, const char *device_id, float temperature)
{
    // 1. 获取设备的物模型
    acme_thing_model_t *model = acme_thing_model_manager_get(plugin->model_manager,
                                                            "acme.device.fcm.v1");
    if (!model) {
        plog_error(plugin, "Thing model not found for FCM device");
        return -1;
    }

    // 2. 验证属性值
    cJSON *temp_value = cJSON_CreateNumber(temperature);
    int ret = acme_thing_model_validate_property_value(model, "STEMP", temp_value);
    if (ret != 0) {
        plog_error(plugin, "Invalid temperature value: %.1f (valid range: 16.0-30.0)", temperature);
        cJSON_Delete(temp_value);
        return -1;
    }

    // 3. 查找属性映射
    acme_tag_mapping_t *tag_mapping = acme_mapping_manager_find_tag(plugin->mapping_manager,
                                                                   device_id, "STEMP");
    if (!tag_mapping) {
        plog_error(plugin, "No mapping found for property STEMP of device %s", device_id);
        cJSON_Delete(temp_value);
        return -1;
    }

    // 4. 创建Neuron写入请求
    neu_req_write_tags_t write_req = {0};
    strncpy(write_req.driver, tag_mapping->neuron_node, sizeof(write_req.driver) - 1);
    strncpy(write_req.group, tag_mapping->neuron_group, sizeof(write_req.group) - 1);
    write_req.n_tag = 1;
    write_req.tags = calloc(1, sizeof(neu_datatag_t));

    // 5. 填充标签数据
    strncpy(write_req.tags[0].name, tag_mapping->neuron_tag, sizeof(write_req.tags[0].name) - 1);
    strncpy(write_req.tags[0].address, tag_mapping->tag_address, sizeof(write_req.tags[0].address) - 1);
    write_req.tags[0].type = tag_mapping->tag_type;
    write_req.tags[0].attribute = tag_mapping->tag_attribute;
    write_req.tags[0].value.f32 = temperature;

    // 6. 执行写入操作
    neu_reqresp_head_t head = {0};
    head.type = NEU_REQ_WRITE_TAGS;
    head.ctx = &write_req;

    ret = neu_plugin_op(plugin, NEU_REQ_WRITE_TAGS, &write_req, NULL);
    if (ret != 0) {
        plog_error(plugin, "Failed to write temperature to device %s", device_id);
        free(write_req.tags);
        cJSON_Delete(temp_value);
        return -1;
    }

    // 7. 记录操作统计
    acme_mapping_optimizer_record_access(plugin->optimizer, device_id, "STEMP",
                                        get_current_time_ms() - start_time, true);

    // 8. 发送MQTT响应
    send_property_write_response(plugin, device_id, "STEMP", temperature, "success");

    free(write_req.tags);
    cJSON_Delete(temp_value);

    plog_info(plugin, "Successfully set temperature %.1f for device %s", temperature, device_id);
    return 0;
}

// 发送属性写入响应
static void send_property_write_response(neu_plugin_t *plugin, const char *device_id,
                                       const char *property_id, float value, const char *result)
{
    cJSON *response = cJSON_CreateObject();
    cJSON_AddStringToObject(response, "version", "1.0");
    cJSON_AddNumberToObject(response, "timestamp", time(NULL) * 1000);
    cJSON_AddStringToObject(response, "message_type", "write_response");
    cJSON_AddStringToObject(response, "source", "gateway");
    cJSON_AddStringToObject(response, "target", "cloud");

    cJSON *data = cJSON_CreateObject();
    cJSON_AddStringToObject(data, "result", result);

    cJSON *properties = cJSON_CreateArray();
    cJSON *prop = cJSON_CreateObject();
    cJSON_AddStringToObject(prop, "identifier", property_id);
    cJSON_AddNumberToObject(prop, "value", value);
    cJSON_AddStringToObject(prop, "status", result);
    cJSON_AddItemToArray(properties, prop);

    cJSON_AddItemToObject(data, "properties", properties);
    cJSON_AddItemToObject(response, "data", data);

    char *response_json = cJSON_Print(response);
    if (response_json) {
        char topic[256];
        snprintf(topic, sizeof(topic), "/acme/%s/%s/write/response",
                plugin->config->gateway_id, device_id);

        mqtt_publish(plugin->conn, topic, response_json, strlen(response_json), 1);
        free(response_json);
    }

    cJSON_Delete(response);
}
```

### 2. 服务调用示例

#### FCM空调控制器电源控制服务
```c
// 服务调用完整流程示例
int fcm_power_control_service_example(neu_plugin_t *plugin, const char *device_id,
                                     cJSON *input_params)
{
    // 1. 获取物模型和服务定义
    acme_thing_model_t *model = acme_thing_model_manager_get(plugin->model_manager,
                                                            "acme.device.fcm.v1");
    if (!model) {
        return send_service_error_response(plugin, device_id, "power_control",
                                         "Thing model not found");
    }

    acme_service_t *service = acme_thing_model_get_service(model, "power_control");
    if (!service) {
        return send_service_error_response(plugin, device_id, "power_control",
                                         "Service not found");
    }

    // 2. 验证输入参数
    int ret = acme_thing_model_validate_service_input(model, "power_control", input_params);
    if (ret != 0) {
        return send_service_error_response(plugin, device_id, "power_control",
                                         "Invalid input parameters");
    }

    // 3. 提取服务参数
    cJSON *power_param = cJSON_GetObjectItem(input_params, "power");
    if (!power_param || !cJSON_IsBool(power_param)) {
        return send_service_error_response(plugin, device_id, "power_control",
                                         "Missing or invalid power parameter");
    }

    bool power_on = cJSON_IsTrue(power_param);
    int onoff_value = power_on ? 1 : 0;

    // 4. 执行服务逻辑 - 设置ONOFF属性
    ret = set_device_property(plugin, device_id, "ONOFF", cJSON_CreateNumber(onoff_value));
    if (ret != 0) {
        return send_service_error_response(plugin, device_id, "power_control",
                                         "Failed to set power state");
    }

    // 5. 等待操作完成并读取确认
    usleep(100000); // 等待100ms

    cJSON *current_value = get_device_property(plugin, device_id, "ONOFF");
    if (!current_value) {
        return send_service_error_response(plugin, device_id, "power_control",
                                         "Failed to read current power state");
    }

    // 6. 验证操作结果
    int actual_value = (int)cJSON_GetNumberValue(current_value);
    bool operation_success = (actual_value == onoff_value);

    // 7. 构造服务响应
    cJSON *output_params = cJSON_CreateObject();
    cJSON_AddBoolToObject(output_params, "success", operation_success);
    cJSON_AddBoolToObject(output_params, "current_power_state", actual_value == 1);
    cJSON_AddStringToObject(output_params, "message",
                           operation_success ? "Power control successful" : "Power control failed");

    // 8. 发送服务响应
    send_service_response(plugin, device_id, "power_control", output_params,
                         operation_success ? "success" : "failure");

    cJSON_Delete(current_value);
    cJSON_Delete(output_params);

    plog_info(plugin, "Power control service executed for device %s: %s",
             device_id, power_on ? "ON" : "OFF");

    return operation_success ? 0 : -1;
}

// 发送服务响应
static void send_service_response(neu_plugin_t *plugin, const char *device_id,
                                 const char *service_id, cJSON *output_params,
                                 const char *result)
{
    cJSON *response = cJSON_CreateObject();
    cJSON_AddStringToObject(response, "version", "1.0");
    cJSON_AddNumberToObject(response, "timestamp", time(NULL) * 1000);
    cJSON_AddStringToObject(response, "message_type", "service_response");
    cJSON_AddStringToObject(response, "source", "gateway");
    cJSON_AddStringToObject(response, "target", "cloud");

    cJSON *data = cJSON_CreateObject();
    cJSON_AddStringToObject(data, "service_id", service_id);
    cJSON_AddStringToObject(data, "result", result);

    if (output_params) {
        cJSON_AddItemToObject(data, "output_params", cJSON_Duplicate(output_params, 1));
    }

    cJSON_AddItemToObject(response, "data", data);

    char *response_json = cJSON_Print(response);
    if (response_json) {
        char topic[256];
        snprintf(topic, sizeof(topic), "/acme/%s/%s/service/response",
                plugin->config->gateway_id, device_id);

        mqtt_publish(plugin->conn, topic, response_json, strlen(response_json), 1);
        free(response_json);
    }

    cJSON_Delete(response);
}
```

### 3. 事件通知示例

#### ECM环境监测器温度报警事件
```c
// 事件通知完整流程示例
int ecm_temperature_alarm_event_example(neu_plugin_t *plugin, const char *device_id,
                                       float current_temp, float threshold)
{
    // 1. 获取物模型和事件定义
    acme_thing_model_t *model = acme_thing_model_manager_get(plugin->model_manager,
                                                            "acme.device.ecm.v1");
    if (!model) {
        plog_error(plugin, "Thing model not found for ECM device");
        return -1;
    }

    acme_event_t *event = acme_thing_model_get_event(model, "temperature_alarm");
    if (!event) {
        plog_error(plugin, "Event definition not found: temperature_alarm");
        return -1;
    }

    // 2. 判断报警类型和严重程度
    acme_event_type_e event_type;
    const char *alarm_type;
    int severity_level;

    if (current_temp > threshold) {
        event_type = ACME_EVENT_WARNING;
        alarm_type = "high_temperature";
        severity_level = (current_temp > threshold + 5.0) ? 3 : 2;
    } else {
        event_type = ACME_EVENT_WARNING;
        alarm_type = "low_temperature";
        severity_level = (current_temp < threshold - 5.0) ? 3 : 2;
    }

    // 3. 构造事件输出参数
    cJSON *output_params = cJSON_CreateObject();
    cJSON_AddNumberToObject(output_params, "current_temperature", current_temp);
    cJSON_AddNumberToObject(output_params, "threshold_value", threshold);
    cJSON_AddStringToObject(output_params, "alarm_type", alarm_type);
    cJSON_AddNumberToObject(output_params, "severity_level", severity_level);
    cJSON_AddStringToObject(output_params, "device_location", "Building A - Floor 2");
    cJSON_AddNumberToObject(output_params, "event_timestamp", time(NULL) * 1000);

    // 4. 创建事件数据
    cJSON *event_data = acme_thing_model_create_event_data(model, "temperature_alarm",
                                                          output_params);
    if (!event_data) {
        plog_error(plugin, "Failed to create event data");
        cJSON_Delete(output_params);
        return -1;
    }

    // 5. 发送事件通知
    int ret = send_event_notification(plugin, device_id, event_data);
    if (ret != 0) {
        plog_error(plugin, "Failed to send event notification");
        cJSON_Delete(event_data);
        cJSON_Delete(output_params);
        return -1;
    }

    // 6. 记录事件到本地日志
    log_event_to_file(plugin, device_id, "temperature_alarm", current_temp, threshold);

    // 7. 更新设备状态
    update_device_alarm_status(plugin, device_id, "temperature_alarm", true);

    cJSON_Delete(event_data);
    cJSON_Delete(output_params);

    plog_warn(plugin, "Temperature alarm triggered for device %s: %.1f°C (threshold: %.1f°C)",
             device_id, current_temp, threshold);

    return 0;
}

// 发送事件通知
static int send_event_notification(neu_plugin_t *plugin, const char *device_id,
                                  cJSON *event_data)
{
    cJSON *notification = cJSON_CreateObject();
    cJSON_AddStringToObject(notification, "version", "1.0");
    cJSON_AddNumberToObject(notification, "timestamp", time(NULL) * 1000);
    cJSON_AddStringToObject(notification, "message_type", "event_notify");
    cJSON_AddStringToObject(notification, "source", "gateway");
    cJSON_AddStringToObject(notification, "target", "cloud");

    cJSON *data = cJSON_CreateObject();
    cJSON_AddItemToObject(data, "event", cJSON_Duplicate(event_data, 1));
    cJSON_AddItemToObject(notification, "data", data);

    char *notification_json = cJSON_Print(notification);
    if (!notification_json) {
        cJSON_Delete(notification);
        return -1;
    }

    // 发送到事件通知主题
    char topic[256];
    snprintf(topic, sizeof(topic), "/acme/%s/%s/event/notify",
            plugin->config->gateway_id, device_id);

    int ret = mqtt_publish(plugin->conn, topic, notification_json,
                          strlen(notification_json), 2); // QoS 2 确保事件可靠传输

    free(notification_json);
    cJSON_Delete(notification);

    return ret;
}

// 事件触发检查函数（在数据更新时调用）
void check_temperature_alarm_trigger(neu_plugin_t *plugin, const char *device_id,
                                    float temperature)
{
    // 获取设备的报警阈值配置
    float high_threshold = get_device_config_float(plugin, device_id, "temp_high_threshold", 30.0);
    float low_threshold = get_device_config_float(plugin, device_id, "temp_low_threshold", 10.0);

    // 检查是否触发高温报警
    if (temperature > high_threshold) {
        // 检查是否已经在报警状态，避免重复发送
        if (!is_device_in_alarm_state(plugin, device_id, "high_temperature")) {
            ecm_temperature_alarm_event_example(plugin, device_id, temperature, high_threshold);
        }
    }
    // 检查是否触发低温报警
    else if (temperature < low_threshold) {
        if (!is_device_in_alarm_state(plugin, device_id, "low_temperature")) {
            ecm_temperature_alarm_event_example(plugin, device_id, temperature, low_threshold);
        }
    }
    // 温度正常，清除报警状态
    else {
        clear_device_alarm_status(plugin, device_id, "temperature_alarm");
    }
}
```

## 📡 实际MQTT报文示例和处理流程

### 1. 属性设置的完整MQTT交互流程

#### 云端下发的属性设置请求报文
```json
// 主题: /acme/SPT_GW_001/FCM_001/write/request
{
    "version": "1.0",
    "timestamp": 1703123456789,
    "request_id": "write_req_20241201_001",
    "message_type": "write_request",
    "source": "cloud",
    "target": "FCM_001",
    "data": {
        "properties": [
            {
                "identifier": "STEMP",
                "value": 25.5,
                "description": "设定温度"
            },
            {
                "identifier": "ONOFF",
                "value": 1,
                "description": "开关状态"
            }
        ]
    }
}
```

#### 网关处理流程和响应报文
```c
// MQTT消息接收和处理函数
static void on_mqtt_message_received(struct mosquitto *mosq, void *userdata,
                                    const struct mosquitto_message *message)
{
    neu_plugin_t *plugin = (neu_plugin_t *)userdata;

    // 1. 解析主题信息
    acme_topic_info_t topic_info = {0};
    int ret = acme_topic_parse(message->topic, &topic_info);
    if (ret != 0) {
        plog_error(plugin, "Failed to parse topic: %s", message->topic);
        return;
    }

    // 2. 解析MQTT消息
    acme_protocol_message_t mqtt_msg = {0};
    ret = acme_protocol_message_parse((char*)message->payload, &mqtt_msg);
    if (ret != 0) {
        plog_error(plugin, "Failed to parse MQTT message");
        acme_topic_info_free(&topic_info);
        return;
    }

    // 3. 根据消息类型分发处理
    switch (mqtt_msg.message_type) {
    case ACME_MSG_WRITE_REQUEST:
        handle_property_write_request(plugin, &topic_info, &mqtt_msg);
        break;
    case ACME_MSG_SERVICE_REQUEST:
        handle_service_call_request(plugin, &topic_info, &mqtt_msg);
        break;
    case ACME_MSG_READ_REQUEST:
        handle_property_read_request(plugin, &topic_info, &mqtt_msg);
        break;
    default:
        plog_warn(plugin, "Unsupported message type: %d", mqtt_msg.message_type);
        break;
    }

    acme_protocol_message_free(&mqtt_msg);
    acme_topic_info_free(&topic_info);
}

// 处理属性写入请求
static void handle_property_write_request(neu_plugin_t *plugin,
                                         acme_topic_info_t *topic_info,
                                         acme_protocol_message_t *mqtt_msg)
{
    cJSON *data = mqtt_msg->data;
    if (!data) return;

    cJSON *properties = cJSON_GetObjectItem(data, "properties");
    if (!properties || !cJSON_IsArray(properties)) return;

    // 处理每个属性
    cJSON *property = NULL;
    cJSON_ArrayForEach(property, properties) {
        cJSON *identifier = cJSON_GetObjectItem(property, "identifier");
        cJSON *value = cJSON_GetObjectItem(property, "value");

        if (!identifier || !cJSON_IsString(identifier) || !value) continue;

        // 调用属性设置函数
        if (strcmp(identifier->valuestring, "STEMP") == 0) {
            float temp = (float)cJSON_GetNumberValue(value);
            fcm_set_temperature_example(plugin, topic_info->device_id, temp);
        } else if (strcmp(identifier->valuestring, "ONOFF") == 0) {
            int onoff = (int)cJSON_GetNumberValue(value);
            set_device_property(plugin, topic_info->device_id, "ONOFF",
                              cJSON_CreateNumber(onoff));
        }
    }
}
```

#### 网关返回的响应报文
```json
// 主题: /acme/SPT_GW_001/FCM_001/write/response
{
    "version": "1.0",
    "timestamp": 1703123457123,
    "request_id": "write_req_20241201_001",
    "message_type": "write_response",
    "source": "gateway",
    "target": "cloud",
    "data": {
        "result": "success",
        "properties": [
            {
                "identifier": "STEMP",
                "value": 25.5,
                "status": "success",
                "timestamp": 1703123457100
            },
            {
                "identifier": "ONOFF",
                "value": 1,
                "status": "success",
                "timestamp": 1703123457110
            }
        ]
    }
}
```

### 2. 服务调用的完整MQTT交互流程

#### 云端下发的服务调用请求报文
```json
// 主题: /acme/SPT_GW_001/FCM_001/service/request
{
    "version": "1.0",
    "timestamp": 1703123456789,
    "request_id": "service_req_20241201_002",
    "message_type": "service_request",
    "source": "cloud",
    "target": "FCM_001",
    "data": {
        "service_id": "power_control",
        "input_params": {
            "power": true,
            "mode": "auto",
            "delay_seconds": 0
        }
    }
}
```

#### 网关处理服务调用的完整流程
```c
// 处理服务调用请求
static void handle_service_call_request(neu_plugin_t *plugin,
                                       acme_topic_info_t *topic_info,
                                       acme_protocol_message_t *mqtt_msg)
{
    cJSON *data = mqtt_msg->data;
    if (!data) {
        send_service_error_response(plugin, topic_info->device_id, "unknown",
                                  "Missing service data");
        return;
    }

    cJSON *service_id = cJSON_GetObjectItem(data, "service_id");
    if (!service_id || !cJSON_IsString(service_id)) {
        send_service_error_response(plugin, topic_info->device_id, "unknown",
                                  "Missing service_id");
        return;
    }

    cJSON *input_params = cJSON_GetObjectItem(data, "input_params");
    if (!input_params) {
        send_service_error_response(plugin, topic_info->device_id, service_id->valuestring,
                                  "Missing input_params");
        return;
    }

    // 根据服务ID分发到具体的服务处理函数
    if (strcmp(service_id->valuestring, "power_control") == 0) {
        fcm_power_control_service_example(plugin, topic_info->device_id, input_params);
    } else if (strcmp(service_id->valuestring, "set_temperature") == 0) {
        fcm_set_temperature_service_example(plugin, topic_info->device_id, input_params);
    } else if (strcmp(service_id->valuestring, "mode_control") == 0) {
        fcm_mode_control_service_example(plugin, topic_info->device_id, input_params);
    } else {
        send_service_error_response(plugin, topic_info->device_id, service_id->valuestring,
                                  "Unsupported service");
    }
}

// 温度设置服务示例
static int fcm_set_temperature_service_example(neu_plugin_t *plugin, const char *device_id,
                                              cJSON *input_params)
{
    // 1. 验证输入参数
    cJSON *temperature = cJSON_GetObjectItem(input_params, "temperature");
    if (!temperature || !cJSON_IsNumber(temperature)) {
        return send_service_error_response(plugin, device_id, "set_temperature",
                                         "Invalid temperature parameter");
    }

    float temp_value = (float)cJSON_GetNumberValue(temperature);

    // 2. 验证温度范围
    if (temp_value < 16.0 || temp_value > 30.0) {
        return send_service_error_response(plugin, device_id, "set_temperature",
                                         "Temperature out of range (16.0-30.0)");
    }

    // 3. 执行温度设置
    int ret = fcm_set_temperature_example(plugin, device_id, temp_value);
    if (ret != 0) {
        return send_service_error_response(plugin, device_id, "set_temperature",
                                         "Failed to set temperature");
    }

    // 4. 读取确认当前温度
    cJSON *current_temp = get_device_property(plugin, device_id, "STEMP");
    float actual_temp = current_temp ? (float)cJSON_GetNumberValue(current_temp) : 0.0;

    // 5. 构造服务响应
    cJSON *output_params = cJSON_CreateObject();
    cJSON_AddBoolToObject(output_params, "success", fabs(actual_temp - temp_value) < 0.1);
    cJSON_AddNumberToObject(output_params, "target_temperature", temp_value);
    cJSON_AddNumberToObject(output_params, "actual_temperature", actual_temp);
    cJSON_AddStringToObject(output_params, "message", "Temperature set successfully");

    send_service_response(plugin, device_id, "set_temperature", output_params, "success");

    if (current_temp) cJSON_Delete(current_temp);
    cJSON_Delete(output_params);

    return 0;
}
```

#### 网关返回的服务响应报文
```json
// 主题: /acme/SPT_GW_001/FCM_001/service/response
{
    "version": "1.0",
    "timestamp": 1703123458234,
    "request_id": "service_req_20241201_002",
    "message_type": "service_response",
    "source": "gateway",
    "target": "cloud",
    "data": {
        "service_id": "power_control",
        "result": "success",
        "output_params": {
            "success": true,
            "current_power_state": true,
            "message": "Power control successful",
            "execution_time_ms": 156
        }
    }
}
```

#### set_temperature服务的完整MQTT交互流程

##### 云端下发的温度设置服务请求报文
```json
// 主题: /acme/SPT_GW_001/FCM_001/service/request
{
    "version": "1.0",
    "timestamp": 1703123460000,
    "request_id": "service_req_20241201_003",
    "message_type": "service_request",
    "source": "cloud",
    "target": "FCM_001",
    "data": {
        "service_id": "set_temperature",
        "input_params": {
            "temperature": 24.0,
            "mode": "auto",
            "fan_speed": "medium"
        }
    }
}
```

##### 网关处理set_temperature服务的完整流程
```c
// 处理温度设置服务的具体实现
static int fcm_set_temperature_service_handler(neu_plugin_t *plugin, const char *device_id,
                                              cJSON *input_params, const char *request_id)
{
    plog_info(plugin, "Processing set_temperature service for device: %s", device_id);

    // 1. 获取物模型验证服务参数
    acme_thing_model_t *model = acme_thing_model_manager_get(plugin->model_manager,
                                                            "acme.device.fcm.v1");
    if (!model) {
        return send_service_error_response(plugin, device_id, "set_temperature",
                                         "Thing model not found", request_id);
    }

    // 2. 验证服务输入参数
    int ret = acme_thing_model_validate_service_input(model, "set_temperature", input_params);
    if (ret != 0) {
        return send_service_error_response(plugin, device_id, "set_temperature",
                                         "Invalid input parameters", request_id);
    }

    // 3. 提取输入参数
    cJSON *temperature = cJSON_GetObjectItem(input_params, "temperature");
    cJSON *mode = cJSON_GetObjectItem(input_params, "mode");
    cJSON *fan_speed = cJSON_GetObjectItem(input_params, "fan_speed");

    if (!temperature || !cJSON_IsNumber(temperature)) {
        return send_service_error_response(plugin, device_id, "set_temperature",
                                         "Missing or invalid temperature parameter", request_id);
    }

    float target_temp = (float)cJSON_GetNumberValue(temperature);

    // 4. 验证温度范围
    if (target_temp < 16.0 || target_temp > 30.0) {
        char error_msg[128];
        snprintf(error_msg, sizeof(error_msg),
                "Temperature %.1f out of range (16.0-30.0)", target_temp);
        return send_service_error_response(plugin, device_id, "set_temperature",
                                         error_msg, request_id);
    }

    // 5. 执行服务逻辑 - 设置相关属性
    bool all_success = true;
    char error_details[256] = {0};

    // 5.1 设置目标温度
    ret = set_device_property(plugin, device_id, "STEMP", cJSON_CreateNumber(target_temp));
    if (ret != 0) {
        all_success = false;
        strcat(error_details, "Failed to set target temperature; ");
    }

    // 5.2 设置模式（如果提供）
    if (mode && cJSON_IsString(mode)) {
        int mode_value = parse_mode_string(mode->valuestring);
        if (mode_value >= 0) {
            ret = set_device_property(plugin, device_id, "SMODE", cJSON_CreateNumber(mode_value));
            if (ret != 0) {
                all_success = false;
                strcat(error_details, "Failed to set mode; ");
            }
        }
    }

    // 5.3 设置风速（如果提供）
    if (fan_speed && cJSON_IsString(fan_speed)) {
        int speed_value = parse_fan_speed_string(fan_speed->valuestring);
        if (speed_value >= 0) {
            ret = set_device_property(plugin, device_id, "SFAN", cJSON_CreateNumber(speed_value));
            if (ret != 0) {
                all_success = false;
                strcat(error_details, "Failed to set fan speed; ");
            }
        }
    }

    // 6. 等待设置完成
    usleep(200000); // 等待200ms让设备处理

    // 7. 读取当前状态确认
    cJSON *current_stemp = get_device_property(plugin, device_id, "STEMP");
    cJSON *current_mode = get_device_property(plugin, device_id, "SMODE");
    cJSON *current_rtemp = get_device_property(plugin, device_id, "RTEMP");

    float actual_stemp = current_stemp ? (float)cJSON_GetNumberValue(current_stemp) : 0.0;
    int actual_mode = current_mode ? (int)cJSON_GetNumberValue(current_mode) : 0;
    float actual_rtemp = current_rtemp ? (float)cJSON_GetNumberValue(current_rtemp) : 0.0;

    // 8. 判断执行结果
    bool temp_set_success = (fabs(actual_stemp - target_temp) < 0.1);
    bool overall_success = all_success && temp_set_success;

    // 9. 构造服务响应
    cJSON *output_params = cJSON_CreateObject();
    cJSON_AddBoolToObject(output_params, "success", overall_success);
    cJSON_AddNumberToObject(output_params, "target_temperature", target_temp);
    cJSON_AddNumberToObject(output_params, "actual_set_temperature", actual_stemp);
    cJSON_AddNumberToObject(output_params, "current_room_temperature", actual_rtemp);
    cJSON_AddNumberToObject(output_params, "current_mode", actual_mode);

    if (overall_success) {
        cJSON_AddStringToObject(output_params, "message", "Temperature set successfully");
    } else {
        cJSON_AddStringToObject(output_params, "message",
                               strlen(error_details) > 0 ? error_details : "Temperature setting failed");
    }

    cJSON_AddNumberToObject(output_params, "execution_time_ms", 200);

    // 10. 发送服务响应
    send_service_response_with_request_id(plugin, device_id, "set_temperature", output_params,
                                         overall_success ? "success" : "failure", request_id);

    // 清理资源
    if (current_stemp) cJSON_Delete(current_stemp);
    if (current_mode) cJSON_Delete(current_mode);
    if (current_rtemp) cJSON_Delete(current_rtemp);
    cJSON_Delete(output_params);

    plog_info(plugin, "set_temperature service completed for device %s: %s (%.1f°C)",
             device_id, overall_success ? "SUCCESS" : "FAILED", target_temp);

    return overall_success ? 0 : -1;
}

// 解析模式字符串
static int parse_mode_string(const char *mode_str)
{
    if (strcmp(mode_str, "auto") == 0) return 0;
    if (strcmp(mode_str, "cool") == 0) return 1;
    if (strcmp(mode_str, "heat") == 0) return 2;
    if (strcmp(mode_str, "fan") == 0) return 3;
    if (strcmp(mode_str, "dry") == 0) return 4;
    return -1; // 无效模式
}

// 解析风速字符串
static int parse_fan_speed_string(const char *speed_str)
{
    if (strcmp(speed_str, "auto") == 0) return 0;
    if (strcmp(speed_str, "low") == 0) return 1;
    if (strcmp(speed_str, "medium") == 0) return 2;
    if (strcmp(speed_str, "high") == 0) return 3;
    return -1; // 无效风速
}

// 发送带请求ID的服务响应
static void send_service_response_with_request_id(neu_plugin_t *plugin, const char *device_id,
                                                 const char *service_id, cJSON *output_params,
                                                 const char *result, const char *request_id)
{
    cJSON *response = cJSON_CreateObject();
    cJSON_AddStringToObject(response, "version", "1.0");
    cJSON_AddNumberToObject(response, "timestamp", time(NULL) * 1000);
    cJSON_AddStringToObject(response, "request_id", request_id);
    cJSON_AddStringToObject(response, "message_type", "service_response");
    cJSON_AddStringToObject(response, "source", "gateway");
    cJSON_AddStringToObject(response, "target", "cloud");

    cJSON *data = cJSON_CreateObject();
    cJSON_AddStringToObject(data, "service_id", service_id);
    cJSON_AddStringToObject(data, "result", result);

    if (output_params) {
        cJSON_AddItemToObject(data, "output_params", cJSON_Duplicate(output_params, 1));
    }

    cJSON_AddItemToObject(response, "data", data);

    char *response_json = cJSON_Print(response);
    if (response_json) {
        char topic[256];
        snprintf(topic, sizeof(topic), "/acme/%s/%s/service/response",
                plugin->config->gateway_id, device_id);

        mqtt_publish(plugin->conn, topic, response_json, strlen(response_json), 1);
        free(response_json);
    }

    cJSON_Delete(response);
}
```

##### 网关返回的set_temperature服务响应报文
```json
// 主题: /acme/SPT_GW_001/FCM_001/service/response
{
    "version": "1.0",
    "timestamp": 1703123460456,
    "request_id": "service_req_20241201_003",
    "message_type": "service_response",
    "source": "gateway",
    "target": "cloud",
    "data": {
        "service_id": "set_temperature",
        "result": "success",
        "output_params": {
            "success": true,
            "target_temperature": 24.0,
            "actual_set_temperature": 24.0,
            "current_room_temperature": 23.5,
            "current_mode": 0,
            "message": "Temperature set successfully",
            "execution_time_ms": 200
        }
    }
}
```

##### 服务调用失败的错误响应示例
```json
// 主题: /acme/SPT_GW_001/FCM_001/service/response
{
    "version": "1.0",
    "timestamp": 1703123460789,
    "request_id": "service_req_20241201_004",
    "message_type": "service_response",
    "source": "gateway",
    "target": "cloud",
    "data": {
        "service_id": "set_temperature",
        "result": "failure",
        "error_code": "INVALID_PARAMETER",
        "error_message": "Temperature 35.0 out of range (16.0-30.0)",
        "output_params": {
            "success": false,
            "target_temperature": 35.0,
            "message": "Temperature 35.0 out of range (16.0-30.0)"
        }
    }
}
```

#### switch_mode模式切换服务的完整MQTT交互流程

##### 云端下发的模式切换服务请求报文
```json
// 主题: /acme/SPT_GW_001/FCM_001/service/request
{
    "version": "1.0",
    "timestamp": 1703123470000,
    "request_id": "service_req_20241201_005",
    "message_type": "service_request",
    "source": "cloud",
    "target": "FCM_001",
    "data": {
        "service_id": "switch_mode",
        "input_params": {
            "mode": "cool",
            "auto_adjust_temp": true,
            "target_temperature": 22.0
        }
    }
}
```

##### 网关处理switch_mode服务的完整流程
```c
// 处理模式切换服务的具体实现
static int fcm_switch_mode_service_handler(neu_plugin_t *plugin, const char *device_id,
                                          cJSON *input_params, const char *request_id)
{
    plog_info(plugin, "Processing switch_mode service for device: %s", device_id);

    // 1. 获取物模型验证服务参数
    acme_thing_model_t *model = acme_thing_model_manager_get(plugin->model_manager,
                                                            "acme.device.fcm.v1");
    if (!model) {
        return send_service_error_response(plugin, device_id, "switch_mode",
                                         "Thing model not found", request_id);
    }

    // 2. 验证服务输入参数
    int ret = acme_thing_model_validate_service_input(model, "switch_mode", input_params);
    if (ret != 0) {
        return send_service_error_response(plugin, device_id, "switch_mode",
                                         "Invalid input parameters", request_id);
    }

    // 3. 提取输入参数
    cJSON *mode = cJSON_GetObjectItem(input_params, "mode");
    cJSON *auto_adjust = cJSON_GetObjectItem(input_params, "auto_adjust_temp");
    cJSON *target_temp = cJSON_GetObjectItem(input_params, "target_temperature");

    if (!mode || !cJSON_IsString(mode)) {
        return send_service_error_response(plugin, device_id, "switch_mode",
                                         "Missing or invalid mode parameter", request_id);
    }

    // 4. 解析模式值
    int mode_value = parse_mode_string(mode->valuestring);
    if (mode_value < 0) {
        char error_msg[128];
        snprintf(error_msg, sizeof(error_msg),
                "Invalid mode: %s (valid: auto, cool, heat, fan, dry)", mode->valuestring);
        return send_service_error_response(plugin, device_id, "switch_mode",
                                         error_msg, request_id);
    }

    // 5. 获取当前状态
    cJSON *current_mode = get_device_property(plugin, device_id, "SMODE");
    cJSON *current_onoff = get_device_property(plugin, device_id, "ONOFF");
    int old_mode = current_mode ? (int)cJSON_GetNumberValue(current_mode) : -1;
    int current_power = current_onoff ? (int)cJSON_GetNumberValue(current_onoff) : 0;

    // 6. 执行模式切换逻辑
    bool all_success = true;
    char operation_log[512] = {0};

    // 6.1 如果设备关闭，先开启设备
    if (current_power == 0) {
        ret = set_device_property(plugin, device_id, "ONOFF", cJSON_CreateNumber(1));
        if (ret != 0) {
            all_success = false;
            strcat(operation_log, "Failed to power on device; ");
        } else {
            strcat(operation_log, "Device powered on; ");
            usleep(500000); // 等待500ms让设备启动
        }
    }

    // 6.2 设置运行模式
    if (all_success) {
        ret = set_device_property(plugin, device_id, "SMODE", cJSON_CreateNumber(mode_value));
        if (ret != 0) {
            all_success = false;
            strcat(operation_log, "Failed to set mode; ");
        } else {
            snprintf(operation_log + strlen(operation_log),
                    sizeof(operation_log) - strlen(operation_log),
                    "Mode changed from %d to %d; ", old_mode, mode_value);
        }
    }

    // 6.3 如果需要自动调节温度
    if (all_success && auto_adjust && cJSON_IsTrue(auto_adjust) &&
        target_temp && cJSON_IsNumber(target_temp)) {

        float temp_value = (float)cJSON_GetNumberValue(target_temp);
        if (temp_value >= 16.0 && temp_value <= 30.0) {
            ret = set_device_property(plugin, device_id, "STEMP", cJSON_CreateNumber(temp_value));
            if (ret != 0) {
                // 温度设置失败不影响模式切换成功
                strcat(operation_log, "Mode set but temperature adjustment failed; ");
            } else {
                snprintf(operation_log + strlen(operation_log),
                        sizeof(operation_log) - strlen(operation_log),
                        "Temperature adjusted to %.1f°C; ", temp_value);
            }
        }
    }

    // 6.4 根据模式自动调整风速
    if (all_success) {
        int recommended_fan_speed = get_recommended_fan_speed(mode_value);
        if (recommended_fan_speed >= 0) {
            ret = set_device_property(plugin, device_id, "SFAN",
                                    cJSON_CreateNumber(recommended_fan_speed));
            if (ret == 0) {
                snprintf(operation_log + strlen(operation_log),
                        sizeof(operation_log) - strlen(operation_log),
                        "Fan speed adjusted to %d; ", recommended_fan_speed);
            }
        }
    }

    // 7. 等待设置完成
    usleep(300000); // 等待300ms

    // 8. 读取当前状态确认
    cJSON *final_mode = get_device_property(plugin, device_id, "SMODE");
    cJSON *final_onoff = get_device_property(plugin, device_id, "ONOFF");
    cJSON *final_stemp = get_device_property(plugin, device_id, "STEMP");
    cJSON *final_sfan = get_device_property(plugin, device_id, "SFAN");

    int actual_mode = final_mode ? (int)cJSON_GetNumberValue(final_mode) : -1;
    int actual_power = final_onoff ? (int)cJSON_GetNumberValue(final_onoff) : 0;
    float actual_stemp = final_stemp ? (float)cJSON_GetNumberValue(final_stemp) : 0.0;
    int actual_fan = final_sfan ? (int)cJSON_GetNumberValue(final_sfan) : 0;

    // 9. 判断执行结果
    bool mode_switch_success = (actual_mode == mode_value) && (actual_power == 1);
    bool overall_success = all_success && mode_switch_success;

    // 10. 构造服务响应
    cJSON *output_params = cJSON_CreateObject();
    cJSON_AddBoolToObject(output_params, "success", overall_success);
    cJSON_AddStringToObject(output_params, "target_mode", mode->valuestring);
    cJSON_AddNumberToObject(output_params, "target_mode_value", mode_value);
    cJSON_AddNumberToObject(output_params, "actual_mode_value", actual_mode);
    cJSON_AddStringToObject(output_params, "actual_mode", get_mode_string(actual_mode));
    cJSON_AddBoolToObject(output_params, "device_powered", actual_power == 1);
    cJSON_AddNumberToObject(output_params, "current_temperature_setting", actual_stemp);
    cJSON_AddNumberToObject(output_params, "current_fan_speed", actual_fan);

    if (overall_success) {
        cJSON_AddStringToObject(output_params, "message", "Mode switched successfully");
    } else {
        cJSON_AddStringToObject(output_params, "message",
                               strlen(operation_log) > 0 ? operation_log : "Mode switch failed");
    }

    cJSON_AddStringToObject(output_params, "operation_log", operation_log);
    cJSON_AddNumberToObject(output_params, "execution_time_ms", 300);

    // 11. 发送服务响应
    send_service_response_with_request_id(plugin, device_id, "switch_mode", output_params,
                                         overall_success ? "success" : "failure", request_id);

    // 清理资源
    if (current_mode) cJSON_Delete(current_mode);
    if (current_onoff) cJSON_Delete(current_onoff);
    if (final_mode) cJSON_Delete(final_mode);
    if (final_onoff) cJSON_Delete(final_onoff);
    if (final_stemp) cJSON_Delete(final_stemp);
    if (final_sfan) cJSON_Delete(final_sfan);
    cJSON_Delete(output_params);

    plog_info(plugin, "switch_mode service completed for device %s: %s (%s)",
             device_id, overall_success ? "SUCCESS" : "FAILED", mode->valuestring);

    return overall_success ? 0 : -1;
}

// 获取推荐风速
static int get_recommended_fan_speed(int mode)
{
    switch (mode) {
    case 0: return 0; // auto模式，自动风速
    case 1: return 2; // cool模式，中等风速
    case 2: return 2; // heat模式，中等风速
    case 3: return 3; // fan模式，高风速
    case 4: return 1; // dry模式，低风速
    default: return -1;
    }
}

// 获取模式字符串
static const char *get_mode_string(int mode_value)
{
    switch (mode_value) {
    case 0: return "auto";
    case 1: return "cool";
    case 2: return "heat";
    case 3: return "fan";
    case 4: return "dry";
    default: return "unknown";
    }
}
```

##### 网关返回的switch_mode服务响应报文
```json
// 主题: /acme/SPT_GW_001/FCM_001/service/response
{
    "version": "1.0",
    "timestamp": 1703123470678,
    "request_id": "service_req_20241201_005",
    "message_type": "service_response",
    "source": "gateway",
    "target": "cloud",
    "data": {
        "service_id": "switch_mode",
        "result": "success",
        "output_params": {
            "success": true,
            "target_mode": "cool",
            "target_mode_value": 1,
            "actual_mode_value": 1,
            "actual_mode": "cool",
            "device_powered": true,
            "current_temperature_setting": 22.0,
            "current_fan_speed": 2,
            "message": "Mode switched successfully",
            "operation_log": "Device powered on; Mode changed from 0 to 1; Temperature adjusted to 22.0°C; Fan speed adjusted to 2; ",
            "execution_time_ms": 300
        }
    }
}
```

##### 模式切换失败的错误响应示例
```json
// 主题: /acme/SPT_GW_001/FCM_001/service/response
{
    "version": "1.0",
    "timestamp": 1703123470890,
    "request_id": "service_req_20241201_006",
    "message_type": "service_response",
    "source": "gateway",
    "target": "cloud",
    "data": {
        "service_id": "switch_mode",
        "result": "failure",
        "error_code": "INVALID_MODE",
        "error_message": "Invalid mode: invalid_mode (valid: auto, cool, heat, fan, dry)",
        "output_params": {
            "success": false,
            "target_mode": "invalid_mode",
            "message": "Invalid mode: invalid_mode (valid: auto, cool, heat, fan, dry)"
        }
    }
}
```

### 3. 事件通知的完整MQTT交互流程

#### 网关主动上报的事件通知报文
```json
// 主题: /acme/SPT_GW_001/ECM_001/event/notify
{
    "version": "1.0",
    "timestamp": 1703123459345,
    "request_id": "event_notify_20241201_003",
    "message_type": "event_notify",
    "source": "gateway",
    "target": "cloud",
    "data": {
        "event": {
            "identifier": "temperature_alarm",
            "name": "温度报警",
            "event_type": "warning",
            "timestamp": 1703123459345,
            "output_params": {
                "current_temperature": 32.5,
                "threshold_value": 30.0,
                "alarm_type": "high_temperature",
                "severity_level": 2,
                "device_location": "Building A - Floor 2",
                "event_timestamp": 1703123459345
            }
        }
    }
}
```

#### 云端返回的事件确认报文
```json
// 主题: /acme/SPT_GW_001/ECM_001/event/ack
{
    "version": "1.0",
    "timestamp": 1703123459567,
    "request_id": "event_notify_20241201_003",
    "message_type": "event_ack",
    "source": "cloud",
    "target": "gateway",
    "data": {
        "event_id": "temperature_alarm",
        "ack_status": "received",
        "processing_action": "alert_sent",
        "message": "Event received and alert sent to operators"
    }
}
```

#### 网关处理事件确认的流程
```c
// 处理事件确认
static void handle_event_ack(neu_plugin_t *plugin,
                            acme_topic_info_t *topic_info,
                            acme_protocol_message_t *mqtt_msg)
{
    cJSON *data = mqtt_msg->data;
    if (!data) return;

    cJSON *event_id = cJSON_GetObjectItem(data, "event_id");
    cJSON *ack_status = cJSON_GetObjectItem(data, "ack_status");

    if (event_id && cJSON_IsString(event_id) &&
        ack_status && cJSON_IsString(ack_status)) {

        if (strcmp(ack_status->valuestring, "received") == 0) {
            // 更新事件状态为已确认
            update_event_ack_status(plugin, topic_info->device_id,
                                   event_id->valuestring, true);

            plog_info(plugin, "Event %s acknowledged by cloud for device %s",
                     event_id->valuestring, topic_info->device_id);
        }
    }
}

// 数据变化触发事件检查的完整流程
static void on_neuron_data_update(neu_plugin_t *plugin, const char *node_name,
                                 const char *group_name, neu_resp_tag_value_t *tag_values,
                                 int tag_count)
{
    // 根据节点名获取设备ID
    char *device_id = extract_device_id_from_node(node_name);
    if (!device_id) return;

    // 遍历更新的标签值
    for (int i = 0; i < tag_count; i++) {
        neu_resp_tag_value_t *tag = &tag_values[i];

        // 查找对应的物模型属性
        char *property_id = acme_mapping_manager_find_property(plugin->mapping_manager,
                                                              device_id, node_name,
                                                              group_name, tag->tag);
        if (!property_id) continue;

        // 检查是否为温度属性，触发报警检查
        if (strcmp(property_id, "TEMP") == 0 || strcmp(property_id, "RTEMP") == 0) {
            float temperature = tag->value.f32;
            check_temperature_alarm_trigger(plugin, device_id, temperature);
        }

        free(property_id);
    }

    free(device_id);
}
```

## 🧪 单元测试用例

### 1. 物模型管理器测试用例

```c
// test_thing_model_manager.c
#include <assert.h>
#include <stdio.h>
#include <string.h>
#include "thing_model_manager.h"

// 测试物模型解析
void test_thing_model_parse()
{
    printf("Testing thing model parsing...\n");

    const char *fcm_json = "{"
        "\"thing_model\": {"
            "\"version\": \"1.0\","
            "\"model_id\": \"acme.device.fcm.v1\","
            "\"model_name\": \"ACME FCM空调控制器\","
            "\"device_type\": \"FCM\","
            "\"manufacturer\": \"ACME\","
            "\"properties\": {"
                "\"ONOFF\": {"
                    "\"name\": \"开关状态\","
                    "\"data_type\": \"enum\","
                    "\"access_mode\": \"rw\","
                    "\"enum_values\": ["
                        "{\"value\": 0, \"name\": \"关闭\"},"
                        "{\"value\": 1, \"name\": \"开启\"}"
                    "]"
                "},"
                "\"STEMP\": {"
                    "\"name\": \"设定温度\","
                    "\"data_type\": \"float\","
                    "\"access_mode\": \"rw\","
                    "\"unit\": \"°C\","
                    "\"range\": {\"min\": 16.0, \"max\": 30.0}"
                "}"
            "},"
            "\"services\": {"
                "\"power_control\": {"
                    "\"name\": \"电源控制\","
                    "\"input_params\": {"
                        "\"power\": {"
                            "\"name\": \"电源状态\","
                            "\"data_type\": \"bool\","
                            "\"required\": true"
                        "}"
                    "},"
                    "\"output_params\": {"
                        "\"success\": {"
                            "\"name\": \"执行结果\","
                            "\"data_type\": \"bool\""
                        "}"
                    "}"
                "}"
            "},"
            "\"events\": {"
                "\"device_offline\": {"
                    "\"name\": \"设备离线\","
                    "\"event_type\": \"error\","
                    "\"output_params\": {"
                        "\"offline_time\": {"
                            "\"name\": \"离线时间\","
                            "\"data_type\": \"int64\""
                        "}"
                    "}"
                "}"
            "}"
        "}"
    "}";

    // 解析物模型
    acme_thing_model_t *model = acme_thing_model_parse(fcm_json);
    assert(model != NULL);
    assert(strcmp(model->model_id, "acme.device.fcm.v1") == 0);
    assert(strcmp(model->device_type, "FCM") == 0);

    // 测试属性查找
    acme_property_t *onoff_prop = acme_thing_model_get_property(model, "ONOFF");
    assert(onoff_prop != NULL);
    assert(onoff_prop->data_type == ACME_DATA_TYPE_ENUM);
    assert(onoff_prop->access_mode == ACME_ACCESS_READ_WRITE);
    assert(onoff_prop->enum_count == 2);

    acme_property_t *stemp_prop = acme_thing_model_get_property(model, "STEMP");
    assert(stemp_prop != NULL);
    assert(stemp_prop->data_type == ACME_DATA_TYPE_FLOAT);
    assert(strcmp(stemp_prop->unit, "°C") == 0);
    assert(stemp_prop->range.has_range == true);
    assert(stemp_prop->range.min == 16.0);
    assert(stemp_prop->range.max == 30.0);

    // 测试服务查找
    acme_service_t *power_service = acme_thing_model_get_service(model, "power_control");
    assert(power_service != NULL);
    assert(strcmp(power_service->name, "电源控制") == 0);
    assert(power_service->input_param_count == 1);
    assert(power_service->output_param_count == 1);

    // 测试事件查找
    acme_event_t *offline_event = acme_thing_model_get_event(model, "device_offline");
    assert(offline_event != NULL);
    assert(strcmp(offline_event->name, "设备离线") == 0);
    assert(offline_event->event_type == ACME_EVENT_ERROR);
    assert(offline_event->output_param_count == 1);

    // 清理资源
    acme_thing_model_free(model);

    printf("✅ Thing model parsing test passed\n");
}

// 测试属性值验证
void test_property_value_validation()
{
    printf("Testing property value validation...\n");

    // 创建测试物模型
    acme_thing_model_t *model = create_test_fcm_model();
    assert(model != NULL);

    // 测试有效的温度值
    cJSON *valid_temp = cJSON_CreateNumber(25.5);
    int ret = acme_thing_model_validate_property_value(model, "STEMP", valid_temp);
    assert(ret == 0);
    cJSON_Delete(valid_temp);

    // 测试超出范围的温度值
    cJSON *invalid_temp_high = cJSON_CreateNumber(35.0);
    ret = acme_thing_model_validate_property_value(model, "STEMP", invalid_temp_high);
    assert(ret != 0);
    cJSON_Delete(invalid_temp_high);

    cJSON *invalid_temp_low = cJSON_CreateNumber(10.0);
    ret = acme_thing_model_validate_property_value(model, "STEMP", invalid_temp_low);
    assert(ret != 0);
    cJSON_Delete(invalid_temp_low);

    // 测试有效的枚举值
    cJSON *valid_onoff = cJSON_CreateNumber(1);
    ret = acme_thing_model_validate_property_value(model, "ONOFF", valid_onoff);
    assert(ret == 0);
    cJSON_Delete(valid_onoff);

    // 测试无效的枚举值
    cJSON *invalid_onoff = cJSON_CreateNumber(5);
    ret = acme_thing_model_validate_property_value(model, "ONOFF", invalid_onoff);
    assert(ret != 0);
    cJSON_Delete(invalid_onoff);

    // 测试类型不匹配
    cJSON *wrong_type = cJSON_CreateString("invalid");
    ret = acme_thing_model_validate_property_value(model, "STEMP", wrong_type);
    assert(ret != 0);
    cJSON_Delete(wrong_type);

    acme_thing_model_free(model);

    printf("✅ Property value validation test passed\n");
}

// 测试服务输入验证
void test_service_input_validation()
{
    printf("Testing service input validation...\n");

    acme_thing_model_t *model = create_test_fcm_model();
    assert(model != NULL);

    // 测试有效的服务输入
    cJSON *valid_input = cJSON_CreateObject();
    cJSON_AddBoolToObject(valid_input, "power", true);

    int ret = acme_thing_model_validate_service_input(model, "power_control", valid_input);
    assert(ret == 0);
    cJSON_Delete(valid_input);

    // 测试缺少必需参数
    cJSON *missing_param = cJSON_CreateObject();
    cJSON_AddStringToObject(missing_param, "mode", "auto");

    ret = acme_thing_model_validate_service_input(model, "power_control", missing_param);
    assert(ret != 0);
    cJSON_Delete(missing_param);

    // 测试参数类型错误
    cJSON *wrong_type_input = cJSON_CreateObject();
    cJSON_AddStringToObject(wrong_type_input, "power", "true"); // 应该是bool类型

    ret = acme_thing_model_validate_service_input(model, "power_control", wrong_type_input);
    assert(ret != 0);
    cJSON_Delete(wrong_type_input);

    acme_thing_model_free(model);

    printf("✅ Service input validation test passed\n");
}
```

### 2. 协议映射器测试用例

```c
// test_protocol_mapper.c
#include <assert.h>
#include <stdio.h>
#include "protocol_mapper.h"

// 测试MQTT消息解析
void test_mqtt_message_parsing()
{
    printf("Testing MQTT message parsing...\n");

    const char *mqtt_json = "{"
        "\"version\": \"1.0\","
        "\"timestamp\": 1703123456789,"
        "\"request_id\": \"test_req_001\","
        "\"message_type\": \"write_request\","
        "\"source\": \"cloud\","
        "\"target\": \"FCM_001\","
        "\"data\": {"
            "\"properties\": ["
                "{\"identifier\": \"STEMP\", \"value\": 25.5},"
                "{\"identifier\": \"ONOFF\", \"value\": 1}"
            "]"
        "}"
    "}";

    acme_protocol_message_t msg = {0};
    int ret = acme_protocol_message_parse(mqtt_json, &msg);
    assert(ret == 0);
    assert(strcmp(msg.version, "1.0") == 0);
    assert(msg.timestamp == 1703123456789);
    assert(strcmp(msg.request_id, "test_req_001") == 0);
    assert(msg.message_type == ACME_MSG_WRITE_REQUEST);
    assert(strcmp(msg.source, "cloud") == 0);
    assert(strcmp(msg.target, "FCM_001") == 0);
    assert(msg.data != NULL);

    // 验证数据内容
    cJSON *properties = cJSON_GetObjectItem(msg.data, "properties");
    assert(properties != NULL);
    assert(cJSON_IsArray(properties));
    assert(cJSON_GetArraySize(properties) == 2);

    acme_protocol_message_free(&msg);

    printf("✅ MQTT message parsing test passed\n");
}

// 测试读取请求映射
void test_read_request_mapping()
{
    printf("Testing read request mapping...\n");

    // 准备测试数据
    acme_protocol_message_t mqtt_msg = {0};
    mqtt_msg.message_type = ACME_MSG_READ_REQUEST;
    strcpy(mqtt_msg.target, "FCM_001");

    mqtt_msg.data = cJSON_CreateObject();
    cJSON *properties = cJSON_CreateArray();
    cJSON_AddItemToArray(properties, cJSON_CreateString("ONOFF"));
    cJSON_AddItemToArray(properties, cJSON_CreateString("STEMP"));
    cJSON_AddItemToObject(mqtt_msg.data, "properties", properties);
    cJSON_AddStringToObject(mqtt_msg.data, "group", "default_group");

    acme_thing_model_t *model = create_test_fcm_model();

    // 执行映射
    neu_reqresp_head_t neu_head = {0};
    void *neu_data = NULL;
    int ret = acme_map_read_request_to_neuron(&mqtt_msg, model, &neu_head, &neu_data);
    assert(ret == 0);
    assert(neu_head.type == NEU_REQ_READ_GROUP);
    assert(neu_data != NULL);

    neu_req_read_group_t *read_req = (neu_req_read_group_t *)neu_data;
    assert(strcmp(read_req->driver, "FCM_001") == 0);
    assert(strcmp(read_req->group, "default_group") == 0);

    // 清理资源
    free(neu_data);
    acme_protocol_message_free(&mqtt_msg);
    acme_thing_model_free(model);

    printf("✅ Read request mapping test passed\n");
}

// 测试写入请求映射
void test_write_request_mapping()
{
    printf("Testing write request mapping...\n");

    // 准备测试数据
    acme_protocol_message_t mqtt_msg = {0};
    mqtt_msg.message_type = ACME_MSG_WRITE_REQUEST;
    strcpy(mqtt_msg.target, "FCM_001");

    mqtt_msg.data = cJSON_CreateObject();
    cJSON *properties = cJSON_CreateArray();

    cJSON *prop1 = cJSON_CreateObject();
    cJSON_AddStringToObject(prop1, "identifier", "STEMP");
    cJSON_AddNumberToObject(prop1, "value", 25.5);
    cJSON_AddItemToArray(properties, prop1);

    cJSON *prop2 = cJSON_CreateObject();
    cJSON_AddStringToObject(prop2, "identifier", "ONOFF");
    cJSON_AddNumberToObject(prop2, "value", 1);
    cJSON_AddItemToArray(properties, prop2);

    cJSON_AddItemToObject(mqtt_msg.data, "properties", properties);

    acme_thing_model_t *model = create_test_fcm_model();

    // 执行映射
    neu_reqresp_head_t neu_head = {0};
    void *neu_data = NULL;
    int ret = acme_map_write_request_to_neuron(&mqtt_msg, model, &neu_head, &neu_data);
    assert(ret == 0);
    assert(neu_head.type == NEU_REQ_WRITE_TAGS);
    assert(neu_data != NULL);

    neu_req_write_tags_t *write_req = (neu_req_write_tags_t *)neu_data;
    assert(strcmp(write_req->driver, "FCM_001") == 0);
    assert(write_req->n_tag == 2);
    assert(write_req->tags != NULL);

    // 验证标签数据
    bool found_stemp = false, found_onoff = false;
    for (int i = 0; i < write_req->n_tag; i++) {
        if (strcmp(write_req->tags[i].name, "STEMP") == 0) {
            assert(write_req->tags[i].value.f32 == 25.5f);
            found_stemp = true;
        } else if (strcmp(write_req->tags[i].name, "ONOFF") == 0) {
            assert(write_req->tags[i].value.i32 == 1);
            found_onoff = true;
        }
    }
    assert(found_stemp && found_onoff);

    // 清理资源
    free(write_req->tags);
    free(neu_data);
    acme_protocol_message_free(&mqtt_msg);
    acme_thing_model_free(model);

    printf("✅ Write request mapping test passed\n");
}
```

### 3. 映射管理器测试用例

```c
// test_mapping_manager.c
#include <assert.h>
#include <stdio.h>
#include "neuron_mapping_manager.h"

// 测试映射配置加载
void test_mapping_config_loading()
{
    printf("Testing mapping config loading...\n");

    const char *mapping_json = "{"
        "\"neuron_mapping\": {"
            "\"device_instance\": {"
                "\"device_id\": \"FCM_001\","
                "\"thing_model_id\": \"acme.device.fcm.v1\","
                "\"neuron_node\": \"FCM_001_node\""
            "},"
            "\"property_groups\": ["
                "{"
                    "\"group_id\": \"default_group\","
                    "\"neuron_group\": \"default_group\","
                    "\"properties\": ["
                        "{"
                            "\"property_id\": \"ONOFF\","
                            "\"neuron_tag\": \"ONOFF\","
                            "\"tag_address\": \"0x01\","
                            "\"tag_type\": \"BIT\","
                            "\"tag_attribute\": \"RW\""
                        "},"
                        "{"
                            "\"property_id\": \"STEMP\","
                            "\"neuron_tag\": \"STEMP\","
                            "\"tag_address\": \"0x10\","
                            "\"tag_type\": \"FLOAT\","
                            "\"tag_attribute\": \"RW\""
                        "}"
                    "]"
                "}"
            "]"
        "}"
    "}";

    acme_mapping_manager_t *manager = acme_mapping_manager_create();
    assert(manager != NULL);

    int ret = acme_mapping_manager_load_config(manager, mapping_json);
    assert(ret == 0);
    assert(manager->device_count == 1);

    // 测试标签映射查找
    acme_tag_mapping_t *onoff_mapping = acme_mapping_manager_find_tag(manager, "FCM_001", "ONOFF");
    assert(onoff_mapping != NULL);
    assert(strcmp(onoff_mapping->neuron_node, "FCM_001_node") == 0);
    assert(strcmp(onoff_mapping->neuron_group, "default_group") == 0);
    assert(strcmp(onoff_mapping->neuron_tag, "ONOFF") == 0);
    assert(strcmp(onoff_mapping->tag_address, "0x01") == 0);
    assert(onoff_mapping->tag_type == NEU_TYPE_BIT);

    acme_tag_mapping_t *stemp_mapping = acme_mapping_manager_find_tag(manager, "FCM_001", "STEMP");
    assert(stemp_mapping != NULL);
    assert(strcmp(stemp_mapping->tag_address, "0x10") == 0);
    assert(stemp_mapping->tag_type == NEU_TYPE_FLOAT);

    // 测试反向查找
    char *property_id = acme_mapping_manager_find_property(manager, "FCM_001",
                                                          "FCM_001_node", "default_group", "ONOFF");
    assert(property_id != NULL);
    assert(strcmp(property_id, "ONOFF") == 0);
    free(property_id);

    acme_mapping_manager_destroy(manager);

    printf("✅ Mapping config loading test passed\n");
}

// 运行所有测试
int main()
{
    printf("🧪 Running ACME MQTT Thing Model Unit Tests\n");
    printf("===========================================\n");

    // 物模型管理器测试
    test_thing_model_parse();
    test_property_value_validation();
    test_service_input_validation();

    // 协议映射器测试
    test_mqtt_message_parsing();
    test_read_request_mapping();
    test_write_request_mapping();

    // 映射管理器测试
    test_mapping_config_loading();

    printf("\n🎉 All tests passed successfully!\n");
    return 0;
}

// 辅助函数：创建测试用的FCM物模型
static acme_thing_model_t *create_test_fcm_model()
{
    acme_thing_model_t *model = calloc(1, sizeof(acme_thing_model_t));
    model->model_id = strdup("acme.device.fcm.v1");
    model->device_type = strdup("FCM");

    // 创建ONOFF属性
    acme_property_t *onoff_prop = calloc(1, sizeof(acme_property_t));
    onoff_prop->identifier = strdup("ONOFF");
    onoff_prop->name = strdup("开关状态");
    onoff_prop->data_type = ACME_DATA_TYPE_ENUM;
    onoff_prop->access_mode = ACME_ACCESS_READ_WRITE;
    onoff_prop->enum_count = 2;
    onoff_prop->enum_values = calloc(2, sizeof(acme_enum_value_t));
    onoff_prop->enum_values[0].value = 0;
    onoff_prop->enum_values[0].name = strdup("关闭");
    onoff_prop->enum_values[1].value = 1;
    onoff_prop->enum_values[1].name = strdup("开启");

    // 创建STEMP属性
    acme_property_t *stemp_prop = calloc(1, sizeof(acme_property_t));
    stemp_prop->identifier = strdup("STEMP");
    stemp_prop->name = strdup("设定温度");
    stemp_prop->data_type = ACME_DATA_TYPE_FLOAT;
    stemp_prop->access_mode = ACME_ACCESS_READ_WRITE;
    stemp_prop->unit = strdup("°C");
    stemp_prop->range.has_range = true;
    stemp_prop->range.min = 16.0;
    stemp_prop->range.max = 30.0;

    // 链接属性
    onoff_prop->next = stemp_prop;
    model->properties = onoff_prop;
    model->property_count = 2;

    // 创建power_control服务
    acme_service_t *power_service = calloc(1, sizeof(acme_service_t));
    power_service->identifier = strdup("power_control");
    power_service->name = strdup("电源控制");
    power_service->input_param_count = 1;
    power_service->input_params = calloc(1, sizeof(acme_service_param_t));
    power_service->input_params[0].identifier = strdup("power");
    power_service->input_params[0].name = strdup("电源状态");
    power_service->input_params[0].data_type = ACME_DATA_TYPE_BOOL;
    power_service->input_params[0].required = true;

    model->services = power_service;
    model->service_count = 1;

    return model;
}
```

## 🚀 部署和配置指南

### 1. 环境准备

#### 系统要求
```bash
# 操作系统要求
- Linux (Ubuntu 20.04+ / CentOS 8+ / Debian 11+)
- 内存: 最小 2GB，推荐 4GB+
- 存储: 最小 10GB 可用空间
- 网络: 支持MQTT连接的网络环境

# 依赖软件版本
- GCC 9.0+
- CMake 3.16+
- libcjson-dev 1.7.14+
- libmosquitto-dev 2.0+
- openssl-dev 1.1.1+
```

#### 依赖安装
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install -y build-essential cmake git
sudo apt install -y libcjson-dev libmosquitto-dev libssl-dev
sudo apt install -y pkg-config

# CentOS/RHEL
sudo yum groupinstall -y "Development Tools"
sudo yum install -y cmake git
sudo yum install -y cjson-devel mosquitto-devel openssl-devel
sudo yum install -y pkgconfig

# 验证安装
gcc --version
cmake --version
pkg-config --modversion libcjson
```

### 2. 编译配置

#### CMakeLists.txt 完整配置
```cmake
# CMakeLists.txt for ACME_MQTT Thing Model Plugin
cmake_minimum_required(VERSION 3.16)
project(acme-mqtt-thing-model VERSION 1.0.0)

# 设置C标准
set(CMAKE_C_STANDARD 11)
set(CMAKE_C_STANDARD_REQUIRED ON)

# 编译选项
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -Wall -Wextra -Werror")
set(CMAKE_C_FLAGS_DEBUG "-g -O0 -DDEBUG")
set(CMAKE_C_FLAGS_RELEASE "-O2 -DNDEBUG")

# 查找依赖包
find_package(PkgConfig REQUIRED)
pkg_check_modules(CJSON REQUIRED libcjson)
pkg_check_modules(MOSQUITTO REQUIRED libmosquitto)
find_package(OpenSSL REQUIRED)

# Neuron SDK路径
set(NEURON_SDK_PATH "${CMAKE_SOURCE_DIR}/../neuron-sdk" CACHE PATH "Neuron SDK path")
if(NOT EXISTS ${NEURON_SDK_PATH})
    message(FATAL_ERROR "Neuron SDK not found at ${NEURON_SDK_PATH}")
endif()

# 包含目录
include_directories(
    ${NEURON_SDK_PATH}/include
    ${CJSON_INCLUDE_DIRS}
    ${CMAKE_CURRENT_SOURCE_DIR}
)

# 源文件分组
set(THING_MODEL_SOURCES
    thing_model_manager.c
    protocol_mapper.c
    topic_manager.c
)

set(MAPPING_SOURCES
    neuron_mapping_manager.c
    device_discovery_manager.c
    mapping_optimizer.c
)

set(SECURITY_SOURCES
    security_manager.c
)

set(PLUGIN_SOURCES
    acme_mqtt_plugin.c
    mqtt_plugin_intf.c
    mqtt_handle.c
    mqtt_config.c
)

# 创建插件库
add_library(${PROJECT_NAME} SHARED
    ${PLUGIN_SOURCES}
    ${THING_MODEL_SOURCES}
    ${MAPPING_SOURCES}
    ${SECURITY_SOURCES}
)

# 链接库
target_link_libraries(${PROJECT_NAME}
    ${NEURON_SDK_PATH}/build/libneuron-base.so
    ${CJSON_LIBRARIES}
    ${MOSQUITTO_LIBRARIES}
    OpenSSL::SSL
    OpenSSL::Crypto
    pthread
    m
)

# 编译器特定选项
target_compile_options(${PROJECT_NAME} PRIVATE
    ${CJSON_CFLAGS_OTHER}
    ${MOSQUITTO_CFLAGS_OTHER}
)

# 安装配置
install(TARGETS ${PROJECT_NAME}
    LIBRARY DESTINATION /opt/neuron/plugins
)

install(FILES schema/acme-mqtt.json
    DESTINATION /opt/neuron/plugins/schema
)

# 测试目标
if(BUILD_TESTING)
    enable_testing()
    add_subdirectory(tests)
endif()

# 打包配置
set(CPACK_PACKAGE_NAME "acme-mqtt-thing-model")
set(CPACK_PACKAGE_VERSION "${PROJECT_VERSION}")
set(CPACK_PACKAGE_DESCRIPTION "ACME MQTT Thing Model Plugin for Neuron")
set(CPACK_GENERATOR "DEB;RPM")
include(CPack)
```

#### 编译脚本
```bash
#!/bin/bash
# build.sh - 编译脚本

set -e

# 配置变量
BUILD_TYPE=${1:-Release}
BUILD_DIR="build"
INSTALL_PREFIX="/opt/neuron"

echo "🔨 Building ACME MQTT Thing Model Plugin..."
echo "Build Type: $BUILD_TYPE"

# 清理旧的构建
if [ -d "$BUILD_DIR" ]; then
    echo "Cleaning old build directory..."
    rm -rf "$BUILD_DIR"
fi

# 创建构建目录
mkdir -p "$BUILD_DIR"
cd "$BUILD_DIR"

# 配置CMake
echo "Configuring CMake..."
cmake .. \
    -DCMAKE_BUILD_TYPE="$BUILD_TYPE" \
    -DCMAKE_INSTALL_PREFIX="$INSTALL_PREFIX" \
    -DBUILD_TESTING=ON

# 编译
echo "Building..."
make -j$(nproc)

# 运行测试
if [ "$BUILD_TYPE" = "Debug" ]; then
    echo "Running tests..."
    make test
fi

echo "✅ Build completed successfully!"
echo "Plugin library: $BUILD_DIR/libacme-mqtt-thing-model.so"
```

### 3. 配置文件设置

#### 插件配置文件 (acme-mqtt.json)
```json
{
    "host": {
        "name": "host",
        "type": "string",
        "default": "127.0.0.1",
        "description": "MQTT服务器地址"
    },
    "port": {
        "name": "port",
        "type": "int",
        "default": 1883,
        "description": "MQTT服务器端口"
    },
    "username": {
        "name": "username",
        "type": "string",
        "default": "",
        "description": "MQTT用户名"
    },
    "password": {
        "name": "password",
        "type": "string",
        "default": "",
        "description": "MQTT密码"
    },
    "gateway_id": {
        "name": "gateway_id",
        "type": "string",
        "default": "SPT_GW_001",
        "description": "网关唯一标识符"
    },
    "client_id": {
        "name": "client_id",
        "type": "string",
        "default": "acme_mqtt_client",
        "description": "MQTT客户端ID"
    },
    "keep_alive": {
        "name": "keep_alive",
        "type": "int",
        "default": 60,
        "description": "MQTT保活间隔(秒)"
    },
    "qos": {
        "name": "qos",
        "type": "int",
        "default": 1,
        "description": "MQTT消息质量等级(0-2)"
    },
    "thing_model_enabled": {
        "name": "thing_model_enabled",
        "type": "bool",
        "default": true,
        "description": "是否启用物模型功能"
    },
    "auto_discovery_enabled": {
        "name": "auto_discovery_enabled",
        "type": "bool",
        "default": true,
        "description": "是否启用自动发现"
    },
    "discovery_interval": {
        "name": "discovery_interval",
        "type": "int",
        "default": 30000,
        "description": "设备发现间隔(毫秒)"
    },
    "security_enabled": {
        "name": "security_enabled",
        "type": "bool",
        "default": false,
        "description": "是否启用安全认证"
    },
    "ca_cert_file": {
        "name": "ca_cert_file",
        "type": "string",
        "default": "/etc/ssl/certs/ca.pem",
        "description": "CA证书文件路径"
    },
    "device_cert_file": {
        "name": "device_cert_file",
        "type": "string",
        "default": "/etc/ssl/certs/device.pem",
        "description": "设备证书文件路径"
    },
    "device_key_file": {
        "name": "device_key_file",
        "type": "string",
        "default": "/etc/ssl/private/device.key",
        "description": "设备私钥文件路径"
    },
    "builtin_models": {
        "name": "builtin_models",
        "type": "array",
        "default": ["acme.device.fcm.v1", "acme.device.ecm.v1"],
        "description": "内置物模型列表"
    }
}
```

#### 物模型配置文件 (thing_models.json)
```json
{
    "thing_models": [
        {
            "model_id": "acme.device.fcm.v1",
            "config_file": "/opt/neuron/config/models/fcm_v1.json",
            "enabled": true,
            "auto_load": true
        },
        {
            "model_id": "acme.device.ecm.v1",
            "config_file": "/opt/neuron/config/models/ecm_v1.json",
            "enabled": true,
            "auto_load": true
        }
    ],
    "mapping_templates": [
        {
            "device_type": "FCM",
            "template_file": "/opt/neuron/config/mappings/fcm_mapping_template.json"
        },
        {
            "device_type": "ECM",
            "template_file": "/opt/neuron/config/mappings/ecm_mapping_template.json"
        }
    ]
}
```

#### 映射模板配置 (fcm_mapping_template.json)
```json
{
    "device_type": "FCM",
    "node_template": "{device_id}_node",
    "default_group": "default_group",
    "property_mappings": {
        "ONOFF": {
            "address_template": "0x01",
            "type": "BIT",
            "attribute": "RW",
            "description": "开关状态"
        },
        "STEMP": {
            "address_template": "0x10",
            "type": "FLOAT",
            "attribute": "RW",
            "description": "设定温度"
        },
        "RTEMP": {
            "address_template": "0x11",
            "type": "FLOAT",
            "attribute": "R",
            "description": "室内温度"
        },
        "SMODE": {
            "address_template": "0x02",
            "type": "UINT16",
            "attribute": "RW",
            "description": "运行模式"
        },
        "SFAN": {
            "address_template": "0x03",
            "type": "UINT16",
            "attribute": "RW",
            "description": "风速设置"
        }
    },
    "group_organization": {
        "control_group": {
            "properties": ["ONOFF", "STEMP", "SMODE", "SFAN"],
            "read_interval": 1000
        },
        "status_group": {
            "properties": ["RTEMP"],
            "read_interval": 2000
        }
    }
}
```

### 4. 部署步骤

#### 自动部署脚本
```bash
#!/bin/bash
# deploy.sh - 自动部署脚本

set -e

NEURON_HOME="/opt/neuron"
CONFIG_DIR="$NEURON_HOME/config"
PLUGIN_DIR="$NEURON_HOME/plugins"
LOG_DIR="/var/log/neuron"

echo "🚀 Deploying ACME MQTT Thing Model Plugin..."

# 检查权限
if [ "$EUID" -ne 0 ]; then
    echo "❌ Please run as root or with sudo"
    exit 1
fi

# 停止Neuron服务
echo "Stopping Neuron service..."
systemctl stop neuron || true

# 创建必要目录
echo "Creating directories..."
mkdir -p "$CONFIG_DIR/models"
mkdir -p "$CONFIG_DIR/mappings"
mkdir -p "$PLUGIN_DIR/schema"
mkdir -p "$LOG_DIR"

# 复制插件文件
echo "Installing plugin..."
cp build/libacme-mqtt-thing-model.so "$PLUGIN_DIR/"
cp schema/acme-mqtt.json "$PLUGIN_DIR/schema/"

# 复制配置文件
echo "Installing configuration files..."
cp config/thing_models.json "$CONFIG_DIR/"
cp config/models/*.json "$CONFIG_DIR/models/"
cp config/mappings/*.json "$CONFIG_DIR/mappings/"

# 设置权限
echo "Setting permissions..."
chown -R neuron:neuron "$NEURON_HOME"
chmod 755 "$PLUGIN_DIR/libacme-mqtt-thing-model.so"
chmod 644 "$CONFIG_DIR"/*.json
chmod 644 "$CONFIG_DIR/models"/*.json
chmod 644 "$CONFIG_DIR/mappings"/*.json

# 启动Neuron服务
echo "Starting Neuron service..."
systemctl start neuron

# 等待服务启动
sleep 5

# 验证部署
echo "Verifying deployment..."
if systemctl is-active --quiet neuron; then
    echo "✅ Neuron service is running"
else
    echo "❌ Neuron service failed to start"
    exit 1
fi

# 检查插件加载
if curl -s http://localhost:7000/api/v2/plugins | grep -q "acme-mqtt"; then
    echo "✅ Plugin loaded successfully"
else
    echo "❌ Plugin not found in plugin list"
    exit 1
fi

echo "🎉 Deployment completed successfully!"
echo ""
echo "Next steps:"
echo "1. Configure MQTT connection via Neuron web interface"
echo "2. Create MQTT application node"
echo "3. Configure device mappings"
echo "4. Test device communication"
```

这套完整的部署和配置指南提供了从环境准备到实际部署的全流程指导，确保物模型功能能够正确安装和运行。
