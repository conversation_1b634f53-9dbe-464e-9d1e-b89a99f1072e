/**
 * 测试 FCM 设备消息处理函数
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>

// 模拟的数据结构和函数
typedef struct {
    char node[64];
    char eui[20];   
    int  rsp;           
    int  len;          
    void * data;        
} neu_acme_dev_data_pop_t;

typedef struct {
    int modeType;
    char *eui;
} node_base_t;

typedef struct {
    char name[64];
} neu_plugin_t;

// 模拟的常量
#define LR_RSP_IOM_LP     0x30
#define LR_RSP_PTV_REP    0x27
#define IOM_FCM           1

// 模拟的日志函数
#define nlog_debug(fmt, ...) printf("[DEBUG] " fmt "\n", ##__VA_ARGS__)

// 模拟的消息处理函数声明
int business_lora_dev_message_handle(neu_plugin_t *plugin, node_base_t * dev, neu_acme_dev_data_pop_t *pInfo);
int fcm_dev_message_handle(neu_plugin_t *plugin, node_base_t * dev, neu_acme_dev_data_pop_t *pInfo);

// 简化的实现用于测试
int fcm_dev_message_handle(neu_plugin_t *plugin, node_base_t * dev, neu_acme_dev_data_pop_t *pInfo)
{
    if(plugin == NULL || dev == NULL || pInfo == NULL || pInfo->data == NULL){
        nlog_debug("parameter error !!!");
        return -1;
    }

    nlog_debug("FCM device:%s recv message rsp:0x%02x len:%d", 
               plugin->name, pInfo->rsp, pInfo->len);

    switch (pInfo->rsp)
    {
        case LR_RSP_IOM_LP:{
            nlog_debug("Processing IO status data");
            uint8_t *data = (uint8_t *)pInfo->data;
            int data_len = pInfo->len - 1;
            
            if(data_len < 20){
                nlog_debug("FCM IO status data length too short: %d", data_len);
                return -1;
            }

            // 解析空调数据
            uint16_t temperature = (data[0] << 8) | data[1];
            uint16_t set_temperature = (data[2] << 8) | data[3];
            uint8_t mode = data[4];
            uint8_t power_switch = data[9];
            uint16_t humidity = (data[15] << 8) | data[16];
            
            nlog_debug("Parsed data - temp:%d.%d°C, set_temp:%d.%d°C, mode:%d, switch:%d, humidity:%d.%d%%", 
                       temperature/10, temperature%10, set_temperature/10, set_temperature%10, 
                       mode, power_switch, humidity/10, humidity%10);
            break;
        }
        case LR_RSP_PTV_REP:{
            nlog_debug("Processing point value data");
            break;
        }
        default:
            nlog_debug("FCM device unsupported message type: 0x%02x", pInfo->rsp);
            return -1;
    }

    return 0;
}

int business_lora_dev_message_handle(neu_plugin_t *plugin, node_base_t * dev, neu_acme_dev_data_pop_t *pInfo)
{
    if(plugin == NULL || dev == NULL || pInfo == NULL){
        nlog_debug("parameter error !!!");
        return -1;
    }

    nlog_debug("node:%s modeType:%d recv message rsp:0x%02x len:%d", 
               plugin->name, dev->modeType, pInfo->rsp, pInfo->len);

    switch (dev->modeType)
    {
        case IOM_FCM:{
            return fcm_dev_message_handle(plugin, dev, pInfo);
        }
        default:
            nlog_debug("Unknown device type: %d", dev->modeType);
            return -1;
    }
}

int main()
{
    printf("=== FCM Message Handle Test ===\n");
    
    // 创建测试数据
    neu_plugin_t plugin = {0};
    strcpy(plugin.name, "test_fcm_device");
    
    node_base_t dev = {0};
    dev.modeType = IOM_FCM;
    dev.eui = "1234567890ABCDEF";
    
    // 模拟空调数据：00 f0 00 a0 04 00 14 03 01 00 00 00 00 01 00 00 01 ff ff ff
    uint8_t test_data[] = {
        0x00, 0xf0, 0x00, 0xa0, 0x04, 0x00, 0x14, 0x03, 0x01, 0x00, 
        0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0xff, 0xff, 0xff
    };
    
    neu_acme_dev_data_pop_t pInfo = {0};
    strcpy(pInfo.node, "test_fcm_device");
    strcpy(pInfo.eui, "1234567890ABCDEF");
    pInfo.rsp = LR_RSP_IOM_LP;
    pInfo.len = sizeof(test_data) + 1; // +1 for RSP byte
    pInfo.data = test_data;
    
    // 测试消息处理
    printf("\n--- Test 1: Valid FCM IO Status Message ---\n");
    int result = business_lora_dev_message_handle(&plugin, &dev, &pInfo);
    printf("Result: %s\n", result == 0 ? "SUCCESS" : "FAILED");
    
    // 测试无效消息类型
    printf("\n--- Test 2: Invalid Message Type ---\n");
    pInfo.rsp = 0x99; // 无效的消息类型
    result = business_lora_dev_message_handle(&plugin, &dev, &pInfo);
    printf("Result: %s\n", result == -1 ? "EXPECTED FAILURE" : "UNEXPECTED SUCCESS");
    
    // 测试数据长度不足
    printf("\n--- Test 3: Insufficient Data Length ---\n");
    pInfo.rsp = LR_RSP_IOM_LP;
    pInfo.len = 10; // 数据长度不足
    result = business_lora_dev_message_handle(&plugin, &dev, &pInfo);
    printf("Result: %s\n", result == -1 ? "EXPECTED FAILURE" : "UNEXPECTED SUCCESS");
    
    printf("\n=== Test Completed ===\n");
    return 0;
}
