#ifndef _UTIL_H_
#define _UTIL_H_

#include "unistd.h"
#include <stdlib.h>
#include <stdio.h>
#include <time.h>
#include <errno.h>
#include <sys/socket.h>
//#include <sys/ioctl.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <net/if.h>
#include <string.h>
#include <assert.h>
#include <regex.h>
#include <stdbool.h>
#include <netdb.h>

#define TIME_MAXLEN 20


typedef struct CPU_PACKED      //定义一个cpu occupy的结构体
{
    char name[20];             //定义一个char类型的数组名name有20个元素
    unsigned int user;         //定义一个无符号的int类型的user
    unsigned int nice;         //定义一个无符号的int类型的nice
    unsigned int system;       //定义一个无符号的int类型的system
    unsigned int idle;         //定义一个无符号的int类型的idle
    unsigned int iowait;
    unsigned int irq;
    unsigned int softirq;
}CPU_OCCUPY;


typedef struct MEM_PACKED      //定义一个mem occupy的结构体
{
        char name[20];         //定义一个char类型的数组名name有20个元素
        unsigned long total;
        char name2[20];
}MEM_OCCUPY;


typedef struct MEM_PACK        //定义一个mem occupy的结构体
{
        double total,used_rate;
}MEM_PACK;



/*
要点提示:
1. float和unsigned long具有相同的数据结构长度
2. union据类型里的数据存放在相同的物理空间
*/
typedef union
{
	float fdata;
	unsigned long ldata;
}FloatLongType;



typedef void (*sighandler_t)(int);
int ts_to_time(const time_t* ts, char* time);
int time_now(char* time);
//int get_mac(const char* dev, unsigned char* mac, int len);
//int get_mac_formatted(const char* dev, char* mac_formatted, char c_format);
//int mac_formatted(const unsigned char* raw_mac, char* mac_formatted, char c_format);
//int get_configmac(char* mac);
int is_file_exist(const char *file_path);
int create_dir(const char *sPathName);

int read_file(const char *fileName, int  read_len, char *filebuf);
int write_file(const char *fileName, const char *filebuf);
int system_popen(const char *cmd, char mode, char *result, char *data);
int system_shell(const char *cmd);
char * l_trim(char * strOutput, const char *strInput);
char *r_trim(char *strOutput, const char *strInput);
char * a_trim(char * szOutput, const char * strInput);
int strToUpper(char *str);
int strToLower(char *str);
char * strsep(char **stringp, const char *delim);
char *strnstr(const char *s1, const char *s2, size_t len);
char* memstr(char* mem_data, int mem_data_len, char* substr);
char* strrev(char* s);
//void urldecode(char *p);

//extern int get_ip(char* eth, int nLen, struct in_addr* ip);
void char2str(char n, char *str);

//void strrpl(char *str);
//bool regex_match(const char *pattern, const char *text);
char *getIPbyhost(const char *host, char *ip_addr);
//extern int des_encrypt(unsigned char *data, int data_len, unsigned char *key_str, unsigned char *ivec_str, int mode, unsigned char *buf);
void hexdump_printf(const char* descrip, char* src, int nByte, uint8_t com);
void Hex2Str( const char *sSrc,  char *sDest, int nSrcLen);
void MESH_Hex2Str( const char *sSrc,  char *sDest, int nSrcLen );

char * float2str(float val, int precision, char *buf);
double get_CpuRate();
MEM_PACK *get_memoccupy();    // get RAM message

void Float_to_Byte(float f,unsigned char byte[]);
void Float_to_Byte_Reverse(float f,unsigned char byte[]); //高位在前

void Byte_to_Float(float *f,unsigned char byte[]);
void Byte_to_Float_Reverse(float *f,unsigned char byte[]);

int StringToBuff(char *str,unsigned char *OutputBuff);
int ArrayToStr(unsigned char *Buff, unsigned int BuffLen, char *OutputStr);
char *str_to_lower(char *instr);
char *strupr(char *str);
char* GetIOStateToggle(int state);
char* GetIOState(int state);
int GetIOValToggle(int val);
int GetIOVal(int val);
int GetOnOffStateToggle(char *state);
int GetOnOffState(char *state);
void LeftTwoDecimal(float fdata,char *buf);
void split(char *src, const char *separator, char **dest, int *num);
char* itoa(int num,char* str,int radix);
void set_current_time_to_string(char *timestamp_now);


int ArrayToStr_upr(unsigned char *Buff, unsigned int BuffLen, char *OutputStr);
uint8_t checksum(char *buff,int len);
void GetFileName(char *path, char *filename);

int getBinSize(char *path);
int readBin(char *path, char *buf, int size);
int ip_to_int(const char *str);

#endif
