#ifndef NEURON_PLUGIN_ACME_LORA_CMD_H
#define NEURON_PLUGIN_ACME_LORA_CMD_H

#ifdef __cplusplus
extern "C" {
#endif

#define LR_RSP_LOCK_AIR   0x0d
#define LR_RSP_MBUS_RTU   0x26
#define LR_RSP_PTV_REP    0x27   //Lora采集点值上报
#define LR_RSP_IOM_LP     0x30
#define LR_RSP_485_CTL    0x31
#define LR_RSP_CLEAR      0x32
#define LR_RSP_DO_CTL     0x33
#define LR_RSP_AO_CTL     0x34
#define LR_RSP_LORA_CTL   0x35
#define LR_RSP_HBUS_HX    0x36
#define LR_RSP_IO_TYPE    0x37
#define LR_RSP_CO_CTL     0x38
#define LR_RSP_IR_LIBKEY  0x39   //红外录制键库操作
#define LR_RSP_IR_CAPTUE  0x40   //红外开始录制键值
#define LR_RSP_MBUS_TCP   0x41
#define LR_RSP_SIEMENS_S7 0x42
#define LR_RSP_SOFT_PT    0x43
#define LR_RSP_GROUP_CTL  0x46


#define LR_RSP_CFG_CTL    0xcf
#define LR_IOM_GET_PTL    0xc1   //get point list
#define LR_PT_PRO_FLAG    0xc2   //点属性标志
#define LR_IN_SEARCHING   0xaa
#define LR_IOM_ACQ_IN     0xa1
#define LR_IOM_ACQ_UPD    0xa2
#define LR_UPD_SER_STA    0xa3  //升级服务启用
#define LR_BROAD_REP_DIS  0xa4  //广播升级忙等待 子模块收到停止lora发送数据直到超时时间到；
#define LR_IN_UPDATE_STA  0xa5  //升级状态上报
#define LR_BROAD_CLR_PTS  0xd1  //广播点表变动
#define LR_SYNS_SYS_TIME  0xc6  //广播系统时间

#define LR_MOD_DEV_SEARCH 0xd2 //模块下挂设备搜索
#define LR_IOM_COM_CFG    0xd3 //tx: iom 通信端口配置 rx:获取iom 通信端口配置
#define LR_IOM_DEBUG_LOG  0xde //调试链路log
#define LR_DIFF_UPGRADE   0xdd //差量升级

#define LR_IOM_CFG_SYNC   0xce //设备上电收到版本号后，配置下发给模块
#define LR_IOM_REBOOT     0xb0 //重启设备模块

#define LR_FBOOT_COMM     0xfb //.fboot 文件通信
#define LR_CSV_COMM       0xfc //.csv 文件通信
#define LR_VERSION_GET    0xf1 //版本查询
#define LR_GROUP_ID_GET   0xf2 //组ID列表获取
#define LR_WARN_ERR_CODE  0xe1 //0x01 掉电报警  0x10 组控制成功  0x11组控制失败
#define LR_UPDAT_ERR_STA  0xe2 //升级过程故障码上报

#define LR_SEND_SUCC_ACK  0xcc //lora 发送成功响应报文
#define LR_GROUP_ID_CFG   0xc9 //群控id配置

#define BACNET_COV_FLAG   10

#define WAVE_MESH_MASTER  1

#define UPD_MODULE_SEGNUM  64
#define UPD_SERV_TIMEOUT   600

#define mWRITE_SINGLE_COIL         0x05
#define mWRITE_SINGLE_REGISTER     0x06
#define mWRITE_MULTIPLE_COILS      0x0F
#define mWRITE_MULTIPLE_REGISTERS  0x10

#define RESUME_ALL   1
#define RESUME_IP    2

#define PAIR_PAR_CHECK       50
#define PAIR_PAR_CHECK_EXCP  254
#define PAIR_PAR_CHECK_DONE  255

#ifdef __cplusplus
}
#endif

#endif
