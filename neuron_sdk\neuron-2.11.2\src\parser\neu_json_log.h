/**
 * NEURON IIoT System for Industry 4.0
 * Copyright (C) 2020-2022 EMQ Technologies Co., Ltd All rights reserved.
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 3 of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with this program; if not, write to the Free Software Foundation,
 * Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.
 **/

/*
 * DO NOT EDIT THIS FILE MANUALLY!
 * It was automatically generated by `json-autotype`.
 */

#ifndef _NEU_JSON_API_NEU_JSON_LOG_H_
#define _NEU_JSON_API_NEU_JSON_LOG_H_

#include "json/json.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef char *neu_json_get_log_resp_row_t;

typedef struct {
    char *node_name;
    char *log_level;
    bool  core;
} neu_json_update_log_level_req_t;

typedef struct {
    int64_t                      page_count;
    int                          n_row;
    neu_json_get_log_resp_row_t *rows;
    int64_t                      error;
} neu_json_get_log_resp_t;

typedef struct {
    char file[128];
    int  size;
} neu_resp_log_file_t;

void neu_json_decode_update_log_level_req_free(
    neu_json_update_log_level_req_t *req);
int neu_json_decode_update_log_level_req(
    char *buf, neu_json_update_log_level_req_t **result);
int neu_json_encode_log_list_resp(void *json_object, void *param);

#ifdef __cplusplus
}
#endif

#endif
