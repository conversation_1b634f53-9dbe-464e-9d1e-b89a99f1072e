# ACME_MQTT 架构图表集合

## 1. ACME_MQTT模块架构设计图

```mermaid
graph TB
    subgraph "ACME_MQTT 模块架构"
        subgraph "插件接口层"
            A1[mqtt_plugin_intf.c]
            A2[生命周期管理]
            A3[配置管理]
            A4[消息路由]
        end
        
        subgraph "业务处理层"
            B1[mqtt_handle.c]
            B2[数据处理]
            B3[JSON编码]
            B4[主题管理]
            B5[handle_acme_mqtt_data_push]
        end
        
        subgraph "协议适配层"
            C1[acme_lora_protocol.c]
            C2[acme_modbus_tcp_protocol.c]
            C3[acme_protocol_comman.c]
        end
        
        subgraph "连接管理层"
            D1[connect_server.c]
            D2[服务器连接]
            D3[认证管理]
            D4[加密通信]
        end
        
        subgraph "配置管理层"
            E1[mqtt_config.c]
            E2[参数解析]
            E3[配置验证]
            E4[动态更新]
        end
        
        subgraph "MQTT客户端层"
            F1[Neuron SDK MQTT Client]
            F2[连接管理]
            F3[消息发布]
            F4[QoS控制]
        end
    end
    
    subgraph "外部接口"
        G1[acme_lora 驱动]
        G2[Web配置界面]
        G3[MQTT云端服务器]
    end
    
    %% 数据流连接
    G1 -->|NEU_REQ_ACME_MQTT_DATA_PUSH| A1
    G2 -->|配置更新| A3
    A1 --> B1
    B1 --> B5
    B5 --> F1
    F1 --> G3
    
    %% 内部连接
    A1 --> A2
    A1 --> A3
    A1 --> A4
    B1 --> B2
    B1 --> B3
    B1 --> B4
    B1 --> C1
    B1 --> C2
    B1 --> C3
    D1 --> D2
    D1 --> D3
    D1 --> D4
    E1 --> E2
    E1 --> E3
    E1 --> E4
    F1 --> F2
    F1 --> F3
    F1 --> F4
    
    %% 样式
    classDef interface fill:#e1f5fe
    classDef business fill:#f3e5f5
    classDef protocol fill:#e8f5e8
    classDef connect fill:#fff3e0
    classDef config fill:#fce4ec
    classDef mqtt fill:#f1f8e9
    classDef external fill:#ffebee
    
    class A1,A2,A3,A4 interface
    class B1,B2,B3,B4,B5 business
    class C1,C2,C3 protocol
    class D1,D2,D3,D4 connect
    class E1,E2,E3,E4 config
    class F1,F2,F3,F4 mqtt
    class G1,G2,G3 external
```

## 2. ACME_MQTT数据流程图

```mermaid
sequenceDiagram
    participant FCM as FCM设备
    participant LoRa as acme_lora驱动
    participant MQTT as acme_mqtt插件
    participant Cloud as MQTT云端服务器
    
    Note over FCM,Cloud: ACME_MQTT 数据传输流程
    
    %% 设备数据上报
    FCM->>LoRa: 0x30空调状态数据
    Note right of LoRa: 30秒周期上报
    
    LoRa->>LoRa: 协议解析
    LoRa->>LoRa: 点位更新
    
    %% 直接推送机制
    LoRa->>MQTT: NEU_REQ_ACME_MQTT_DATA_PUSH
    Note right of MQTT: 直接推送，绕过订阅
    
    MQTT->>MQTT: 参数验证
    MQTT->>MQTT: MQTT客户端状态检查
    
    alt MQTT连接正常
        MQTT->>MQTT: 构造JSON数据
        Note right of MQTT: {"timestamp":..., "driver":"FCM_001", "tags":[...]}
        
        MQTT->>MQTT: 生成主题
        Note right of MQTT: /neuron/SPT_GW_001/acme/1234567890ABCDEF/data
        
        MQTT->>Cloud: MQTT Publish (QoS1)
        Cloud-->>MQTT: PUBACK
        
        MQTT->>MQTT: 更新统计指标
        Note right of MQTT: 传输成功计数
        
    else MQTT连接断开
        MQTT->>MQTT: 数据缓存
        Note right of MQTT: 等待重连后发送
    end
    
    MQTT->>MQTT: 资源清理
    Note right of MQTT: 释放内存，防止泄漏
    
    %% 传统订阅模式对比
    Note over FCM,Cloud: 传统订阅模式 (对比)
    LoRa->>LoRa: 定时器触发
    LoRa->>MQTT: 订阅组数据
    MQTT->>Cloud: MQTT Publish
    Note right of Cloud: 延迟更高，资源消耗更大
```

## 3. SPT网关整体架构图

```mermaid
graph TB
    subgraph "SPT网关系统架构"
        subgraph "Web前端层"
            W1[Web配置界面]
            W2[实时监控]
            W3[设备管理]
        end
        
        subgraph "HTTP API层"
            H1[RESTful API]
            H2[认证授权]
            H3[数据接口]
        end
        
        subgraph "Neuron核心引擎"
            subgraph "北向应用层"
                N1[ACME_MQTT插件]
                N2[Web_Server模块]
                N3[数据缓存]
            end
            
            subgraph "南向驱动层"
                S1[ACME_GW插件]
                S2[ACME_LoRa插件]
                S3[设备管理器]
            end
        end
        
        subgraph "硬件抽象层"
            HAL1[LoRa串口]
            HAL2[网络接口]
            HAL3[GPIO]
            HAL4[存储]
        end
        
        subgraph "外部系统"
            EXT1[FCM空调设备]
            EXT2[MQTT云端服务器]
            EXT3[管理平台]
        end
    end
    
    %% 连接关系
    W1 --> H1
    W2 --> H1
    W3 --> H1
    H1 --> N2
    H2 --> N2
    H3 --> N2
    
    N1 --> EXT2
    N2 --> H1
    S1 --> HAL1
    S2 --> S1
    S3 --> S1
    
    HAL1 --> EXT1
    HAL2 --> EXT2
    HAL2 --> EXT3
    
    %% 数据流
    EXT1 -.->|LoRa数据| HAL1
    HAL1 -.-> S1
    S1 -.-> S2
    S2 -.->|直接推送| N1
    N1 -.-> EXT2
    
    %% 样式
    classDef web fill:#e3f2fd
    classDef api fill:#f3e5f5
    classDef north fill:#e8f5e8
    classDef south fill:#fff3e0
    classDef hal fill:#fce4ec
    classDef external fill:#ffebee
    
    class W1,W2,W3 web
    class H1,H2,H3 api
    class N1,N2,N3 north
    class S1,S2,S3 south
    class HAL1,HAL2,HAL3,HAL4 hal
    class EXT1,EXT2,EXT3 external
```

## 4. 内存监控架构图

```mermaid
graph LR
    subgraph "内存监控系统"
        subgraph "监控组件"
            M1[memory_monitor.c]
            M2[RSS内存监控]
            M3[泄漏检测]
            M4[统计分析]
        end
        
        subgraph "关键检查点"
            C1[acme_lora_msg_recv]
            C2[fcm_dev_message_handle]
            C3[loraRecvIoStatusHandle]
            C4[handle_acme_mqtt_data_push]
        end
        
        subgraph "分析工具"
            A1[analyze_memory.sh]
            A2[verify_lora_memory_fix.sh]
            A3[内存趋势分析]
            A4[泄漏报告生成]
        end
    end
    
    subgraph "监控输出"
        O1[内存监控日志]
        O2[RSS使用趋势]
        O3[泄漏检测报告]
        O4[性能分析报告]
    end
    
    %% 连接关系
    M1 --> M2
    M1 --> M3
    M1 --> M4
    
    C1 --> M1
    C2 --> M1
    C3 --> M1
    C4 --> M1
    
    A1 --> O1
    A2 --> O2
    A3 --> O3
    A4 --> O4
    
    M2 --> O1
    M3 --> O3
    M4 --> O4
    
    %% 样式
    classDef monitor fill:#e1f5fe
    classDef check fill:#f3e5f5
    classDef tool fill:#e8f5e8
    classDef output fill:#fff3e0
    
    class M1,M2,M3,M4 monitor
    class C1,C2,C3,C4 check
    class A1,A2,A3,A4 tool
    class O1,O2,O3,O4 output
```

## 使用说明

### 导出为图片的步骤：

1. **在线导出**：
   - 访问 https://mermaid.live/
   - 复制上述代码到编辑器
   - 点击 "Actions" → "Download PNG"

2. **VS Code导出**：
   - 安装 "Mermaid Preview" 插件
   - 新建 .md 文件，粘贴代码
   - 右键选择导出图片

3. **命令行导出**：
   ```bash
   # 安装工具
   npm install -g @mermaid-js/mermaid-cli
   
   # 导出图片
   mmdc -i architecture.mmd -o architecture.png -w 1920 -H 1080
   ```

### 建议的图片尺寸：
- **架构图**: 1920x1080 (高清)
- **流程图**: 1600x900 (宽屏)
- **简单图表**: 1200x800 (标准)

这样您就可以获得高质量的架构图和流程图图片文件了！
