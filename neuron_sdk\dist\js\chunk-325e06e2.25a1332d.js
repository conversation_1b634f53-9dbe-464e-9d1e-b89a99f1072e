(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-325e06e2"],{"064a":function(e,t,n){"use strict";n("4354")},4354:function(e,t,n){},"5a70":function(e,t,n){"use strict";n.r(t);var r=n("7a23"),a=n("9978"),c=n("1da1"),o=(n("96cf"),n("d3b7"),n("25f0"),n("a434"),n("fb6a"),n("d89f")),u=n("6c02"),i=n("d472"),s=n("47e2"),l=n("73ec"),d=n("b3bd"),b=n("8c45"),f=function(){var e=Object(u["c"])(),t=Object(u["d"])(),n=Object(s["b"])(),a=n.t,f=Object(d["a"])(),g=f.createRawTagForm,m=Object(d["b"])(),p=m.groupName,j=m.parseTagData,O=m.handleValidTagFormError,v=Object(r["ref"])([]),h=Object(r["ref"])([g()]),w=Object(r["ref"])({tagList:h.value}),x=Object(r["ref"])(!1),k=Object(r["computed"])((function(){return e.params.node.toString()})),C=Object(b["a"])(),N=C.nodePluginInfo,T=C.getNodePluginInfo,R=Object(r["ref"])(),V=function(e){e&&v.value.push(e)},y=function(){w.value.tagList.push(g())},D=function(e){w.value.tagList.splice(e,1)},I=function(e,t){0!==e?(i["EmqxMessage"].error(a("config.tagPartAddedFailedPopup",[Object(l["i"])(t)])),w.value.tagList=w.value.tagList.slice(e)):2405===t?i["EmqxMessage"].error(a("error.addTagByNode2405")):Object(l["o"])(t)},P=function(){var e=Object(c["a"])(regeneratorRuntime.mark((function e(){var t,n,r;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,j(w.value.tagList);case 3:return t=e.sent,e.next=6,Object(o["g"])({tags:t,node:k.value,group:p.value});case 6:return e.abrupt("return",Promise.resolve());case 9:return e.prev=9,e.t0=e["catch"](0),n=e.t0.data,r=void 0===n?{}:n,0!==r.error&&void 0!==r.index&&I(r.index,r.error),e.abrupt("return",Promise.reject(e.t0));case 14:case"end":return e.stop()}}),e,null,[[0,9]])})));return function(){return e.apply(this,arguments)}}(),S=function(){var e=Object(c["a"])(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,R.value.validate();case 3:return e.abrupt("return",Promise.resolve());case 6:return e.prev=6,e.t0=e["catch"](0),O(e.t0),e.abrupt("return",Promise.reject(e.t0));case 10:case"end":return e.stop()}}),e,null,[[0,6]])})));return function(){return e.apply(this,arguments)}}(),E=function(){var e=Object(c["a"])(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,x.value=!0,e.next=4,S();case 4:return e.next=6,P();case 6:i["EmqxMessage"].success(a("common.createSuccess")),t.push({name:"SouthDriverGroupTag"}),e.next=13;break;case 10:e.prev=10,e.t0=e["catch"](0),console.error(e.t0);case 13:return e.prev=13,x.value=!1,e.finish(13);case 16:case"end":return e.stop()}}),e,null,[[0,10,13,16]])})));return function(){return e.apply(this,arguments)}}(),F=function(){t.back()};return T(),{nodePluginInfo:N,formData:w,isSubmitting:x,createRawTagForm:g,addTagItem:y,deleteTagItem:D,tagFormRef:R,setFormRef:V,cancel:F,submit:E}},g={class:"add-tag"},m={class:"card-title"},p=Object(r["createElementVNode"])("i",{class:"iconfont iconcreate"},null,-1),j=Object(r["defineComponent"])({setup:function(e){var t=f(),n=t.nodePluginInfo,c=t.formData,o=t.isSubmitting,u=t.addTagItem,i=t.deleteTagItem,s=t.tagFormRef,l=t.cancel,d=t.submit;return function(e,t){var b=Object(r["resolveComponent"])("emqx-card"),f=Object(r["resolveComponent"])("emqx-button");return Object(r["openBlock"])(),Object(r["createElementBlock"])("div",g,[Object(r["createVNode"])(b,{shadow:"none"},{default:Object(r["withCtx"])((function(){return[Object(r["createElementVNode"])("h3",m,Object(r["toDisplayString"])(e.$t("config.addTags")),1)]})),_:1}),Object(r["createVNode"])(b,{shadow:"none"},{default:Object(r["withCtx"])((function(){return[Object(r["createVNode"])(a["a"],{ref:function(e,t){t["tagFormRef"]=e,Object(r["isRef"])(s)&&(s.value=e)},data:Object(r["unref"])(c),"node-plugin-info":Object(r["unref"])(n),onDeleteTagItem:Object(r["unref"])(i)},null,8,["data","node-plugin-info","onDeleteTagItem"]),Object(r["createVNode"])(f,{class:"btn-add-tag",onClick:Object(r["unref"])(u)},{default:Object(r["withCtx"])((function(){return[p,Object(r["createElementVNode"])("span",null,Object(r["toDisplayString"])(e.$t("common.add")),1)]})),_:1},8,["onClick"])]})),_:1}),Object(r["createVNode"])(b,{shadow:"none",class:"footer add-tag-ft"},{default:Object(r["withCtx"])((function(){return[Object(r["createVNode"])(f,{type:"primary",onClick:Object(r["unref"])(d),disabled:0===Object(r["unref"])(c).tagList.length,loading:Object(r["unref"])(o)},{default:Object(r["withCtx"])((function(){return[Object(r["createTextVNode"])(Object(r["toDisplayString"])(e.$t("common.create")),1)]})),_:1},8,["onClick","disabled","loading"]),Object(r["createVNode"])(f,{onClick:Object(r["unref"])(l)},{default:Object(r["withCtx"])((function(){return[Object(r["createTextVNode"])(Object(r["toDisplayString"])(e.$t("common.cancel")),1)]})),_:1},8,["onClick"])]})),_:1})])}}});n("064a");const O=j;t["default"]=O}}]);