#include <nng/nng.h>
#include <nng/supplemental/http/http.h>
#include <jansson.h>
#include <memory.h>
#include <signal.h>
#include <stdbool.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "neuron.h"
#include "mqtt_config.h"
#include "mqtt_plugin_intf.h"
#include "acme_mqtt_plugin.h"
#include "connect_comman.h"

extern const neu_plugin_module_t neu_plugin_module;

acme_plugin_t *acme_plugin = NULL;          //业务模块对象

/*
* 打印json 内容
*/
int acme_print_json(void *json_value)
{
    if(json_value == NULL) return -1;

    char *printStr = NULL; 
    neu_json_encode(json_value, &printStr);           
    nlog_debug("^_^ acme_print_json value string:%s",printStr);
    free(printStr);
    return 0;
}

/*
* 获取适配器配置信息，设计 MQTT 的参数配置（适配器相关数据插件无法直接通过指针拿，只能通过nng 消息）
*/
int get_node_setting_req(neu_plugin_t *plugin)
{
    if(plugin == NULL) return -1;

    int                        ret                          = 0;
    neu_reqresp_head_t         header                       = { 0 };
    neu_req_get_node_setting_t cmd                          = { 0 };

    header.ctx             = plugin;
    header.type            = NEU_REQ_GET_NODE_SETTING;
    //header.otel_trace_type = NEU_OTEL_TRACE_TYPE_REST_COMM;
    strcpy(cmd.node, plugin->common.name);

    ret = neu_plugin_op(plugin, header, &cmd);
    if (ret != 0) {
        nlog_debug("get_adapter_setting msg send error.");
        return -1;
    }
    return 0;
}

/*
*  刷新ACME MQTT 配置参数
*  适配器返回的 MQTT 配置参数json ，直接复用内存空间 不释放
*/
int acme_mqtt_setting_set(void *new_setting_json)
{
    if(new_setting_json == NULL) return -1;

    pthread_mutex_lock(&acme_plugin->mutex);
    if(acme_plugin->mqtt_setting != NULL){
        neu_json_decode_free(acme_plugin->mqtt_setting);
    }
    acme_plugin->mqtt_setting = new_setting_json;
    
    //打印json 结构参数内容
    acme_print_json(acme_plugin->mqtt_setting);

    pthread_mutex_unlock(&acme_plugin->mutex);

    return 0;
}

/*
*  获取 ACME MQTT 配置参数
*  返回json 配置内容体，使用者需要释放
*/
void * acme_mqtt_setting_get()
{
    if(acme_plugin == NULL || acme_plugin->mqtt_setting == NULL) { nlog_debug("error: acme parameter is null."); return NULL;}

    json_t * setting = NULL;
    pthread_mutex_lock(&acme_plugin->mutex);
    setting = json_deep_copy(acme_plugin->mqtt_setting);
    pthread_mutex_unlock(&acme_plugin->mutex);

    return setting;
}

/*
* http 服务器响应新的 MQTT 登录参数，修改本地mqtt setting 配置
*/
int acme_mqtt_setting_modify(char *username, char *password, char *mqttAddr, int port , char *time)
{
    int ret = 0;
    if(username == NULL || password == NULL || mqttAddr == NULL) return -1;
    pthread_mutex_lock(&acme_plugin->mutex);
    
    void *json_setting = acme_plugin->mqtt_setting;
    if(json_setting == NULL){
        nlog_debug("[error] acme_plugin mqtt_setting info null.");        
        return -1;
    }

    json_t *params = json_object_get(json_setting, "params");
    if (!json_is_object(params)) {
        fprintf(stderr, "params不是对象\n");
        json_decref(json_setting);
        return 1;
    }

    json_object_set_new(params,"username",json_string(username));
    json_object_set_new(params,"password",json_string(password));
    json_object_set_new(params,"host",json_string(mqttAddr));
    json_object_set_new(params,"port",json_integer(port));

    //打印json 结构参数内容
    acme_print_json(json_setting);

    pthread_mutex_unlock(&acme_plugin->mutex);
    return 0;
}

/*
* 响应查询node setting 消息
*/
void * get_node_setting_resp(neu_plugin_t *plugin, neu_resp_get_node_setting_t *setting)
{
    int ret = 0;
    /*neu_json_get_node_setting_resp_t resp     = {
        .node    = setting->node,
        .setting = setting->setting,
    };*/

    nlog_debug("*** get setting content:%s ***",setting->setting);   

    void *object = neu_json_encode_new();
    neu_json_load_key(object, "params", setting->setting, true);

    //ret = neu_json_encode_get_node_setting_resp(object ,&resp);

    if (0 != ret) {
        //TODO 解析失败:
        nlog_debug("setting encode json error.");
    } else {
        //获取成功
        nlog_debug("setting encode json ok.");        
        acme_mqtt_setting_set(object);           
    }

    free(setting->setting);
    //free(json_str);

    return NULL;
}


/*
* 请求适配器进行 插件start 命令
*/
int acme_mqtt_start_req()
{
    pthread_mutex_lock(&acme_plugin->mutex);
    acme_plugin->mqtt_start = true;
    pthread_mutex_unlock(&acme_plugin->mutex);

    /* 目前插件暂不支持 start 接口，后续可在 core 添加私有命令，在插件中实现适配器的相关命令实现即可
    neu_reqresp_head_t     header = { 0 };
    neu_req_node_setting_t cmd    = { 0 };

    header.ctx  = acme_info->plugin;
    header.type = NEU_REQ_NODE_SETTING;
    strcpy(cmd.node, acme_info->plugin->common.name);

    void * setting_json = acme_mqtt_setting_get();
    neu_json_encode(setting_json, &cmd.setting); 
    neu_json_encode_free(setting_json);
    
    nlog_debug("---> acme mqtt  send setting msg.%s",cmd.setting);
    ret  = neu_plugin_op(acme_info->plugin, header, &cmd);
    nlog_debug("acme mqtt  send setting msg ok.");
*/

    return 0;
}

/*
* 断开 MQTT 连接
*/
int acme_mqtt_stop_req()
{
    pthread_mutex_lock(&acme_plugin->mutex);
    acme_plugin->mqtt_start = false;
    pthread_mutex_unlock(&acme_plugin->mutex);
    return 0;
}

/*
* 获取 MQTT 启动标记
*/
bool acme_mqtt_get_status()
{
    return acme_plugin->mqtt_start; 
}

/*
* 业务单元初始化
*/
acme_plugin_t * acme_process_init(neu_plugin_t *plugin)
{
    if(plugin == NULL) return NULL;
    
    acme_plugin = (acme_plugin_t *)calloc(1, sizeof(acme_plugin_t));
    if(acme_plugin == NULL) { nlog_debug("acme_plugin calloc error!");  return NULL;}

    acme_plugin->plugin = plugin;
    acme_plugin->module = &neu_plugin_module;
    acme_plugin->mqtt_setting = NULL;
    acme_plugin->mqtt_start = false;
    pthread_mutex_init(&acme_plugin->mutex, NULL);

    nlog_debug("acme_process_init init ok.");

    return acme_plugin;
}

