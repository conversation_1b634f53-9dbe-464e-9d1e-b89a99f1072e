/**
 * 测试 FCM 设备通过 adapter 回调函数获取组信息并更新点位的功能
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <stdbool.h>

// 模拟的数据结构
typedef struct {
    char *name;
    char *address;
    int type;
} mock_tag_t;

typedef struct {
    char name[64];
    int tag_count;
    int interval;
    char *description;
} mock_group_info_t;

typedef struct {
    mock_group_info_t *groups;
    int group_count;
    mock_tag_t *tags;
    int tag_count;
} mock_adapter_t;

typedef struct {
    mock_adapter_t *adapter;
} mock_plugin_common_t;

typedef struct {
    mock_plugin_common_t common;
} mock_plugin_t;

// 模拟的回调函数结构
typedef struct {
    void * (*get_groups)(mock_adapter_t *adapter);
} mock_adapter_callbacks_t;

// 模拟的 FCM 地址映射
#define ACME_FCM_ADDR_ONOFF    "1"    // 开关
#define ACME_FCM_ADDR_STEMP    "2"    // 设置温度
#define ACME_FCM_ADDR_SMODE    "3"    // 设置模式
#define ACME_FCM_ADDR_WSPED    "4"    // 设置风速
#define ACME_FCM_ADDR_RTEMP    "5"    // 读取温度
#define ACME_FCM_ADDR_ERROC    "6"    // 错误码
#define ACME_FCM_ADDR_HUMID    "7"    // 湿度

// 模拟的日志函数
#define nlog_debug(fmt, ...) printf("[DEBUG] " fmt "\n", ##__VA_ARGS__)

// 模拟的 get_groups 回调函数
void *mock_get_groups(mock_adapter_t *adapter)
{
    if(adapter == NULL) {
        nlog_debug("adapter is NULL");
        return NULL;
    }
    
    nlog_debug("get_groups callback called, returning %d groups", adapter->group_count);
    return adapter->groups;
}

// 模拟的点位更新函数
int mock_update_tag_by_address_with_callback(mock_plugin_t *plugin, mock_adapter_t *adapter, 
                                             const char *address, int tag_type, float value)
{
    if(plugin == NULL || adapter == NULL || address == NULL) {
        nlog_debug("Invalid parameters for tag update");
        return -1;
    }
    
    // 通过回调函数获取组信息
    mock_adapter_callbacks_t callbacks = {
        .get_groups = mock_get_groups
    };
    
    mock_group_info_t *groups = (mock_group_info_t *)callbacks.get_groups(adapter);
    if(groups == NULL) {
        nlog_debug("Failed to get groups from adapter");
        return -1;
    }
    
    bool tag_found = false;
    
    // 遍历所有 groups
    for(int g = 0; g < adapter->group_count; g++) {
        mock_group_info_t *group = &groups[g];
        nlog_debug("Searching in group: %s", group->name);
        
        // 遍历该 group 中的所有 tags (简化处理，假设所有 tags 都在一个全局数组中)
        for(int t = 0; t < adapter->tag_count; t++) {
            mock_tag_t *tag = &adapter->tags[t];
            
            // 比较 address 字段
            if(tag->address != NULL && strcmp(tag->address, address) == 0) {
                nlog_debug("Found matching tag: %s (address: %s) in group: %s", 
                          tag->name, tag->address, group->name);
                
                nlog_debug("Updated tag: %s with value: %.2f", tag->name, value);
                tag_found = true;
                break;
            }
        }
        
        if(tag_found) {
            break;
        }
    }
    
    if(!tag_found) {
        nlog_debug("No tag found with address: %s", address);
        return -1;
    }
    
    return 0;
}

// 模拟的 FCM 数据更新函数
int mock_fcm_update_tag_values_with_callback(mock_plugin_t *plugin, mock_adapter_t *adapter,
                                             uint16_t temperature, uint16_t set_temperature, uint8_t mode,
                                             uint8_t power_switch, uint8_t high_wind, uint8_t medium_wind, 
                                             uint8_t low_wind, uint16_t humidity, uint8_t wind_auto_flag)
{
    int ret = 0;
    
    // 计算风速等级
    int32_t wind_speed = 0;  // 0-停止
    if(wind_auto_flag) {
        wind_speed = 4;  // 自动
    } else if(high_wind) {
        wind_speed = 1;  // 高速
    } else if(medium_wind) {
        wind_speed = 2;  // 中速  
    } else if(low_wind) {
        wind_speed = 3;  // 低速
    }
    
    nlog_debug("FCM updating tag values via callback - temp:%.1f, set_temp:%.1f, mode:%d, switch:%d, wind:%d, humidity:%d", 
               (float)temperature/10.0, (float)set_temperature/10.0, mode, power_switch, wind_speed, humidity/10);
    
    // 更新各个点位
    ret += mock_update_tag_by_address_with_callback(plugin, adapter, ACME_FCM_ADDR_ONOFF, 1, (float)power_switch);
    ret += mock_update_tag_by_address_with_callback(plugin, adapter, ACME_FCM_ADDR_STEMP, 2, (float)set_temperature/10.0);
    ret += mock_update_tag_by_address_with_callback(plugin, adapter, ACME_FCM_ADDR_SMODE, 1, (float)mode);
    ret += mock_update_tag_by_address_with_callback(plugin, adapter, ACME_FCM_ADDR_WSPED, 1, (float)wind_speed);
    ret += mock_update_tag_by_address_with_callback(plugin, adapter, ACME_FCM_ADDR_RTEMP, 2, (float)temperature/10.0);
    ret += mock_update_tag_by_address_with_callback(plugin, adapter, ACME_FCM_ADDR_HUMID, 1, (float)humidity/10);
    
    return ret;
}

int main()
{
    printf("=== FCM Callback-based Tag Update Test ===\n");
    
    // 创建模拟的 tags
    mock_tag_t tags[] = {
        {"ONOFF", "1", 1},      // 开关
        {"STEMP", "2", 2},      // 设置温度
        {"SMODE", "3", 1},      // 设置模式
        {"WSPED", "4", 1},      // 设置风速
        {"RTEMP", "5", 2},      // 读取温度
        {"ERROC", "6", 1},      // 错误码
        {"HUMID", "7", 1},      // 湿度
    };
    
    // 创建模拟的 groups
    mock_group_info_t groups[] = {
        {"default_group", 7, 1000, NULL},
        {"custom_group", 0, 2000, NULL}
    };
    
    // 创建模拟的 adapter
    mock_adapter_t adapter = {
        .groups = groups,
        .group_count = 2,
        .tags = tags,
        .tag_count = sizeof(tags) / sizeof(tags[0])
    };
    
    // 创建模拟的 plugin
    mock_plugin_t plugin = {
        .common = {
            .adapter = &adapter
        }
    };
    
    // 模拟空调数据：00 f0 00 a0 04 00 14 03 01 00 00 00 00 01 00 00 01 ff ff ff
    uint16_t temperature = 0x00f0;      // 24.0°C
    uint16_t set_temperature = 0x00a0;  // 16.0°C
    uint8_t mode = 0x04;                // 模式 4
    uint8_t power_switch = 0x00;        // 关闭
    uint8_t high_wind = 0x00;           // 高风关闭
    uint8_t medium_wind = 0x00;         // 中风关闭
    uint8_t low_wind = 0x00;            // 低风关闭
    uint16_t humidity = 0x0001;         // 0.1%
    uint8_t wind_auto_flag = 0xff;      // 自动风速
    
    printf("\n--- Test: Update FCM tag values via adapter callback ---\n");
    int result = mock_fcm_update_tag_values_with_callback(&plugin, &adapter, temperature, set_temperature, mode,
                                                         power_switch, high_wind, medium_wind, low_wind,
                                                         humidity, wind_auto_flag);
    
    printf("Update result: %s\n", result == 0 ? "SUCCESS" : "FAILED");
    
    printf("\n--- Test: Update non-existent address via callback ---\n");
    result = mock_update_tag_by_address_with_callback(&plugin, &adapter, "99", 1, 123.45);
    printf("Update result: %s\n", result == -1 ? "EXPECTED FAILURE" : "UNEXPECTED SUCCESS");
    
    printf("\n=== Test Completed ===\n");
    printf("\nKey improvements:\n");
    printf("1. Uses adapter callback function get_groups() instead of direct call\n");
    printf("2. Follows the same pattern as get_dev_base() callback\n");
    printf("3. Maintains separation between plugin and adapter layers\n");
    printf("4. Allows for proper abstraction and testing\n");
    
    return 0;
}
