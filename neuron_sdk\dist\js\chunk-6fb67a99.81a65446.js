(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6fb67a99"],{2909:function(e,t,n){"use strict";n.d(t,"a",(function(){return l}));var r=n("6b75");function a(e){if(Array.isArray(e))return Object(r["a"])(e)}var o=n("db90"),c=n("06c5");function u(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function l(e){return a(e)||Object(o["a"])(e)||Object(c["a"])(e)||u()}},"820e":function(e,t,n){"use strict";var r=n("23e7"),a=n("c65b"),o=n("59ed"),c=n("f069"),u=n("e667"),l=n("2266");r({target:"Promise",stat:!0},{allSettled:function(e){var t=this,n=c.f(t),r=n.resolve,i=n.reject,d=u((function(){var n=o(t.resolve),c=[],u=0,i=1;l(e,(function(e){var o=u++,l=!1;i++,a(n,t,e).then((function(e){l||(l=!0,c[o]={status:"fulfilled",value:e},--i||r(c))}),(function(e){l||(l=!0,c[o]={status:"rejected",reason:e},--i||r(c))}))})),--i||r(c)}));return d.error&&i(d.value),n.promise}})},9978:function(e,t,n){"use strict";var r=n("2909"),a=(n("b0c0"),n("99af"),n("a4d3"),n("e01a"),n("7a23")),o=n("3fd4"),c=n("b3bd"),u=n("a0c5"),l=n("9dde"),i=n("a007"),d=n("e8f0"),b=function(e){return Object(a["pushScopeId"])("data-v-15d33db4"),e=e(),Object(a["popScopeId"])(),e},s={class:"thead-title"},f={class:"thead-title"},m=b((function(){return Object(a["createElementVNode"])("i",{class:"el-icon-info icon-label"},null,-1)})),p={class:"thead-title"},j={class:"thead-title"},O={class:"thead-title"},v={key:1},g={key:1},h={key:1},V=Object(a["defineComponent"])({props:{data:{type:Object,required:!0},nodePluginInfo:{type:Object},edit:{type:Boolean,default:!1}},emits:["update:modelValue","deleteTagItem"],setup:function(e,t){var n=t.expose,b=t.emit,V=e,x=Object(l["a"])(V),w=x.formCom,C=x.isAttrsIncludeStatic,k=x.tagTypeOptListAfterFilter,y=x.rules,E=x.validate,N=x.resetFields,B=Object(c["f"])(),L=B.isShowPrecisionField,T=Object(a["computed"])({get:function(){return V.data},set:function(e){b("update:modelValue",e)}}),S=Object(a["computed"])((function(){return function(e){return T.value.tagList[e].type===i["k"].BYTES}})),_=function(e,t){var n=C.value(e.attribute);n?(T.value.tagList[t].precision=void 0,T.value.tagList[t].decimal=null,S.value(t)&&(T.value.tagList[t].type=null)):T.value.tagList[t].value=void 0,Object(a["nextTick"])((function(){w.value.validateField("tagList.".concat(t,".address"))}))},$=function(e,t){var n=["tagList.".concat(t,".address")];T.value.tagList[t].value&&n.push("tagList.".concat(t,".value")),w.value.validateField(n)},F=function(e){b("deleteTagItem",e)};return n({validate:E,resetFields:N}),function(t,n){var c=Object(a["resolveComponent"])("emqx-input"),l=Object(a["resolveComponent"])("emqx-option"),b=Object(a["resolveComponent"])("emqx-select"),V=Object(a["resolveComponent"])("emqx-input-number"),x=Object(a["resolveComponent"])("emqx-button");return Object(a["openBlock"])(),Object(a["createBlock"])(Object(a["unref"])(o["ElForm"]),{ref:function(e,t){t["formCom"]=e,Object(a["isRef"])(w)&&(w.value=e)},model:Object(a["unref"])(T),onSubmit:n[0]||(n[0]=Object(a["withModifiers"])((function(){}),["prevent"]))},{default:Object(a["withCtx"])((function(){return[Object(a["createVNode"])(Object(a["unref"])(o["ElTable"]),{data:Object(a["unref"])(T).tagList,"empty-text":t.$t("common.emptyData"),"header-row-class-name":"thead-row"},{default:Object(a["withCtx"])((function(){return[Object(a["createVNode"])(Object(a["unref"])(o["ElTableColumn"]),{width:"120",fixed:""},{header:Object(a["withCtx"])((function(){return[Object(a["createElementVNode"])("span",s,Object(a["toDisplayString"])(t.$t("common.name")),1)]})),default:Object(a["withCtx"])((function(t){var n=t.row,r=t.$index;return[Object(a["createVNode"])(Object(a["unref"])(o["ElFormItem"]),{prop:"tagList.".concat(r,".name"),rules:Object(a["unref"])(y).name,required:""},{default:Object(a["withCtx"])((function(){return[Object(a["createVNode"])(c,{modelValue:n.name,"onUpdate:modelValue":function(e){return n.name=e},modelModifiers:{trim:!0},disabled:e.edit},null,8,["modelValue","onUpdate:modelValue","disabled"])]})),_:2},1032,["prop","rules"])]})),_:1}),Object(a["createVNode"])(Object(a["unref"])(o["ElTableColumn"]),{width:"180"},{header:Object(a["withCtx"])((function(){return[Object(a["createElementVNode"])("span",f,Object(a["toDisplayString"])(t.$t("common.attribute")),1),Object(a["createVNode"])(d["a"],{content:t.$t("config.staticNotSupportBytes")},{default:Object(a["withCtx"])((function(){return[m]})),_:1},8,["content"])]})),default:Object(a["withCtx"])((function(e){var t=e.row,n=e.$index;return[Object(a["createVNode"])(Object(a["unref"])(o["ElFormItem"]),{prop:"tagList.".concat(n,".attribute"),rules:Object(a["unref"])(y).attribute,required:""},{default:Object(a["withCtx"])((function(){return[Object(a["createVNode"])(u["a"],{modelValue:t.attribute,"onUpdate:modelValue":function(e){return t.attribute=e},collapseTags:!0,onChange:function(e){return _(t,n)}},null,8,["modelValue","onUpdate:modelValue","onChange"])]})),_:2},1032,["prop","rules"])]})),_:1}),Object(a["createVNode"])(Object(a["unref"])(o["ElTableColumn"]),{width:"130"},{header:Object(a["withCtx"])((function(){return[Object(a["createElementVNode"])("span",p,Object(a["toDisplayString"])(t.$t("common.type")),1)]})),default:Object(a["withCtx"])((function(e){var n=e.row,r=e.$index;return[Object(a["createVNode"])(Object(a["unref"])(o["ElFormItem"]),{prop:"tagList.".concat(r,".type"),rules:Object(a["unref"])(y).type,required:""},{default:Object(a["withCtx"])((function(){return[Object(a["createVNode"])(b,{modelValue:n.type,"onUpdate:modelValue":function(e){return n.type=e},placeholder:t.$t("common.pleaseSelect"),onChange:function(e){return $(n,r)}},{default:Object(a["withCtx"])((function(){return[(Object(a["openBlock"])(!0),Object(a["createElementBlock"])(a["Fragment"],null,Object(a["renderList"])(Object(a["unref"])(k),(function(e){return Object(a["openBlock"])(),Object(a["createBlock"])(l,{key:e.value,value:e.value,label:e.label,disabled:e.value===Object(a["unref"])(i["k"]).BYTES&&Object(a["unref"])(C)(n.attribute)},null,8,["value","label","disabled"])})),128))]})),_:2},1032,["modelValue","onUpdate:modelValue","placeholder","onChange"])]})),_:2},1032,["prop","rules"])]})),_:1}),Object(a["createVNode"])(Object(a["unref"])(o["ElTableColumn"]),{width:"130"},{header:Object(a["withCtx"])((function(){return[Object(a["createElementVNode"])("span",j,Object(a["toDisplayString"])(t.$t("config.address")),1)]})),default:Object(a["withCtx"])((function(e){var n=e.row,u=e.$index;return[Object(a["unref"])(C)(n.attribute)?(Object(a["openBlock"])(),Object(a["createBlock"])(Object(a["unref"])(o["ElFormItem"]),{key:0,prop:"tagList.".concat(u,".address"),rules:[{required:!1},Object(a["unref"])(y).address]},{default:Object(a["withCtx"])((function(){return[Object(a["createVNode"])(c,{modelValue:n.address,"onUpdate:modelValue":function(e){return n.address=e}},null,8,["modelValue","onUpdate:modelValue"])]})),_:2},1032,["prop","rules"])):(Object(a["openBlock"])(),Object(a["createBlock"])(Object(a["unref"])(o["ElFormItem"]),{key:1,prop:"tagList.".concat(u,".address"),rules:[{required:!0,message:t.$t("config.tagAddressRequired")}].concat(Object(r["a"])(Object(a["unref"])(y).address))},{default:Object(a["withCtx"])((function(){return[Object(a["createVNode"])(c,{modelValue:n.address,"onUpdate:modelValue":function(e){return n.address=e}},null,8,["modelValue","onUpdate:modelValue"])]})),_:2},1032,["prop","rules"]))]})),_:1}),Object(a["createVNode"])(Object(a["unref"])(o["ElTableColumn"]),{width:"110",rules:Object(a["unref"])(y).value},{header:Object(a["withCtx"])((function(){return[Object(a["createElementVNode"])("span",O,Object(a["toDisplayString"])(t.$t("config.tagValue")),1)]})),default:Object(a["withCtx"])((function(e){var n=e.row,u=e.$index;return[Object(a["createVNode"])(Object(a["unref"])(o["ElFormItem"]),{prop:"tagList.".concat(u,".value"),rules:[].concat(Object(r["a"])(Object(a["unref"])(y).value),[{required:Object(a["unref"])(C)(n.attribute),message:t.$t("config.tagValueRequired")}])},{default:Object(a["withCtx"])((function(){return[Object(a["unref"])(C)(n.attribute)?(Object(a["openBlock"])(),Object(a["createBlock"])(c,{key:0,modelValue:n.value,"onUpdate:modelValue":function(e){return n.value=e},modelModifiers:{trim:!0}},null,8,["modelValue","onUpdate:modelValue"])):(Object(a["openBlock"])(),Object(a["createElementBlock"])("span",v,"-"))]})),_:2},1032,["prop","rules"])]})),_:1},8,["rules"]),Object(a["createVNode"])(Object(a["unref"])(o["ElTableColumn"]),{label:t.$t("config.desc"),width:"120"},{default:Object(a["withCtx"])((function(e){var t=e.row,n=e.$index;return[Object(a["createVNode"])(Object(a["unref"])(o["ElFormItem"]),{prop:"tagList.".concat(n,".description")},{default:Object(a["withCtx"])((function(){return[Object(a["createVNode"])(c,{modelValue:t.description,"onUpdate:modelValue":function(e){return t.description=e},type:"text"},null,8,["modelValue","onUpdate:modelValue"])]})),_:2},1032,["prop"])]})),_:1},8,["label"]),Object(a["createVNode"])(Object(a["unref"])(o["ElTableColumn"]),{label:t.$t("config.decimal"),width:"110"},{default:Object(a["withCtx"])((function(e){var t=e.row,n=e.$index;return[Object(a["createVNode"])(Object(a["unref"])(o["ElFormItem"]),{prop:"tagList.".concat(n,".decimal")},{default:Object(a["withCtx"])((function(){return[Object(a["unref"])(C)(t.attribute)?(Object(a["openBlock"])(),Object(a["createElementBlock"])("span",g,"-")):(Object(a["openBlock"])(),Object(a["createBlock"])(V,{key:0,modelValue:t.decimal,"onUpdate:modelValue":function(e){return t.decimal=e},step:.1,"controls-position":"right"},null,8,["modelValue","onUpdate:modelValue","step"]))]})),_:2},1032,["prop"])]})),_:1},8,["label"]),Object(a["createVNode"])(Object(a["unref"])(o["ElTableColumn"]),{label:t.$t("config.precision"),width:"110"},{default:Object(a["withCtx"])((function(e){var t=e.row,n=e.$index;return[Object(a["createVNode"])(Object(a["unref"])(o["ElFormItem"]),{prop:"tagList.".concat(n,".precision")},{default:Object(a["withCtx"])((function(){return[Object(a["unref"])(L)(t.type)&&!Object(a["unref"])(C)(t.attribute)?(Object(a["openBlock"])(),Object(a["createBlock"])(V,{key:0,modelValue:t.precision,"onUpdate:modelValue":function(e){return t.precision=e},min:0,max:17,"controls-position":"right"},null,8,["modelValue","onUpdate:modelValue"])):(Object(a["openBlock"])(),Object(a["createElementBlock"])("span",h,"-"))]})),_:2},1032,["prop"])]})),_:1},8,["label"]),Object(a["createVNode"])(Object(a["unref"])(o["ElTableColumn"]),{label:t.$t("common.oper"),fixed:"right","min-width":"80"},{default:Object(a["withCtx"])((function(e){var n=e.$index;return[Object(a["createVNode"])(x,{type:"danger",size:"mini",disabled:0===n&&1===Object(a["unref"])(T).tagList.length,onClick:function(e){return F(n)}},{default:Object(a["withCtx"])((function(){return[Object(a["createTextVNode"])(Object(a["toDisplayString"])(t.$t("common.delete")),1)]})),_:2},1032,["disabled","onClick"])]})),_:1},8,["label"])]})),_:1},8,["data","empty-text"])]})),_:1},8,["model"])}}}),x=(n("ff4b"),n("6b0d")),w=n.n(x);const C=w()(V,[["__scopeId","data-v-15d33db4"]]);t["a"]=C},"9dde":function(e,t,n){"use strict";var r=n("1da1"),a=n("ade3"),o=(n("96cf"),n("4de4"),n("d3b7"),n("ac1f"),n("1276"),n("a9e3"),n("7db0"),n("4d63"),n("c607"),n("2c3e"),n("25f0"),n("00b4"),n("820e"),n("3ca3"),n("ddb0"),n("d81d"),n("caad"),n("2532"),n("7a23")),c=n("47e2"),u=n("b3bd"),l=n("a557"),i=n("a007");t["a"]=function(e){var t=Object(c["b"])(),n=t.t,d=Object(o["ref"])(),b=Object(u["d"])(),s=b.isAttrsIncludeTheValue,f=Object(o["computed"])((function(){return function(e){return s(e,i["j"].Static)}})),m=Object(o["computed"])((function(){var t;return null===e||void 0===e||null===(t=e.nodePluginInfo)||void 0===t?void 0:t.tag_regex})),p=Object(u["g"])(),j=p.tagTypeOptList,O=Object(o["computed"])((function(){return m.value?j.filter((function(t){return e.nodePluginInfo.tag_regex.some((function(e){return e.type===t.value}))})):j})),v=function(t,r,a){var o,c,u,l=t.field,i=l.split(".")[1],d=null!==e&&void 0!==e&&null!==(o=e.data)&&void 0!==o&&o.tagList?null===e||void 0===e||null===(c=e.data)||void 0===c?void 0:c.tagList[i]:null===e||void 0===e?void 0:e.data,b=d.type,s=d.attribute,p=f.value(Number(s));if(p)return a();var j=null===(u=m.value)||void 0===u?void 0:u.find((function(e){return e.type===Number(b)}));if(j){var O=null!==j&&void 0!==j&&j.regex?new RegExp(j.regex):null;O?O.test(r)?a():a(new Error("".concat(n("config.tagAddressValid")))):a()}else a()},g=Object(l["b"])(!1),h=g.checkWriteData,V=function(e,t,n){return e?t+i["k"][e]+n:""},x=Object(o["computed"])((function(){return function(e){var t;return t={},Object(a["a"])(t,l["a"].FormattingError,V(e,n("data.writeDataFormattingErrorPrefix"),n("data.writeDataFormattingErrorSuffix"))),Object(a["a"])(t,l["a"].LessThanMinimum,V(e,n("data.writeDataMinimumErrorPrefix"),n("data.writeDataMinimumErrorSuffix"))),Object(a["a"])(t,l["a"].GreaterThanMaximum,V(e,n("data.writeDataMaximumErrorPrefix"),n("data.writeDataMaximumErrorSuffix"))),Object(a["a"])(t,l["a"].LessThanMinSafeInteger,n("data.writeDataSafeMinimumError")),Object(a["a"])(t,l["a"].GreaterThanMaxSafeInteger,n("data.writeDataSafeMaximumError")),t}})),w=function(){var t=Object(r["a"])(regeneratorRuntime.mark((function t(n,r,a){var o,c,u,l,d,b,f,m,p,j,O,v,g;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(c=n.field,u=c.split(".")[1],l=null!==e&&void 0!==e&&null!==(o=e.data)&&void 0!==o&&o.tagList?e.data.tagList[u]:null===e||void 0===e?void 0:e.data,d=l.type,b=l.attribute,t.prev=4,f=s(Number(b),i["j"].Static),f){t.next=8;break}return t.abrupt("return",a());case 8:if(!d){t.next=18;break}return m=String(r),t.next=12,Promise.allSettled([h(d,m)]);case 12:if(p=t.sent,j=p.map((function(e){return(null===e||void 0===e?void 0:e.value)||!1})),j.includes(!0)){t.next=17;break}return O=x.value(d)[Number("1")],t.abrupt("return",a(new Error(O)));case 17:return t.abrupt("return",a());case 18:a(),t.next=26;break;case 21:t.prev=21,t.t0=t["catch"](4),v=(null===t.t0||void 0===t.t0?void 0:t.t0.message)||t.t0,g=x.value(d)[Number(v)],a(new Error(g));case 26:case"end":return t.stop()}}),t,null,[[4,21]])})));return function(e,n,r){return t.apply(this,arguments)}}(),C=Object(o["computed"])((function(){return{name:[{required:!0,message:n("config.tagNameRequired")}],address:[{validator:v,trigger:["blur","change"]}],attribute:[{required:!0,message:n("config.tagAttributeRequired")},{validator:function(e,t){return t&&0!==t.length?[]:[new Error(n("config.tagAttributeRequired"))]}}],type:[{required:!0,message:n("config.tagTypeRequired")}],value:[{validator:w,trigger:"blur"}]}})),k=function(){return d.value?d.value.validate():Promise.resolve()},y=function(){return d.value.resetField()};return{formCom:d,isAttrsIncludeStatic:f,tagTypeOptListAfterFilter:O,rules:C,validate:k,resetFields:y}}},a0c5:function(e,t,n){"use strict";n("a9e3"),n("b64b"),n("7db0"),n("d3b7"),n("a15b"),n("4e82");var r=n("7a23"),a=n("b3bd"),o=Object(r["defineComponent"])({props:{modelValue:{type:[Number]},collapseTags:{type:Boolean,default:!1}},emits:["update:modelValue","change"],setup:function(e,t){var n=t.emit,o=e,c=Object(a["d"])(),u=c.tagAttributeTypeOptList,l=c.tagAttrValueMap,i=Object(r["computed"])({get:function(){return o.modelValue?l[o.modelValue]:[]},set:function(e){var t=Object.keys(l),r=t.find((function(t){return l[Number(t)].join(",")===e.sort((function(e,t){return e-t})).join(",")}));n("update:modelValue",Number(r))}}),d=function e(){n("change",e)};return function(t,n){var a=Object(r["resolveComponent"])("emqx-option"),o=Object(r["resolveComponent"])("emqx-select");return Object(r["openBlock"])(),Object(r["createBlock"])(o,{modelValue:Object(r["unref"])(i),"onUpdate:modelValue":n[0]||(n[0]=function(e){return Object(r["isRef"])(i)?i.value=e:null}),multiple:"","collapse-tags":e.collapseTags,placeholder:t.$t("common.pleaseSelect"),onChange:d},{default:Object(r["withCtx"])((function(){return[(Object(r["openBlock"])(!0),Object(r["createElementBlock"])(r["Fragment"],null,Object(r["renderList"])(Object(r["unref"])(u),(function(e){return Object(r["openBlock"])(),Object(r["createBlock"])(a,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])})),128))]})),_:1},8,["modelValue","collapse-tags","placeholder"])}}});const c=o;t["a"]=c},a434:function(e,t,n){"use strict";var r=n("23e7"),a=n("da84"),o=n("23cb"),c=n("5926"),u=n("07fa"),l=n("7b0b"),i=n("65f0"),d=n("8418"),b=n("1dde"),s=b("splice"),f=a.TypeError,m=Math.max,p=Math.min,j=9007199254740991,O="Maximum allowed length exceeded";r({target:"Array",proto:!0,forced:!s},{splice:function(e,t){var n,r,a,b,s,v,g=l(this),h=u(g),V=o(e,h),x=arguments.length;if(0===x?n=r=0:1===x?(n=0,r=h-V):(n=x-2,r=p(m(c(t),0),h-V)),h+n-r>j)throw f(O);for(a=i(g,r),b=0;b<r;b++)s=V+b,s in g&&d(a,b,g[s]);if(a.length=r,n<r){for(b=V;b<h-r;b++)s=b+r,v=b+n,s in g?g[v]=g[s]:delete g[v];for(b=h;b>h-r+n;b--)delete g[b-1]}else if(n>r)for(b=h-r;b>V;b--)s=b+r-1,v=b+n-1,s in g?g[v]=g[s]:delete g[v];for(b=0;b<n;b++)g[b+V]=arguments[b+2];return g.length=h-r+n,a}})},eb8c:function(e,t,n){},ff4b:function(e,t,n){"use strict";n("eb8c")}}]);