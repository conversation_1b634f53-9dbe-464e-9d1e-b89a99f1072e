# Default values for neuron.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.


image:
  repository: emqx/neuron
  pullPolicy: IfNotPresent
  # -- Overrides the image tag whose default is the chart appVersion.
  tag: ""

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

podAnnotations: {}

# @ignored
podSecurityContext: {}
  # fsGroup: 2000

# @ignored
securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  enabled: true
  type: ClusterIP

  # -- Service ports
  ports:
    web:
      # -- Neuron Dashboard port name
      name: "web"
      # -- Neuron Dashboard port
      port: 7000
    api:
      # -- Neuron API port name
      name: "api"
      # -- Neuron API port
      port: 7001

  # -- Specify the nodePort(s) value for the LoadBalancer and NodePort service types.
  # ref: https://kubernetes.io/docs/concepts/services-networking/service/#type-nodeport
  nodePorts:
    web:
    api:
   ## Set the LoadBalancer service type to internal only.
  ## ref: https://kubernetes.io/docs/concepts/services-networking/service/#internal-load-balancer
  ##
  # loadBalancerIP:
  ## Load Balancer sources
  ## ref: https://kubernetes.io/docs/tasks/access-application-cluster/configure-cloud-provider-firewall/#restrict-access-for-loadbalancer-service
  ## Example:

  # -- loadBalancerSourceRanges:
  # - **********/24
  #
  loadBalancerSourceRanges: []
  ## TODO: it's just for OpenELB
  # -- Provide any additional annotations which may be required. Evaluated as a template
  annotations:
    lb.kubesphere.io/v1alpha1: openelb
    eip.openelb.kubesphere.io/v1alpha2: eip-pool
    protocol.openelb.kubesphere.io/v1alpha1: layer2

persistence:
  enabled: false
  accessMode: ReadWriteOnce
  ## TODO: not all storage class can work well, it depends on the specific storage
  storageClass: standard
  # storageClass: huawei-nfs

neuronMounts:
  - name: pvc-neuron-data
    capacity: 100Mi
    mountPath: "/opt/neuron/persistence"

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

nodeSelector: {}

tolerations:
  - key: "node.kubernetes.io/not-ready"
    operator: "Exists"
    effect: "NoExecute"
    tolerationSeconds: 30
  - key: "node.kubernetes.io/unreachable"
    operator: "Exists"
    effect: "NoExecute"
    tolerationSeconds: 30

affinity: {}

# -- Kubernetes Cluster Domain
clusterDomain: cluster.local

# @ignored
tls:
  enabled: false
  # -- neuron public key
  publicKey: |
    -----BEGIN PUBLIC KEY-----
    MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAzrO7FUz4GGKl4nP5lwKM
    h5uageJAAHnLZpVxTR1wXA9E870sU03gHnPWsh2bXCCx85ymYaiu/5VlptgnU0rQ
    B1I3xGRG8eHmHPrhHNWDRKpY6oY8AnTPqXvfa/Z4u+njJzDSqi4qM8Gwfqlgpjlg
    8DUF+sheLx8hL+x9XFzovTxafrmyJRRAVQTkhB7esKKqRF8BMjdOdYZxJEv9jxa0
    BwXcjprPDlj5TV6k3wJqbq4UtnEEVbjEQKqFVkfY4FuJ5NSPHpGEix94PGQGRsiW
    kGX0vx5udHJ0SlXM6qpMLny18FhWbwKNX3urc3mImn/j3zcJ5x1d+FlXAXs1NBSe
    TQIDAQAB
    -----END PUBLIC KEY-----
  # -- neuron private key
  privateKey: |
**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************


neuronEnv:
  enabled: true
  keys:
    disableAuth: DISABLE_AUTH

  values:
    disableAuth: true

# -- only for kubeedge deployment
hostPorts:
  enabled: false
  web: 30370
  api: 30371

