/**
 * NEURON IIoT System for Industry 4.0
 * Copyright (C) 2020-2024 EMQ Technologies Co., Ltd All rights reserved.
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 3 of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with this program; if not, write to the Free Software Foundation,
 * Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.
 **/

/*************************************************************
*  lora 设备业务逻辑接口层
*  主要实现 设备web 、云端下发 与 Lora 协议层的数据交换服务
*************************************************************/
#include "otel/otel_manager.h"
#include "utils/asprintf.h"
#include "utils/time.h"
#include "acme_lora.h"
#include <stdlib.h>
#include "util.h"
#include <neuron.h>
#include "errcodes.h"
#include "business.h"

int hex2str_printf(char *data,int nByte)
{
    char hexs[600]={0};
    
    Hex2Str(data, hexs, nByte);
    hexs[2*nByte] = '\0';
    printf ("Hex: %s\n", hexs); 
    return 0;
}

/*
* 设备搜索命令应答
*/
int business_lora_dev_search_resp(neu_plugin_t *plugin,neu_reqresp_head_t *head,void *data)
{
    int ret = 0;
    if(plugin == NULL || head == NULL){
        nlog_debug("parameter error !!!");
        return -1;
    }

    neu_reqresp_head_t         header                       = { 0 };
    header.ctx             = head->ctx;
    strcpy(header.sender, head->sender);
    strcpy(header.receiver, head->receiver);

    nlog_debug("dev_search_resp sender:%s receiver:%s",head->sender,head->receiver);

    if(data == NULL){       //没有搜索到设备，响应成功
        neu_resp_error_t error = { 0 };
        header.type = NEU_RESP_ERROR;
        error.error = NEU_ERR_SUCCESS;
        if (0 != neu_plugin_op(plugin, header, &error)) {
            plog_error(plugin, "neu_plugin_op(NEU_RESP_ACME_DEV_SEARCH) fail");
            return -1;
        }
    }else{
        header.type = NEU_RESP_ACME_DEV_SEARCH;

        nlog_debug("dev_search_resp send msg: NEU_RESP_ACME_DEV_SEARCH");

        neu_resp_get_acme_dev_search_t resp = {0};
        resp.devs = (UT_array *)data;
        if (0 != neu_plugin_op(plugin, header, &resp)) {
            plog_error(plugin, "neu_plugin_op(NEU_RESP_ACME_DEV_SEARCH) fail");
            return -1;
        }
    }

    return ret;
}


/*
* 搜索子设备命令
* 根据不同设备类型，返回默认设备组（如 FCM）或者返回总线上真实搜索到的设备 如多联机
*/
int business_lora_dev_search(neu_plugin_t *plugin, node_base_t * dev, neu_reqresp_head_t *head)
{
    int ret = 0;
    if(plugin == NULL || dev == NULL){
        nlog_debug("parameter error !!!");
        return -1;
    }

    nlog_debug("node:%s || modeType:%d , search device start...",plugin->common.name, dev->modeType);

    switch (dev->modeType)
    {
        case SPT:
            

            break;

        case IOM_FCM:{
            UT_array  *devs = fcm_dev_search(plugin->common.name);
            if(devs == NULL){
                nlog_debug("business_lora_dev_search_resp resp null !!!");
                business_lora_dev_search_resp(plugin, head, NULL);
            }else{
                nlog_debug("dev search resp start...");
                business_lora_dev_search_resp(plugin, head, devs);
            }
            break;
        }
        default:
            break;
    }

    return ret;
}



/*
* 设备通道命令下发
*/
int business_lora_dev_ctrl(neu_plugin_t *plugin, node_base_t * dev , void * params, neu_reqresp_head_t *head)
{
    int ret = 0;
    if(plugin == NULL || dev == NULL || params == NULL){
        nlog_debug("parameter error !!!");
        return -1;
    }

    nlog_debug("node:%s modeType:%d chn ctrl command:%s ",plugin->common.name, dev->modeType ,params);

    switch (dev->modeType)
    {
        case SPT:          

            break;
        case IOM_FCM:{
            ret = fcm_dev_ctrl(plugin,dev,params);
            break;
        }
        default:
            break;
    }
    
    return ret;
}

/*
* 适配器下发的添加组命令，用插件内部进行系统默认组的检查，并选择性添加组内默认点位
*/
int business_lora_dev_add_group(neu_plugin_t *plugin, node_base_t * dev ,neu_req_add_group_t *group)
{
    int ret = 0;
    if(plugin == NULL || dev == NULL || group == NULL){
        nlog_debug("parameter error !!!");
        return -1;
    }
    
    nlog_debug("node:%s add group check.",plugin->common.name);

    switch (dev->modeType)
    {
        case SPT:
            

            break;
        case IOM_FCM:{
            ret = fcm_dev_add_group(plugin,dev,group);
            break;
        }
        default:
            break;
    }
    return ret;
}

/*
* 设备业务层下发 写点位命令
*/
int business_lora_dev_write_tag(neu_plugin_t *plugin, node_base_t * dev, neu_datatag_t *tag,neu_value_u value)
{
    int ret = 0;
    if(plugin == NULL || dev == NULL || tag == NULL){
        nlog_debug("parameter error !!!");
        return -1;
    }

    nlog_debug("node:%s write tag check.",plugin->common.name);

    switch (dev->modeType)
    {
        case SPT:
            

            break;
        case IOM_FCM:{
            ret = fcm_dev_write_tag(plugin,dev,tag,value);
            break;
        }
        default:
            break;
    }


    return  ret;
}

/*
* 设备消息处理函数
* 处理从 LoRa 网关接收到的设备上报数据
*/
int business_lora_dev_message_handle(neu_plugin_t *plugin, node_base_t * dev, neu_acme_dev_data_pop_t *pInfo)
{
    int ret = 0;
    if(plugin == NULL || dev == NULL || pInfo == NULL){
        nlog_debug("parameter error !!!");
        return -1;
    }

    nlog_debug("node:%s modeType:%d recv message rsp:0x%02x len:%d",
               plugin->common.name, dev->modeType, pInfo->rsp, pInfo->len);

    switch (dev->modeType)
    {
        case SPT:{
            // TODO: 处理 SPT 类型设备的消息
            nlog_debug("SPT device message handling not implemented yet");
            break;
        }
        case IOM_FCM:{
            ret = fcm_dev_message_handle(plugin, dev, pInfo);
            break;
        }
        default:
            nlog_debug("Unknown device type: %d", dev->modeType);
            ret = -1;
            break;
    }
        
    return ret;
}



