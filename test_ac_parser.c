#include "ac_data_parser.h"

/**
 * 测试程序 - 解析您提供的示例数据
 */
int main()
{
    // 您提供的示例数据
    uint8_t test_data[] = {
        0x00, 0xf0, 0x00, 0xa0, 0x04, 0x00, 0x14, 0x03, 0x01, 0x00, 
        0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0xff, 0xff, 0xff
    };
    
    // 补充缺失的3个字节（优先级字段）
    uint8_t complete_data[] = {
        0x00, 0xf0, 0x00, 0xa0, 0x04, 0x00, 0x14, 0x03, 0x01, 0x00, 
        0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0xff, 0xff, 0xff,
        0x00, 0x00, 0x00  // 补充的优先级字段
    };
    
    ac_data_t ac_data;
    
    printf("Original data (20 bytes):\n");
    for (int i = 0; i < sizeof(test_data); i++) {
        printf("%02X ", test_data[i]);
        if ((i + 1) % 10 == 0) printf("\n");
    }
    printf("\n\n");
    
    // 解析原始数据（20字节）
    printf("=== Parsing Original Data (20 bytes) ===\n");
    if (parse_ac_data(test_data, sizeof(test_data), &ac_data) == 0) {
        print_ac_data(&ac_data);
    }
    
    printf("\n\nComplete data (23 bytes):\n");
    for (int i = 0; i < sizeof(complete_data); i++) {
        printf("%02X ", complete_data[i]);
        if ((i + 1) % 10 == 0) printf("\n");
    }
    printf("\n\n");
    
    // 解析完整数据（23字节）
    printf("=== Parsing Complete Data (23 bytes) ===\n");
    if (parse_ac_data(complete_data, sizeof(complete_data), &ac_data) == 0) {
        print_ac_data(&ac_data);
    }
    
    // 数据解析说明
    printf("\n=== Data Analysis ===\n");
    printf("Based on your data: 00 f0 00 a0 04 00 14 03 01 00 00 00 00 01 00 00 01 ff ff ff\n");
    printf("Temperature:      0x00f0 = %d (24.0°C)\n", 0x00f0);
    printf("Set Temperature:  0x00a0 = %d (16.0°C)\n", 0x00a0);
    printf("Mode:             0x04 = %d (Dry mode)\n", 0x04);
    printf("Week Day:         0x00 = %d (Invalid/Sunday)\n", 0x00);
    printf("Week:             0x14 = %d (Week 20)\n", 0x14);
    printf("Hour:             0x03 = %d (3 o'clock)\n", 0x03);
    printf("Minute:           0x01 = %d (1 minute)\n", 0x01);
    printf("Power Switch:     0x00 = %d (OFF)\n", 0x00);
    printf("High Wind:        0x00 = %d (OFF)\n", 0x00);
    printf("Medium Wind:      0x00 = %d (OFF)\n", 0x00);
    printf("Low Wind:         0x00 = %d (OFF)\n", 0x00);
    printf("Cold Valve:       0x01 = %d (OPEN)\n", 0x01);
    printf("Hot Valve:        0x00 = %d (CLOSE)\n", 0x00);
    printf("Humidity:         0x0001 = %d (0.1%%)\n", 0x0001);
    printf("Wind Auto Flag:   0xff = %d (AUTO)\n", 0xff);
    printf("GID:              0xff = %d\n", 0xff);
    printf("OID:              0xff = %d\n", 0xff);
    printf("Note: Your data appears to be 20 bytes, but format specifies 23 bytes total.\n");
    printf("Missing 3 bytes for priority fields (onoff_pri, set_temp_pri, set_mode_pri, set_wspeed_pri).\n");
    
    return 0;
}

/**
 * 辅助函数：将十六进制字符串转换为字节数组
 * 使用示例：
 * char hex_str[] = "00f000a0040014030100000000010000001ffffff";
 * uint8_t data[23];
 * int len = hex_string_to_bytes(hex_str, data, sizeof(data));
 */
int hex_string_to_bytes(const char* hex_str, uint8_t* bytes, int max_bytes)
{
    int len = strlen(hex_str);
    if (len % 2 != 0) return -1; // 长度必须是偶数
    
    int byte_count = len / 2;
    if (byte_count > max_bytes) return -1; // 超出缓冲区大小
    
    for (int i = 0; i < byte_count; i++) {
        char hex_byte[3] = {hex_str[i*2], hex_str[i*2+1], '\0'};
        bytes[i] = (uint8_t)strtol(hex_byte, NULL, 16);
    }
    
    return byte_count;
}
