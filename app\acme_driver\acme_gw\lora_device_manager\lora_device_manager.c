/*
* 此单元主要实现 子模块管理相关接口实现
* 子设备注册、子设备更新、子设备删除
*/

#include "otel/otel_manager.h"
#include "utils/asprintf.h"
#include "utils/time.h"
#include <stdlib.h>
#include <neuron.h>
#include "errcodes.h"
#include "util.h"

#include "lora_device_manager.h"
#include "acme_lora_driver.h"

typedef struct device_entity {
    char *name;
    int mid;
    uint8_t online; //0-offline   1-online
    uint8_t pair_flag;      //是否正在配对
    uint8_t deviceType;     //设备类型
    uint8_t eui[20];
    int pid;
    int link;
    int subType;
    //TODO: 完善模块基本信息定义

    UT_hash_handle hh;
} device_entity_t;


struct neu_subDevice_manager {
    device_entity_t *devices;
};



/*
* 创建子设备管理器
*/
neu_subDevice_manager_t *neu_devices_manager_create()
{
    neu_subDevice_manager_t *devices_manager = calloc(1, sizeof(neu_subDevice_manager_t));
    return devices_manager;
}

/*
* 设备管理器 销毁
*/
void neu_devices_manager_destroy(neu_subDevice_manager_t *mgr)
{
    device_entity_t *el = NULL, *tmp = NULL;

    HASH_ITER(hh, mgr->devices, el, tmp)
    {
        HASH_DEL(mgr->devices, el);
        free(el->name);
        free(el);
    }

    free(mgr);
}

/*
* 添加子设备到管理器
*/
int neu_device_manager_add(neu_subDevice_manager_t *mgr,  manager_dev_info_t *dev)
{
    if(mgr == NULL || dev == NULL) return -1;

    device_entity_t *node = (device_entity_t *)calloc(1,sizeof(device_entity_t));
    node->name       = strdup(dev->name);
    node->mid        = dev->mid;
    node->online     = dev->online;; //0-offline   1-online
    node->pair_flag  = dev->pair_flag;;      //是否正在配对
    node->deviceType = dev->deviceType;;     //设备类型
    memcpy(node->eui,dev->eui,EUI_ADR_LEN);
    node->pid        = dev->pid;
    node->link       = dev->link;
    node->subType    = dev->subType;

    HASH_ADD_STR(mgr->devices, name, node);
    nlog_debug("subDevice %s add ok.",node->name);

    return 0;
}

/*
* 查找对应名称的子设备
*/
manager_dev_info_t * neu_device_manager_get(neu_subDevice_manager_t *mgr, char *name)
{
    if(mgr == NULL || name == NULL) return NULL;
    
    device_entity_t *node = NULL;

    HASH_FIND_STR(mgr->devices, name, node);
    if (NULL == node) {
        return NULL;
    }

    manager_dev_info_t *dev = (manager_dev_info_t *)calloc(1,sizeof(manager_dev_info_t));
    dev->name       = strdup(node->name);
    dev->mid        = node->mid;
    dev->online     = node->online;; //0-offline   1-online
    dev->pair_flag  = node->pair_flag;;      //是否正在配对
    dev->deviceType = node->deviceType;;     //设备类型
    memcpy(dev->eui,node->eui,EUI_ADR_LEN);
    dev->pid        = node->pid;
    dev->link       = node->link;
    dev->subType    = node->subType;
  
    nlog_debug("subDevice %s , mid=%d get ok.",dev->name, dev->mid);

    return dev;
}

/*
* 通过名称，查找设备的modeType 
*/
uint8_t neu_device_manager_get_modeType_by_name(neu_subDevice_manager_t *mgr, char *name)
{
    if(mgr == NULL || name == NULL) return -1;
    device_entity_t *node = NULL;

    HASH_FIND_STR(mgr->devices, name, node);
    if (NULL == node) {
        return -1;
    }

    return node->deviceType;
}

/*
* 更新子设备名称
*/
int neu_device_manager_update_name(neu_subDevice_manager_t *mgr, const char *node_name,
                                 const char *new_node_name)
{
    device_entity_t *node = NULL;

    HASH_FIND_STR(mgr->devices, node_name, node);
    if (NULL == node) {
        return NEU_ERR_NODE_NOT_EXIST;
    }

    char *new_name = strdup(new_node_name);
    if (NULL == new_name) {
        return NEU_ERR_EINTERNAL;
    }

    HASH_DEL(mgr->devices, node);
    free(node->name);
    node->name = new_name;
    HASH_ADD_STR(mgr->devices, name, node);

    return 0;
}

/*
* 子设备删除
*/
void neu_device_manager_del(neu_subDevice_manager_t *mgr, const char *name)
{
    device_entity_t *node = NULL;

    HASH_FIND_STR(mgr->devices, name, node);
    if (node != NULL) {
        HASH_DEL(mgr->devices, node);
        free(node->name);
        free(node);
    }

    nlog_debug("subDevice %s delete ok.",name);
}


/*
* 子设备信息全量更新
*/
int neu_device_manager_update(neu_subDevice_manager_t *mgr, const char *name,
                            manager_dev_info_t *dev)
{
    if(mgr == NULL || name == NULL || dev == NULL){
        nlog_debug("parameter error !!!");
        return -1;
    }

    neu_device_manager_del(mgr,name);
    neu_device_manager_add(mgr,dev);

    nlog_debug("subdevice %s update node ok.",name);
    return 0;
}

/*
* 获取子设备个数
*/
uint16_t neu_device_manager_size(neu_subDevice_manager_t *mgr)
{
    return HASH_COUNT(mgr->devices);
}

#if 0   //多种查询接口 后续补充完善
/*
* 获取指定类型的子设备集合
*/
UT_array *neu_device_manager_get(neu_subDevice_manager_t *mgr, int type)
{
    UT_array *     array = NULL;
    UT_icd         icd   = { sizeof(neu_resp_node_info_t), NULL, NULL, NULL };
    device_entity_t *el = NULL, *tmp = NULL;

    utarray_new(array, &icd);

    HASH_ITER(hh, mgr->devices, el, tmp)
    {
        if (!el->is_static && el->display) {
            if (el->adapter->module->type & type) {
                neu_resp_node_info_t info = { 0 };
                strcpy(info.node, el->adapter->name);
                strcpy(info.plugin, el->adapter->module->module_name);
                utarray_push_back(array, &info);
            }
        }
    }

    return array;
}

UT_array *neu_device_manager_filter(neu_subDevice_manager_t *mgr, int type,
                                  const char *plugin, const char *node)
{
    UT_array *     array = NULL;
    UT_icd         icd   = { sizeof(neu_resp_node_info_t), NULL, NULL, NULL };
    device_entity_t *el = NULL, *tmp = NULL;

    utarray_new(array, &icd);

    HASH_ITER(hh, mgr->devices, el, tmp)
    {
        if (!el->is_static && el->display) {
            if (el->adapter->module->type & type) {
                if (strlen(plugin) > 0 &&
                    strcmp(el->adapter->module->module_name, plugin) != 0) {
                    continue;
                }
                if (strlen(node) > 0 &&
                    strstr(el->adapter->name, node) == NULL) {
                    continue;
                }
                neu_resp_node_info_t info = { 0 };
                strcpy(info.node, el->adapter->name);
                strcpy(info.plugin, el->adapter->module->module_name);
                utarray_push_back(array, &info);
            }
        }
    }

    return array;
}

UT_array *neu_device_manager_get_all(neu_subDevice_manager_t *mgr)
{
    UT_array *     array = NULL;
    UT_icd         icd   = { sizeof(neu_resp_node_info_t), NULL, NULL, NULL };
    device_entity_t *el = NULL, *tmp = NULL;

    utarray_new(array, &icd);

    HASH_ITER(hh, mgr->devices, el, tmp)
    {
        neu_resp_node_info_t info = { 0 };
        strcpy(info.node, el->adapter->name);
        strcpy(info.plugin, el->adapter->module->module_name);
        utarray_push_back(array, &info);
    }

    return array;
}

UT_array *neu_device_manager_get_adapter(neu_subDevice_manager_t *mgr, int type)
{
    UT_array *     array = NULL;
    device_entity_t *el = NULL, *tmp = NULL;

    utarray_new(array, &ut_ptr_icd);

    HASH_ITER(hh, mgr->devices, el, tmp)
    {
        if (!el->is_static && el->display) {
            if (el->adapter->module->type & type) {
                utarray_push_back(array, &el->adapter);
            }
        }
    }

    return array;
}
#endif


/*
* 查找 指定mid 设备的名称
*/
char * neu_device_manager_name_by_mid(neu_subDevice_manager_t *mgr, int mid)
{
    device_entity_t *el = NULL, *tmp = NULL;

    HASH_ITER(hh, mgr->devices, el, tmp)
    {
        if(el->mid == mid){
            return strdup(el->name);
        }
    }

    return NULL;
}


/*
* 查找 指定mid 设备的设备类型deviceType
*/
int neu_device_manager_deviceType_by_mid(neu_subDevice_manager_t *mgr, int mid)
{
    device_entity_t *el = NULL, *tmp = NULL;
    if(mgr == NULL || mid <= 0){
        nlog_debug("parameter error.");
        return -1;
    }

    HASH_ITER(hh, mgr->devices, el, tmp)
    {
        if(el->mid == mid){
            return el->deviceType;
        }
    }

    return -1;
}

/*
* 查找 指定mid 设备的设备基本信息
*/
manager_dev_info_t  * neu_device_manager_deviceInfo_by_mid(neu_subDevice_manager_t *mgr, int mid)
{
    device_entity_t *el = NULL, *tmp = NULL;
    if(mgr == NULL || mid <= 0){
        nlog_debug("parameter error.");
        return NULL;
    }
    HASH_ITER(hh, mgr->devices, el, tmp)
    {
        if(mid == el->mid)    //类型匹配
        {
            manager_dev_info_t  *dev = (manager_dev_info_t  *)calloc(1,sizeof(manager_dev_info_t));
            dev->name       = strdup(el->name);
            dev->mid        = el->mid;
            dev->online     = el->online; //0-offline   1-online
            dev->pair_flag  = el->pair_flag;      //是否正在配对
            dev->deviceType = el->deviceType;     //设备类型
            memcpy(dev->eui, el->eui,EUI_ADR_LEN);
            dev->pid        = el->pid;
            dev->link       = el->link;
            dev->subType    = el->subType;
            nlog_debug("get devInfo by mid:%d success.",mid);
            return dev;             
        }
    }

    nlog_debug("error: device manager not found mid:%d dev info !!!",mid);
    return NULL;
}


/*
*  通过名称查找设备 Mid
*/
int neu_device_manager_mid_by_name(neu_subDevice_manager_t *mgr, char * name)
{
    if(mgr == NULL || name == NULL) return -1;
    device_entity_t *node = NULL;

    HASH_FIND_STR(mgr->devices, name, node);
    if (NULL == node) {
        return -1;
    }

    return node->mid;
}

/*
* 查找指定类型deviceType 的未配对的设备
*/
manager_dev_info_t  *neu_device_manager_pair_dev_by_deviceType(neu_subDevice_manager_t *mgr, uint8_t deviceType)
{
    if(mgr == NULL || deviceType < 0){
        nlog_debug("parameter error.");
        return NULL;
    }

    device_entity_t *el = NULL, *tmp = NULL;

    HASH_ITER(hh, mgr->devices, el, tmp)
    {
        if(deviceType == el->deviceType)    //类型匹配
        {
             //if (strstr(el->eui,"00000000000")){    //eui 地址空
                manager_dev_info_t  *dev = (manager_dev_info_t  *)calloc(1,sizeof(manager_dev_info_t));
                dev->name       = strdup(el->name);
                dev->mid        = el->mid;
                dev->online     = el->online;; //0-offline   1-online
                dev->pair_flag  = el->pair_flag;      //是否正在配对
                dev->deviceType = el->deviceType;;     //设备类型
                dev->pid        = el->pid;
                dev->link       = el->link;
                dev->subType    = el->subType;
                nlog_debug("get pair dev by devicetype:%d success.",deviceType);
                return dev;
             //}
        }
    }

    nlog_debug("error: device manager not found devicetype:%d dev info !!!",deviceType);
    return NULL;
}


/*
* 通过eui 查找模块名
*/
char  * neu_device_manager_get_name_by_eui(neu_subDevice_manager_t *mgr, char *eui)
{
    if(mgr == NULL || eui == NULL){
        nlog_debug("parameter error.");
        return NULL;
    }

    device_entity_t *el = NULL, *tmp = NULL;

    HASH_ITER(hh, mgr->devices, el, tmp)
    {
        if(strcmp(eui,el->eui) == 0){
            return el->name;
        }
    }

    nlog_debug("error: device manager not found eui:%s dev info !!!",eui);
    return NULL;
}
