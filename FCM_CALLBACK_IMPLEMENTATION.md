# FCM 设备消息处理函数 - 回调函数实现

## 概述

根据您的要求，我已经修改了 `fcm_update_tag_by_address` 函数，使其通过 adapter 回调函数来获取组信息，而不是直接调用 `neu_adapter_driver_get_group`。这样做遵循了与 `get_dev_base` 相同的设计模式，保持了插件层和适配器层之间的正确抽象。

## 主要修改

### 1. 扩展 adapter_callbacks_t 结构

在 `neuron_sdk/neuron-2.11.2/include/neuron/adapter.h` 中添加了新的回调函数：

```c
typedef struct adapter_callbacks {
    // ... 现有的回调函数 ...
    void * (*get_dev_base)(neu_adapter_t *adapter);
    UT_array * (*get_groups)(neu_adapter_t *adapter);  // 新增的回调函数
    // ...
} adapter_callbacks_t;
```

### 2. 实现 adapter_get_groups 回调函数

在 `neuron_sdk/neuron-2.11.2/src/adapter/adapter.c` 中实现：

```c
static UT_array *adapter_get_groups(neu_adapter_t *adapter)
{
    if(adapter == NULL) {
        return NULL;
    }
    
    if(adapter->module->type == NEU_NA_TYPE_DRIVER) {
        return acme_get_group((neu_adapter_driver_t *)adapter);
    }
    
    return NULL;
}
```

### 3. 注册回调函数

在 adapter 初始化时注册新的回调函数：

```c
static const adapter_callbacks_t callback_funs = {
    // ... 现有的回调函数 ...
    .get_dev_base    = adapter_get_dev_base,
    .get_groups      = adapter_get_groups,  // 新增
};
```

### 4. 修改 FCM 点位更新函数

在 `app/acme_driver/acme_lora/business_logic/product/business_fcm.c` 中修改：

```c
static int fcm_update_tag_by_address(neu_plugin_t *plugin, neu_adapter_driver_t *driver, 
                                     const char *address, neu_type_e tag_type, neu_value_u value)
{
    // ... 参数检查 ...
    
    // 通过 adapter 回调函数获取所有 groups（而不是直接调用 neu_adapter_driver_get_group）
    neu_adapter_t *adapter = (neu_adapter_t *)driver;
    UT_array *groups = plugin->common.adapter_callbacks->get_groups(adapter);
    
    // ... 其余逻辑保持不变 ...
}
```

## 设计优势

### 1. 遵循现有模式
- 与 `get_dev_base` 回调函数保持一致的设计模式
- 维护了插件层和适配器层之间的正确抽象

### 2. 解耦合
- 插件不再直接调用适配器内部函数
- 通过回调函数接口进行交互，提高了模块间的独立性

### 3. 可测试性
- 可以轻松模拟回调函数进行单元测试
- 便于验证不同场景下的行为

### 4. 扩展性
- 未来可以轻松添加更多的回调函数
- 支持不同类型的适配器实现

## 使用流程

```
1. LoRa 设备数据接收
   ↓
2. business_lora_dev_message_handle()
   ↓
3. fcm_dev_message_handle()
   ↓
4. fcm_process_io_status_data()
   ↓
5. fcm_update_tag_values()
   ↓
6. fcm_update_tag_by_address() (循环调用)
   ↓
7. plugin->common.adapter_callbacks->get_groups(adapter)  // 通过回调获取组信息
   ↓
8. 遍历 groups 和 tags
   ↓
9. address 字段匹配
   ↓
10. fcm_update_tag_value_direct()
    ↓
11. 更新点位值
```

## Address 映射关系

| Address | 点位功能 | 数据类型 | 说明 |
|---------|----------|----------|------|
| "1" | ONOFF | INT32 | 开关状态 |
| "2" | STEMP | FLOAT | 设定温度 |
| "3" | SMODE | INT32 | 运行模式 |
| "4" | WSPED | INT32 | 风速等级 |
| "5" | RTEMP | FLOAT | 读取温度 |
| "6" | ERROC | INT32 | 错误码 |
| "7" | HUMID | INT32 | 湿度 |

## 测试验证

创建了 `test_fcm_callback_update.c` 测试程序，验证了：

1. **正常功能**：通过回调函数成功获取组信息并更新点位
2. **错误处理**：处理不存在的 address 地址
3. **回调机制**：验证回调函数被正确调用
4. **数据解析**：验证空调数据的正确解析和映射

## 关键改进点

### 之前的实现问题：
```c
// 直接调用适配器内部函数（不推荐）
UT_array *groups = neu_adapter_driver_get_group(driver);
```

### 修改后的实现：
```c
// 通过回调函数获取（推荐）
neu_adapter_t *adapter = (neu_adapter_t *)driver;
UT_array *groups = plugin->common.adapter_callbacks->get_groups(adapter);
```

## 注意事项

1. **内存管理**：确保正确释放 `utarray_free(groups)` 和 `utarray_free(tags)`
2. **错误处理**：检查回调函数返回值，处理 NULL 情况
3. **线程安全**：回调函数内部需要考虑线程安全问题
4. **向后兼容**：新增的回调函数不影响现有功能

## 总结

通过这次修改，我们成功地：

1. ✅ **遵循了设计模式**：使用与 `get_dev_base` 相同的回调机制
2. ✅ **保持了抽象层次**：插件层通过回调函数访问适配器功能
3. ✅ **提高了可维护性**：清晰的接口定义和职责分离
4. ✅ **增强了可测试性**：可以轻松模拟和测试回调函数
5. ✅ **实现了功能需求**：通过 address 字段匹配更新点位值

这个实现完全满足了您的要求：**在 LoRa 插件内不能直接调用 `neu_adapter_driver_get_group`，必须通过 adapter 回调函数获取组信息**。
