/**
 * NEURON IIoT System for Industry 4.0
 * Copyright (C) 2020-2022 EMQ Technologies Co., Ltd All rights reserved.
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 3 of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with this program; if not, write to the Free Software Foundation,
 * Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.
 **/

/*
 * DO NOT EDIT THIS FILE MANUALLY!
 * It was automatically generated by `json-autotype`.
 */

#include <stdlib.h>
#include <string.h>

#include "json/json.h"

#include "neu_json_system.h"

void neu_json_decode_system_ctl_req_free(neu_json_system_ctl_req_t *req)
{
    if (req != NULL) {
        free(req);
    }
}

int neu_json_decode_system_ctl_req(char *                      buf,
                                   neu_json_system_ctl_req_t **result)
{
    int   ret      = 0;
    void *json_obj = neu_json_decode_new(buf);

    neu_json_system_ctl_req_t *req =
        calloc(1, sizeof(neu_json_system_ctl_req_t));
    if (req == NULL) {
        return -1;
    }

    neu_json_elem_t req_elems[] = {
        {
            .name = "action",
            .t    = NEU_JSON_INT,
        },
    };

    ret = neu_json_decode_by_json(json_obj, NEU_JSON_ELEM_SIZE(req_elems),
                                  req_elems);
    if (ret != 0) {
        free(req);
        if (json_obj != NULL) {
            neu_json_decode_free(json_obj);
        }
        return -1;
    }

    req->action = req_elems[0].v.val_int;
    *result     = req;

    if (json_obj != NULL) {
        neu_json_decode_free(json_obj);
    }
    return ret;
}